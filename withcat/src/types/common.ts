// 通用类型定义

export interface BigNumberLike {
  toString(): string;
  toNumber(): number;
  times(value: any): BigNumberLike;
  plus(value: any): BigNumberLike;
  minus(value: any): BigNumberLike;
  dividedBy(value: any): BigNumberLike;
  pow(value: any): BigNumberLike;
}

export interface HexString {
  toString(): string;
}

export type NetworkType = 'mainnet' | 'testnet' | 'devnet';

export interface ChainConfig {
  chainId: number;
  name: string;
  rpcUrl: string;
  blockExplorer?: string;
  nativeCurrency?: {
    name: string;
    symbol: string;
    decimals: number;
  };
}

export interface SDKError extends Error {
  code: string;
  details?: any;
}
