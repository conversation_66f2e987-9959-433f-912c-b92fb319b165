// 钱包相关类型定义

export interface WalletInfo {
  privateKey: string;
  publicKey: string;
  address: string;
}

export interface HDWalletParams {
  seedHex: string;
  addressIndex: string;
}

export interface WalletSDKConfig {
  chainId: number;
  rpcUrl?: string;
  network?: 'mainnet' | 'testnet';
}

export interface WalletSDK {
  createAddress(params: HDWalletParams): WalletInfo;
  publicKeyToAddress(publicKey: string): string;
  verifyAddress(address: string): boolean;
}

// dot
export interface dotInfo {
  privateKey: string;
  publicKey: string;
  address: string;
}
