// 交易相关类型定义

export interface ETHTransactionParams {
  privateKey: string;
  nonce: number;
  from: string;
  to: string;
  gasLimit: number;
  amount: string;
  gasPrice: number;
  decimal: number;
  chainId: any;
  tokenAddress: string;
  callData: string;
  maxPriorityFeePerGas?: number;
  maxFeePerGas?: number;
}

export interface EIP1559TransactionParams
  extends Omit<ETHTransactionParams, 'gasPrice'> {
  maxPriorityFeePerGas: number;
  maxFeePerGas: number;
}

export interface LegacyTransactionParams extends ETHTransactionParams {
  gasPrice: number;
}

export interface TokenTransferParams {
  tokenAddress: string;
  to: string;
  amount: string;
  decimal: number;
}

export interface TransactionResult {
  signedTx: string;
  txHash?: string;
}

export type TransactionType = 'legacy' | 'eip1559';
