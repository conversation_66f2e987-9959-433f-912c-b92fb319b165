import { BIP32Factory } from 'bip32';
import * as ecc from 'tiny-secp256k1';
import * as bitcoin from 'bitcoinjs-lib';
const cashaddr = require('cashaddrjs');
const bitcore = require('bitcore-lib');

import { toXOnly } from 'bitcoinjs-lib/src/psbt/bip371';

const bip32 = BIP32Factory(ecc);

// 定义类型
interface BtcAddressInfo {
  privateKey: string;
  publicKey: string;
  address: string;
}

interface TransactionInput {
  address: string;
  txid: string;
  amount: number;
  vout: number;
  index?: number;
  output?: string;
  publicKey: string;
}

interface TransactionOutput {
  address: string;
  amount: number;
}

interface SignObj {
  inputs: TransactionInput[];
  outputs: TransactionOutput[];
}

// 定义映射
const purposeMap: { [key: string]: number } = {
  p2pkh: 44,
  p2sh: 49,
  p2wpkh: 84,
  p2tr: 86,
};

const bipNumberMap: { [key: string]: number } = {
  btc: 0,
  ltc: 2,
  doge: 3,
  bch: 145,
  bsv: 236,
};

// 获取链配置
function getChainConfig(chain: string) {
  switch (chain) {
    case 'btc':
      return bitcoin.networks.bitcoin;
    case 'ltc':
      return {
        messagePrefix: '\x19Litecoin Signed Message:\n',
        bech32: 'ltc',
        bip32: {
          public: 0x019da462,
          private: 0x019d9cfe,
        },
        pubKeyHash: 0x30,
        scriptHash: 0x32,
        wif: 0xb0,
      };
    case 'doge':
      return {
        messagePrefix: '\x19Dogecoin Signed Message:\n',
        bech32: 'doge',
        bip32: {
          public: 0x02facafd,
          private: 0x02fac398,
        },
        pubKeyHash: 0x1e,
        scriptHash: 0x16,
        wif: 0x9e,
      };
    case 'bch':
      return {
        messagePrefix: '\x18Bitcoin Cash Signed Message:\n',
        bech32: 'bc',
        bip32: {
          public: 0x0488b21e,
          private: 0x0488ade4,
        },
        pubKeyHash: 0x00,
        scriptHash: 0x05,
        wif: 0x80,
      };
    case 'bsv':
      return {
        messagePrefix: '\x18Bitcoin SV Signed Message:\n',
        bech32: 'bsv',
        bip32: {
          public: 0x0488b21e,
          private: 0x0488ade4,
        },
        pubKeyHash: 0x00,
        scriptHash: 0x05,
        wif: 0x80,
      };
    default:
      throw new Error(`Unsupported chain: ${chain}`);
  }
}

// 离线地址生成
export function createUtxoAddress(
  seedHex: string,
  receiveOrChange: '0' | '1',
  addressIndex: string,
  chain: string,
  typeAddress: string = 'p2pkh'
): BtcAddressInfo {
  const root = bip32.fromSeed(Buffer.from(seedHex, 'hex'));
  const purpose = purposeMap[typeAddress];
  const bipNum = bipNumberMap[chain];
  let path = `m/${purpose}'/${bipNum}'/0'/${receiveOrChange}/${addressIndex}`;
  let child = root.derivePath(path);
  if (!child.privateKey) {
    throw new Error('Private key is undefined');
  }
  let address: any;
  let utxoNetwork = getChainConfig(chain);
  switch (typeAddress) {
    case 'p2pkh': // 支持所有格式的地址生成
      const p2pkhAddress = bitcoin.payments.p2pkh({
        pubkey: Buffer.from(child.publicKey),
        network: utxoNetwork,
      });
      if (chain === 'bch') {
        address = cashaddr.encode('bitcoincash', 'P2PKH', p2pkhAddress.hash);
      } else {
        address = p2pkhAddress.address;
      }
      break;

    case 'p2wpkh': // 支持 BTC 和 LTC；不支持 Doge, BCH 和 BSV
      if (chain === 'doge' || chain === 'bch' || chain === 'bsv') {
        throw new Error('Do not support this chain');
      }
      const p2wpkhAddress = bitcoin.payments.p2wpkh({
        pubkey: Buffer.from(child.publicKey),
        network: utxoNetwork,
      });
      address = p2wpkhAddress.address;
      break;
    case 'p2sh': // 支持 BTC, LTC 和 Doge; 不支持 BCH
      if (chain === 'bch') {
        throw new Error('Do not support this chain');
      }
      const p2shAddress = bitcoin.payments.p2sh({
        redeem: bitcoin.payments.p2wpkh({
          pubkey: Buffer.from(child.publicKey),
          network: utxoNetwork,
        }),
      });
      address = p2shAddress.address;
      break;
    case 'p2tr': // 仅仅支持 BTC; 其他格式的地址不支持
      if (chain !== 'btc') {
        throw new Error('Only bitcoin support p2tr format address');
      }
      child = root.derivePath(path);
      if (!child.privateKey) {
        throw new Error('Private key is undefined');
      }
      const p2trAddress = bitcoin.payments.p2tr({
        internalPubkey: Buffer.from(child.publicKey!.slice(1, 33)), // Slice to extract x-only pubkey
        network: bitcoin.networks.bitcoin,
      });
      address = p2trAddress.address;
      break;
    default:
      throw new Error('This way can not support');
  }
  if (!address) {
    throw new Error('Address generation failed');
  }
  return {
    privateKey: Buffer.from(child.privateKey).toString('hex'),
    publicKey: Buffer.from(child.publicKey).toString('hex'),
    address,
  };
}

// 离线签名
export function signUtxoTransaction(params: {
  privateKey: string;
  signObj: SignObj;
  network: string;
}): string {
  const { privateKey, signObj, network } = params;
  const net = bitcore.Networks[network];
  const inputs = signObj.inputs.map((input) => {
    return {
      address: input.address,
      txId: input.txid,
      outputIndex: input.vout,
      script: new bitcore.Script.fromAddress(input.address).toHex(),
      satoshis: input.amount,
    };
  });
  const outputs = signObj.outputs.map((output) => {
    return {
      address: output.address,
      satoshis: output.amount,
    };
  });
  const transaction = new bitcore.Transaction(net).from(inputs).to(outputs);
  transaction.version = 2;
  transaction.sign(privateKey);
  return transaction.toString();
}

// 离线 taproot 签名
export async function signBtcTaprootTransaction(params: {
  signObj: SignObj;
  privateKey: string;
}) {
  const { signObj, privateKey } = params;
  const psbt = new bitcoin.Psbt({ network: bitcoin.networks.bitcoin });

  const inputs = signObj.inputs.map((input) => {
    return {
      hash: input.txid,
      index: input.index || 0,
      witnessUtxo: {
        value: input.amount,
        script: Buffer.from(input.output!, 'hex'),
      },
      tapInternalKey: toXOnly(Buffer.from(input.publicKey, 'hex')),
    };
  });
  psbt.addInputs(inputs);

  const sendInternalKey = bip32.fromPrivateKey(
    Buffer.from(privateKey, 'hex'),
    Buffer.from('0')
  );

  const outputs = signObj.outputs.map((output) => {
    return {
      value: output.amount,
      address: output.address,
    };
  });

  psbt.addOutputs(outputs);

  const tweakedSigner = sendInternalKey.tweak(
    bitcoin.crypto.taggedHash(
      'TapTweak',
      // @ts-ignore
      toXOnly(sendInternalKey.publicKey) as any
    )
  );

  await psbt.signInputAsync(0, tweakedSigner as any);
  psbt.finalizeAllInputs();
  const tx = psbt.extractTransaction();
  return tx.toBuffer().toString('hex');
}
