import { ethers } from 'ethers';
import { Interface } from '@ethersproject/abi';
import BigNumber from 'bignumber.js';
// 使用 ethers 签名交易 和 @ethereumjs/tx 签名交易 两者之一即可
import { FeeMarketEIP1559Transaction, Transaction } from '@ethereumjs/tx';
import Common from '@ethereumjs/common';
import type { ETHTransactionParams } from '../../types/transaction';
import type { WalletInfo, HDWalletParams } from '../../types/wallet';

export function numberToHex(value: any) {
  const number = new BigNumber(value);
  const result = number.toString(16);
  return '0x' + result;
}

// ETH SDK 支持的 同源链
const SUPPER_CHAIN_NETWORKS = {
  1: 'Ethereum',
  324: 'ZksyncEra',
  42161: 'Arbitrum',
  42170: 'ArbitrumNova',
  5000: 'Mantle',
  56: 'Bsc<PERSON>hain',
  128: 'He<PERSON>',
  137: 'Polygon',
  10001: 'EthereumPow',
  61: 'EthereumClassic',
  8217: 'klay',
  1101: 'PolygonZk',
  66: 'OkexChain',
  9001: 'Evmos',
  10: 'Optimism',
  59144: 'Linea',
  8453: 'Base',
  17000: 'Holesky',
  11155111: 'Sepolia',
} as const;

type ChainId = keyof typeof SUPPER_CHAIN_NETWORKS;

// 创建地址
export function createAddress(params: HDWalletParams): WalletInfo {
  const { seedHex, addressIndex } = params;
  const rootNode = ethers.utils.HDNode.fromSeed(Buffer.from(seedHex, 'hex'));

  const { privateKey, publicKey, address } = rootNode.derivePath(
    `m/44'/60'/0'/0/${addressIndex}`
  );
  return {
    privateKey,
    publicKey,
    address,
  };
}

// 导入钱包
export function importEthWallet(privateKey: string) {
  // 将私钥转换为 Buffer，ethers.Wallet 的构造函数需要一个 Buffer 类型的私钥
  const wallet = new ethers.Wallet(Buffer.from(privateKey, 'hex'));
  return JSON.stringify({
    privateKey,
    address: wallet.address,
  });
}

// 公钥生成地址
export function publicKeyToAddress(publicKey: string) {
  return ethers.utils.computeAddress(publicKey);
}

// 验证地址
export function verifyAddress(address: string) {
  return ethers.utils.isAddress(address);
}

// 使用  @ethereumjs/tx 签名交易
export function signTransaction(params: ETHTransactionParams): string {
  let {
    privateKey,
    nonce,
    from,
    to,
    gasPrice,
    gasLimit,
    amount,
    tokenAddress,
    callData,
    decimal,
    maxPriorityFeePerGas,
    maxFeePerGas,
    chainId,
  } = params;

  const transactionNonce = numberToHex(nonce);
  const gasLimits = numberToHex(gasLimit);
  const chainIdHex = numberToHex(chainId);
  let newAmount = new BigNumber(amount).times(new BigNumber(10).pow(decimal));
  const numBalanceHex = numberToHex(newAmount);
  let txData: any = {
    nonce: transactionNonce,
    gasLimit: gasLimits,
    to,
    from,
    chainId: chainIdHex,
    value: numBalanceHex,
  };
  if (maxFeePerGas && maxPriorityFeePerGas) {
    txData.maxFeePerGas = numberToHex(maxFeePerGas);
    txData.maxPriorityFeePerGas = numberToHex(maxPriorityFeePerGas);
  } else {
    txData.gasPrice = numberToHex(gasPrice);
  }
  // 如果是 ERC20 token 交易，则需要设置 tokenAddress 和 callData
  if (tokenAddress && tokenAddress !== '0x00') {
    const ABI = ['function transfer(address to, uint amount)'];
    const iface = new Interface(ABI);
    if (params.callData) {
      txData.data = callData;
      txData.value = '0x0';
    } else {
      txData.data = iface.encodeFunctionData('transfer', [to, numBalanceHex]);
      txData.to = tokenAddress;
    }
    txData.value = '0x0';
  }
  let common: any, tx: any;
  // 使用 EIP-1559 签名交易
  if (txData.maxFeePerGas && txData.maxPriorityFeePerGas) {
    common = (Common as any).custom({
      chainId: chainId,
      defaultHardfork: 'london',
    });
    tx = FeeMarketEIP1559Transaction.fromTxData(txData, {
      common,
    });
  } else {
    // 使用 Legacy 签名交易
    common = (Common as any).custom({ chainId: chainId });
    tx = Transaction.fromTxData(txData, {
      common,
    });
  }
  const privateKeyBuffer = Buffer.from(privateKey, 'hex');
  const signedTx = tx.sign(privateKeyBuffer);
  const serializedTx = signedTx.serialize();
  if (!serializedTx) {
    throw new Error('sign is null or undefined');
  }
  return `0x${serializedTx.toString('hex')}`;
}

// 使用 ethers 完成签名
export function signTransactionWithEthers(params: ETHTransactionParams) {
  let {
    privateKey,
    nonce,
    from,
    to,
    gasPrice,
    gasLimit,
    amount,
    tokenAddress,
    callData,
    decimal,
    maxPriorityFeePerGas,
    maxFeePerGas,
    chainId,
  } = params;

  if (!SUPPER_CHAIN_NETWORKS[chainId as ChainId]) {
    throw new Error(`Unsupported chainId: ${chainId}`);
  }

  const wallet = new ethers.Wallet(Buffer.from(privateKey, 'hex'));

  const txData: any = {
    nonce,
    from,
    to,
    value: ethers.utils.parseUnits(amount, decimal),
    gasLimit,
    chainId,
  };

  if (maxFeePerGas && maxPriorityFeePerGas) {
    // 对于 EIP-1559 交易，使用 ethers 的 UnsignedTransaction 格式
    txData.maxFeePerGas = ethers.BigNumber.from(maxFeePerGas);
    txData.maxPriorityFeePerGas = ethers.BigNumber.from(maxPriorityFeePerGas);
    // 设置交易类型为 EIP-1559
    txData.type = 2;
  } else {
    txData.gasPrice = ethers.BigNumber.from(gasPrice || '0');
  }
  // 如果 tokenAddress 存在且不为 0x00，则表示这是一笔代币交易
  if (tokenAddress && tokenAddress !== '0x00') {
    const ABI = [
      'function transfer(address to, uint256 amount) returns (bool)',
    ];
    // 将 ABI 转换为 ethers 的格式
    const iface = new Interface(ABI);

    if (params.callData) {
      txData.data = params.callData;
    } else {
      // 将 amount 转换为十六进制
      txData.data = iface.encodeFunctionData('transfer', [
        to,
        ethers.utils.parseUnits(amount, decimal).toHexString(),
      ]);
      txData.to = tokenAddress;
    }

    txData.value = '0x0';
  }

  if (callData) {
    txData.data = callData;
  }
  // 使用 ethers 签名交易
  return wallet.signTransaction(txData);
}
