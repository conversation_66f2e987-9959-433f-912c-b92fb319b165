import { ethers } from 'ethers';
import { Interface } from '@ethersproject/abi';
import type { ETHTransactionParams } from '../../types/transaction';
import type { WalletInfo, HDWalletParams } from '../../types/wallet';

// ETH SDK 支持的 同源链
const SUPPER_CHAIN_NETWORKS = {
  1: 'Ethereum',
  324: 'ZksyncEra',
  42161: 'Arbitrum',
  42170: 'ArbitrumNova',
  5000: 'Mantle',
  56: 'BscChain',
  128: 'Heco',
  137: 'Polygon',
  10001: 'EthereumPow',
  61: 'EthereumClassic',
  8217: 'klay',
  1101: 'PolygonZk',
  66: 'OkexChain',
  9001: 'Evmos',
  10: 'Optimism',
  59144: 'Linea',
  8453: 'Base',
  17000: 'Holesky',
  11155111: 'Sepolia',
} as const;

type ChainId = keyof typeof SUPPER_CHAIN_NETWORKS;

// 创建地址
export function createAddress(params: HDWalletParams): WalletInfo {
  const { seedHex, addressIndex } = params;
  const rootNode = ethers.utils.HDNode.fromSeed(Buffer.from(seedHex, 'hex'));

  const { privateKey, publicKey, address } = rootNode.derivePath(
    `m/44'/60'/0'/0/${addressIndex}`
  );
  return {
    privateKey,
    publicKey,
    address,
  };
}

// 使用 ethers 完成签名
export function signTransactionWithEthers(params: ETHTransactionParams) {
  let {
    privateKey,
    nonce,
    from,
    to,
    gasPrice,
    gasLimit,
    amount,
    tokenAddress,
    callData,
    decimal,
    maxPriorityFeePerGas,
    maxFeePerGas,
    chainId,
  } = params;

  if (!SUPPER_CHAIN_NETWORKS[chainId as ChainId]) {
    throw new Error(`Unsupported chainId: ${chainId}`);
  }

  const wallet = new ethers.Wallet(Buffer.from(privateKey, 'hex'));

  const txData: any = {
    nonce,
    from,
    to,
    value: ethers.utils.parseUnits(amount, decimal),
    gasLimit,
    chainId,
  };

  if (maxFeePerGas && maxPriorityFeePerGas) {
    // 对于 EIP-1559 交易，使用 ethers 的 UnsignedTransaction 格式
    txData.maxFeePerGas = ethers.BigNumber.from(maxFeePerGas);
    txData.maxPriorityFeePerGas = ethers.BigNumber.from(maxPriorityFeePerGas);
    // 设置交易类型为 EIP-1559
    txData.type = 2;
  } else {
    txData.gasPrice = ethers.BigNumber.from(gasPrice || '0');
  }
  // 如果 tokenAddress 存在且不为 0x00，则表示这是一笔代币交易
  if (tokenAddress && tokenAddress !== '0x00') {
    const ABI = [
      'function transfer(address to, uint256 amount) returns (bool)',
    ];
    // 将 ABI 转换为 ethers 的格式
    const iface = new Interface(ABI);

    if (params.callData) {
      txData.data = params.callData;
    } else {
      // 将 amount 转换为十六进制
      txData.data = iface.encodeFunctionData('transfer', [
        to,
        ethers.utils.parseUnits(amount, decimal).toHexString(),
      ]);
      txData.to = tokenAddress;
    }

    txData.value = '0x0';
  }

  if (callData) {
    txData.data = callData;
  }
  // 使用 ethers 签名交易
  return wallet.signTransaction(txData);
}
