import {
  cryptoWaitReady,
  mnemonicToMiniSecret,
  sr25519PairFromSeed,
  blake2AsHex,
  // mnemonicGenerate,
} from '@polkadot/util-crypto';
import { Keyring } from '@polkadot/keyring';
import { ApiPromise, WsProvider } from '@polkadot/api';

import type { dotInfo } from '../../types/wallet';

interface DotSignerPayloadJSON {
  address: string;
  amount: string; // 使用字符串以避免大数精度问题
  recipient: string;
  tip?: string;
}

export async function createAddress(): Promise<dotInfo> {
  await cryptoWaitReady();

  // const mnemonic = mnemonicGenerate();
  // console.log('==mnemonic=>', mnemonic);
  const mnemonic =
    'shield velvet agent idea slim barrel unlock sunny blanket ecology flock business';

  // const mnemonic =
  //   'suffer boil broom slam long quick credit metal bunker raven sort team';
  const keyring = new Keyring({ type: 'sr25519', ss58Format: 0 }); // Polkadot chain
  const sp = keyring.createFromUri(mnemonic);

  // 生成私钥
  const miniSecret = mnemonicToMiniSecret(mnemonic);
  const keypair = sr25519PairFromSeed(miniSecret);

  return {
    privateKey: Buffer.from(keypair.secretKey).toString('hex'),
    publicKey: Buffer.from(sp.publicKey).toString('hex'),
    address: sp.address,
  };

  /***
   * privateKey: 'a8468c48243fea240406a4a49741f9376a70aeb08f8cd8bbff2bc298c498626285aa00ec48b343763fbfa99f5f5a98d9d3195f75f227648dc2bda5a2cd1a1679'
   * publicKey: 'ae840ff411aab72776d8775a41946f04212988ef5b99e45baeba2e911a35ac40'
   * address: '************************************************'
   * mnemonic: 'shield velvet agent idea slim barrel unlock sunny blanket ecology flock business'
   */
}

export async function signDotTransaction(params: {
  mnemonic: string; // 明确传入的是 64 字节的十六进制私钥，可以带 0x 前缀
  signObj: DotSignerPayloadJSON;
}) {
  await cryptoWaitReady();
  const {
    mnemonic,
    signObj: { address, amount, recipient, tip = '0' },
  } = params;

  const wsProvider = new WsProvider('wss://rpc.polkadot.io');
  const api = await ApiPromise.create({ provider: wsProvider });

  // 创建一个转账交易对象 (Extrinsic)
  const transfer = api.tx.balances.transferAllowDeath(recipient, amount);

  // 获取构建签名负载所需的所有链上信息
  const lastHeader = await api.rpc.chain.getHeader();
  // const blockNumber = lastHeader.number.toNumber();
  const nonce = await api.query.system
    .account(address)
    .then((res) => (res as any).nonce.toNumber());
  const { specVersion, transactionVersion } = api.runtimeVersion;
  const genesisHash = api.genesisHash.toHex();

  // 创建签名负载
  const payloadObject = {
    specVersion: specVersion.toHex(),
    transactionVersion: transactionVersion.toHex(),
    address,
    blockHash: genesisHash, // 对于“永生交易”，blockHash 使用 genesisHash
    genesisHash: genesisHash,
    era: '0x00', // '0x00' 代表 ImmortalEra (永生交易)
    method: transfer.method.toHex(),
    nonce: nonce,
    tip: api.createType('Compact<Balance>', tip).toHex(), // Tip 也需要正确编码
  };

  // createSigningPayload 会返回一个 SignerPayload 对象
  const payload = api.createType('SignerPayload', payloadObject, {
    version: 4,
  });

  const keyring = new Keyring({ type: 'sr25519', ss58Format: 0 });

  const keypair = keyring.addFromUri(mnemonic);

  // 确保私钥和地址匹配
  if (keypair.address !== address) {
    console.log('===>', keypair.address, address);
    throw new Error('提供的私钥与付款人地址不匹配。');
  }

  // toPayload() 返回一个适合签名的 JSON 对象，toU8a() 返回字节数组
  const dataToSign = payload.toU8a({ method: true });

  // 如果负载数据超过 256 字节，Polkadot 规定只签名其哈希值
  const dataToSignHashed =
    dataToSign.length > 256 ? blake2AsHex(dataToSign) : dataToSign;

  // 将签名添加回原始的、完整的交易对象
  const signature = keypair.sign(dataToSignHashed, { withType: true });

  // 返回结果
  return {
    signedTx: transfer.toHex(), // 完整的、可广播的交易
    signature: Buffer.from(signature).toString('hex'),
    payload: payload.toHex(),
  };
}
