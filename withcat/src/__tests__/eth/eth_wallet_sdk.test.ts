import { generateMnemonic, mnemonicToSeed } from '../../wallet/bip/bip';
import {
  createAddress,
  importEthWallet,
  signTransaction,
  signTransactionWithEthers,
} from '../../wallet/eth/eth_wallet_sdk';

const privateKey =
  'cadf7450f8a7f15b5a3c9eb094b7de3fcef85465ee3de3d3a3bb687f31c79289';
// const sepoliaUrl = 'https://ethereum-sepolia-rpc.publicnode.com';
describe('eth wallet test', () => {
  it('test createAddress', () => {
    const mnemonic = generateMnemonic({ number: 12, language: 'english' });
    const params = {
      mnemonic: mnemonic,
      password: '',
    };
    const seed = mnemonicToSeed(params);
    const account = createAddress({
      seedHex: seed.toString('hex'),
      addressIndex: '0',
    });
    console.log(account);
  });

  it('test importEthWallet', () => {
    const wallet = importEthWallet(
      'cadf7450f8a7f15b5a3c9eb094b7de3fcef85465ee3de3d3a3bb687f31c79289'
    );
    console.log(wallet);
  });

  // 测试签名转 ETH  - Legacy
  it('test signTransaction', () => {
    const params = {
      privateKey,
      nonce: 34,
      from: '******************************************',
      to: '******************************************',
      gasLimit: 21000,
      amount: '0.1',
      gasPrice: ***********,
      decimal: 18,
      chainId: ********,
      tokenAddress: '0x00',
      callData: '',
    };

    const signTransactionResult = signTransaction(params);
    console.log(signTransactionResult);
  });

  // 测试签名 ERC20 token 交易 EIP-1559
  it('test signTransaction ERC20', async () => {
    const params = {
      privateKey,
      nonce: 34,
      from: '******************************************',
      to: '******************************************',
      gasLimit: 91000,
      amount: '0.1',
      gasPrice: 0,
      decimal: 18,
      chainId: ********,
      maxFeePerGas: 327993150328,
      maxPriorityFeePerGas: 32799315032,
      tokenAddress: '******************************************',
      callData: '',
    };

    const signTransactionResult = await signTransactionWithEthers(params);
    console.log(signTransactionResult);
  });
});
