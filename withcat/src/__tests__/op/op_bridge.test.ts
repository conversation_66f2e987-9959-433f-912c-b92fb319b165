import { ethers } from 'ethers';
import { signTransactionWithEthers } from '../../wallet/op/op_sdk';

const privateKey =
  'cadf7450f8a7f15b5a3c9eb094b7de3fcef85465ee3de3d3a3bb687f31c79289';
const fromAddress = '******************************************';

// const l1TokenAddress = '******************************************';
// const l2TokenAddress = '******************************************';

describe('op bridge test', () => {
  it('test ETH signTransaction', async () => {
    const sepoliaProvider = new ethers.providers.JsonRpcProvider(
      'https://ethereum-sepolia-rpc.publicnode.com'
    );

    const wallet = new ethers.Wallet(privateKey);
    // 获取实时Nonce
    const nonce = await sepoliaProvider.getTransactionCount(
      fromAddress,
      'pending'
    );

    // 获取实时Gas费用建议 (EIP-1559)
    const feeData = await sepoliaProvider.getFeeData();
    if (!feeData.maxFeePerGas || !feeData.maxPriorityFeePerGas) {
      throw new Error('Could not fetch EIP-1559 fee data.');
    }

    // 跨链桥合约地址 L1
    const L1_BRIDGE_ADDRESS = '******************************************';

    const l1BridgeInterface = new ethers.utils.Interface([
      'function depositETH(uint32 _l2Gas, bytes calldata _extraData)',
    ]);
    const callDataForBridge = l1BridgeInterface.encodeFunctionData(
      'depositETH',
      [200000, '0x']
    );

    // 签名参数
    const params = {
      privateKey,
      nonce,
      from: fromAddress,
      to: L1_BRIDGE_ADDRESS, // 目标是跨链桥合约
      gasLimit: 500000, // 设置一个更充裕的Gas Limit
      gasPrice: 0,
      amount: '0.1', // 您想跨链的ETH数量，例如 '0.01'
      decimal: 18,
      maxFeePerGas: feeData.maxFeePerGas.toNumber(),
      maxPriorityFeePerGas: feeData.maxPriorityFeePerGas.toNumber(),
      chainId: 11155111,
      tokenAddress: '0x00', // 不是代币交易
      callData: callDataForBridge, // 传入编码好的函数调用数据
    };
    const signedRawTx = await signTransactionWithEthers(params);
    console.log(signedRawTx);
  });
});
