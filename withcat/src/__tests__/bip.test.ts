import { generateMnemonic, mnemonicToSeed } from '../wallet/bip/bip';
describe('eth wallet test', () => {
  it('test createAddress', () => {
    const mnemonic = generateMnemonic({ number: 12, language: 'english' });
    const params = {
      mnemonic: mnemonic,
      password: '',
    };
    const seed = mnemonicToSeed(params);
    console.log('test bip ==> ', mnemonic, seed.toString('hex'));
  });
});
