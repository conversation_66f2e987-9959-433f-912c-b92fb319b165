import { generateMnemonic, mnemonicToSeed } from '../../wallet/bip/bip';
import {
  createUtxoAddress,
  signUtxoTransaction,
  signBtcTaprootTransaction,
} from '../../wallet/btc/btc_wallet_sdk';

describe('btc wallet sdk', () => {
  it('test createUtxoAddress', () => {
    // const mnemonic = generateMnemonic({ number: 12, language: 'english' });
    const mnemonic =
      'exile pottery skate turn leg person shield remove exclude inform gospel actual';
    const params = {
      mnemonic: mnemonic,
      password: '',
    };
    console.log('===>', mnemonic);
    const seed = mnemonicToSeed(params);
    const address = createUtxoAddress(
      seed.toString('hex'),
      '0',
      '0',
      'btc',
      'p2wpkh'
    );

    console.log('===>', address);

    // p2pkh = **********************************  **********************************
    // p2sh  = **********************************  **********************************
    // p2wpkh = ******************************************  ******************************************
  });

  test('sign', async () => {
    const data = {
      outputs: [
        {
          amount: 3000,
          address: '******************************************',
        },
        {
          amount: 1000,
          address: '******************************************',
        },
      ],
      inputs: [
        {
          address: '******************************************',
          txid: 'b00771c6acc9d84e503edb1cab32325dee4d261762e84d23fb11fab26143ff18',
          vout: 1,
          amount: 5000,
          publicKey:
            '0276a74f96481911d19d18648f5905bf3fd6ee658c3ba4d1f1bc3cb1bb9b49b7f9',
        },
      ],
    };

    var ss1 = signUtxoTransaction({
      privateKey:
        'a45e1e7303bae8397499594963802b89dccc1c1c8891ab5cd1515bc7e90cde6e',
      signObj: data,
      network: 'mainnet',
    });

    console.log(ss1);
  });
});
