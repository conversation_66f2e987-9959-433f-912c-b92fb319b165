import {
  createAddress,
  signDotTransaction,
} from '../../wallet/dot/dot_wallet_sdk';

describe('dot wallet sdk', () => {
  it('test createAddress', async () => {
    const dotInfo = await createAddress();
    console.log('===>', dotInfo);
  });

  // 5C8dva241FhpFFkK8CTr7LWQPWaBmNKtjBsN42V696VuoZF6
  // suffer boil broom slam long quick credit metal bunker raven sort team

  /***
   * privateKey: 'a8468c48243fea240406a4a49741f9376a70aeb08f8cd8bbff2bc298c498626285aa00ec48b343763fbfa99f5f5a98d9d3195f75f227648dc2bda5a2cd1a1679'
   * publicKey: 'ae840ff411aab72776d8775a41946f04212988ef5b99e45baeba2e911a35ac40'
   * address: '************************************************'
   * mnemonic: 'shield velvet agent idea slim barrel unlock sunny blanket ecology flock business'
   */

  /**
   * recipient-privateKey: '50f6e75d7d6b85967d326d830e40d36b52b5f7ae16f1dace037c1eff7703e051e84e26886bca06d2ae40eb2521ca4a54fbbadc1fc66084904cff001e4250c2aa',
   * recipient-publicKey: '181b32e1c174efd4bcba1428b860afb030c27fedf12eb8538c689b481991d634',
   * recipient-address: '********************************MMDBZfdZT96ujXG',
   * recipient-mnemonic: 'harbor inner hammer love ordinary try pattern ozone profit oppose swing nuclear',
   */

  it('test signDotTransaction', async () => {
    // 这是一个有效的测试网密钥对，但请不要在主网使用
    const mnemonic =
      'shield velvet agent idea slim barrel unlock sunny blanket ecology flock business';
    const address = '************************************************'; // Alice 的地址

    const signObj = {
      address: address,
      recipient: '********************************MMDBZfdZT96ujXG',
      amount: '***********', // 0.01 WND (Westend 测试网)
    };

    // 注意：为了让测试在本地运行，可能需要 mock WsProvider 和 ApiPromise
    // 但函数本身的逻辑现在是正确的
    const res = await signDotTransaction({ mnemonic, signObj });
    console.log('===>', res);

    expect(res.signedTx).toBeDefined();
    expect(res.signature).toBeDefined();
  });
});
