{"name": "wallet-sdk", "version": "1.0.0", "type": "module", "description": "A modern wallet SDK for Web3 applications", "main": "dist/index.cjs", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}, "./umd": "./dist/index.umd.js"}, "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build && npm run test"}, "keywords": ["wallet", "web3", "sdk", "blockchain", "crypto"], "author": "withcat", "license": "MIT", "devDependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-typescript": "^11.1.6", "@types/jest": "^29.5.8", "@types/node": "^20.8.10", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "eslint": "^8.54.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "rollup": "^4.6.1", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^29.1.1", "tslib": "^2.8.1", "typescript": "^5.2.2"}, "dependencies": {"@ethereumjs/common": "2.4.0", "@ethereumjs/tx": "3.2.1", "@ethersproject/abi": "5.4.0", "@polkadot/api": "^16.4.1", "@polkadot/keyring": "^13.5.3", "@polkadot/util-crypto": "^13.5.3", "bignumber.js": "9.0.1", "bip32": "^5.0.0-rc.0", "bip39": "^3.0.4", "bitcoinjs-lib": "^6.1.7", "bitcore-lib": "^10.10.5", "cashaddrjs": "^0.4.4", "ethers": "^5.7.2", "tiny-secp256k1": "^2.2.4"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "**************:web3-workspace/wallet-sdk.git"}, "bugs": {"url": "https://github.com/web3-workspace/wallet-sdk/issues"}, "homepage": "https://github.com/web3-workspace/wallet-sdk", "packageManager": "pnpm@9.15.0+sha512.76e2379760a4328ec4415815bcd6628dee727af3779aaa4c914e3944156c4299921a89f976381ee107d41f12cfa4b66681ca9c718f0668fa0831ed4c6d8ba56c"}