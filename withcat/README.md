# DOT 链调研

## 链的基本原理

Polkadot 的核心目标与 Cosmos 类似，都是为了解决“链与链之间无法互通”的问题，但它的实现方式非常独特。您可以把 Polkadot 想象成一个高度协同的、共享安全性的“插座”和“插头”系统。

- 中继链 (Relay Chain)：这是 Polkadot 的核心主干，可以看作是一个拥有大量插座的高级电源板。中继链本身不处理复杂的智能合约或应用逻辑，它的主要职责只有三件：

  - 协调所有连接到它的其他链。

  - 保证所有连接到它的链的最终安全性。

  - 处理它们之间的消息和资产传递。

- 平行链 (Parachains)：这些是可以插入到中继链“插座”上的、各自独立的区块链。每条平行链都可以是高度定制化的，专为某个特定领域服务（如 DeFi、游戏、身份认证等）。

  - 平行链自己负责处理自己的业务逻辑和出块，但它们将最终的安全性外包给了中 relay 链。

  - 比喻：每条平行链就像一个需要供电的高级电器（比如一台服务器、一个游戏机）。它自己负责运行内部程序，但它的电源线必须插在中继链这个总电源板上，由总电源板来确保供电的稳定和安全。

- (Parathreads)：与平行链类似，但它们是按需付费的，不需要长期租用一个“插槽”。适合那些交易量不大的项目。

核心原理总结：Polkadot 通过一个中心化的中继链，为许多异构的（结构、功能各不相同的）平行链提供统一的安全保障和跨链互操作性。这种模式被称为**“共享安全性” (Shared Security)**。

### 共识算法

Polkadot 创造性地使用了一种混合共识算法，将出块和最终性确认分开，以实现效率和安全的平衡。

- BABE (Blind Assignment for Blockchain Extension)：负责出块。

  - BABE 是一种基于 Ouroboros 的出块机制。它会通过一个随机算法，在每个时间点（slot）“盲选”出一位或多位验证人来生成新的区块。

  - 这个过程非常快，可以持续不断地产生新的区块，保证了链的“活性”。

  - 比喻：BABE 就像是工厂里不断生产产品的流水线工人，他们负责快速地把产品造出来。

- GRANDPA (GHOST-based Recursive Ancestor Deriving Prefix Agreement)：负责最终性确认。

  - GRANDPA 是一个最终性确认工具。它不直接参与出块，而是对由 BABE 生成的链进行投票。

- 当超过三分之二的验证人对包含某个区块的链达成共识时，这个区块以及它之前的所有区块就被认为是最终确定的，不可逆转。

- 这个过程可以一次性确认一大批区块，效率很高。

- 比喻：GRANDPA 像是工厂里的“质检总监”。他不会去生产线上干活，而是会定期检查一大批已经生产出来的产品，一旦他盖上“合格”的章，这批产品就谁也不能退回了。

总结：BABE 保证链一直在增长，GRANDPA 保证链的增长是安全且不可篡改的。

支持 staking

### 账户模型

账户模型

### 单链和多链

Polkadot 是一个典型的异构多链 (Heterogeneous Multi-chain) 架构。
一条中继链和多条（最多约 100 条）平行链/平行线程组成。

### 代币精度

decimals = 10
Polkadot uses 10 decimals

### 密码学算法

ed25519

```
{
  "prefix": 0,
  "network": "polkadot",
  "displayName": "Polkadot Relay Chain",
  "symbols": ["DOT"],
  "decimals": [10],
  "standardAccount": "*25519",
  "website": "https://polkadot.network"
},
```

### 账户体系(离线地址生成)

生成助记词：https://polkadot.js.org/docs/util-crypto/examples/create-mnemonic
验证地址：https://polkadot.js.org/docs/util-crypto/examples/validate-address

Polkadot 及其生态中的所有链，都使用一种名为 SS58 的地址格式。这种格式的特点是，同一个公钥可以根据一个网络前缀 (ss58Format) 编码成不同样式的地址。

- Polkadot 主网 的前缀是 0，生成的地址以 1 开头。

- Kusama 网络 的前缀是 2，生成的地址以大写字母开头（如 F、H 等）。

- 通用的 Substrate 链 的默认前缀是 42，生成的地址以 5 开头。

```
export async function createAddress(): Promise<dotInfo> {
  await cryptoWaitReady();

  // const mnemonic = mnemonicGenerate();
  // console.log('==mnemonic=>', mnemonic);
  const mnemonic =
    'shield velvet agent idea slim barrel unlock sunny blanket ecology flock business';

  // const mnemonic =
  //   'suffer boil broom slam long quick credit metal bunker raven sort team';
  const keyring = new Keyring({ type: 'sr25519', ss58Format: 0 }); // Polkadot chain
  const sp = keyring.createFromUri(mnemonic);

  // 生成私钥
  const miniSecret = mnemonicToMiniSecret(mnemonic);
  const keypair = sr25519PairFromSeed(miniSecret);

  return {
    privateKey: Buffer.from(keypair.secretKey).toString('hex'),
    publicKey: Buffer.from(sp.publicKey).toString('hex'),
    address: sp.address,
  };
```

### 交易构建体系(离线签名)

https://github.com/polkadot-js/api/blob/master/packages/types/src/types/extrinsic.ts

```
export async function signDotTransaction(params: {
  mnemonic: string; // 明确传入的是 64 字节的十六进制私钥，可以带 0x 前缀
  signObj: DotSignerPayloadJSON;
}) {
  await cryptoWaitReady();
  const {
    mnemonic,
    signObj: { address, amount, recipient, tip = '0' },
  } = params;

  const wsProvider = new WsProvider('wss://rpc.polkadot.io');
  const api = await ApiPromise.create({ provider: wsProvider });

  // 创建一个转账交易对象 (Extrinsic)
  const transfer = api.tx.balances.transferAllowDeath(recipient, amount);

  // 获取构建签名负载所需的所有链上信息
  const lastHeader = await api.rpc.chain.getHeader();
  // const blockNumber = lastHeader.number.toNumber();
  const nonce = await api.query.system
    .account(address)
    .then((res) => (res as any).nonce.toNumber());
  const { specVersion, transactionVersion } = api.runtimeVersion;
  const genesisHash = api.genesisHash.toHex();

  // 创建签名负载
  const payloadObject = {
    specVersion: specVersion.toHex(),
    transactionVersion: transactionVersion.toHex(),
    address,
    blockHash: genesisHash, // 对于“永生交易”，blockHash 使用 genesisHash
    genesisHash: genesisHash,
    era: '0x00', // '0x00' 代表 ImmortalEra (永生交易)
    method: transfer.method.toHex(),
    nonce: nonce,
    tip: api.createType('Compact<Balance>', tip).toHex(), // Tip 也需要正确编码
  };

  // createSigningPayload 会返回一个 SignerPayload 对象
  const payload = api.createType('SignerPayload', payloadObject, {
    version: 4,
  });

  const keyring = new Keyring({ type: 'sr25519', ss58Format: 0 });

  const keypair = keyring.addFromUri(mnemonic);

  // 确保私钥和地址匹配
  if (keypair.address !== address) {
    console.log('===>', keypair.address, address);
    throw new Error('提供的私钥与付款人地址不匹配。');
  }

  // toPayload() 返回一个适合签名的 JSON 对象，toU8a() 返回字节数组
  const dataToSign = payload.toU8a({ method: true });

  // 如果负载数据超过 256 字节，Polkadot 规定只签名其哈希值
  const dataToSignHashed =
    dataToSign.length > 256 ? blake2AsHex(dataToSign) : dataToSign;

  // 将签名添加回原始的、完整的交易对象
  const signature = keypair.sign(dataToSignHashed, { withType: true });

  // 返回结果
  return {
    signedTx: transfer.toHex(), // 完整的、可广播的交易
    signature: Buffer.from(signature).toString('hex'),
    payload: payload.toHex(),
  };
}
```

### 是否 Token, 是否 NFT

### RPC 的调用

https://polkadot.js.org/docs/

参考文档：
CMC: https://coinmarketcap.com/currencies/polkadot-new/
polkadotJS SDK: https://polkadot.js.org/docs/
gitHub: https://github.com/paritytech/polkadot-sdk
浏览器：https://polkadot.subscan.io/
