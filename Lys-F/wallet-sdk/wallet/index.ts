import {Interface} from '@ethersproject/abi';
import {FeeMarketEIP1559Transaction, Transaction} from '@ethereumjs/tx'
import Common from '@ethereumjs/common'
import * as TinySecp256k1 from 'tiny-secp256k1';
import { BIP32Factory } from 'bip32';
const bip32 = BIP32Factory(TinySecp256k1);
const ethers = require('ethers');
const BigNumber = require('bignumber.js');
const bitcore = require('bitcore-lib');
const bitcoin = require('bitcoinjs-lib');
const cashaddr = require('cashaddress');
import * as ecc from 'tiny-secp256k1';

bitcoin.initEccLib(ecc);

export function numberToHex(value: any) {
    const number = BigNumber(value);
    const result = number.toString(16);
    return '0x' + result;
}

export function createAddress (seedHex: string, addressIndex: string) {
    const hdNode = ethers.utils.HDNode.fromSeed(Buffer.from(seedHex, 'hex'));
    const {
        privateKey,
        publicKey,
        address
    } = hdNode.derivePath("m/44'/60'/0'/0/" + addressIndex + '');
    return JSON.stringify({
        privateKey,
        publicKey,
        address
    });
}

export function signTransaction(params: { privateKey: string; nonce: number; from: string; to: string; gasLimit: number; amount: string; gasPrice: number; decimal: number; chainId: any; tokenAddress: string; callData: string;  maxPriorityFeePerGas?: number; maxFeePerGas?: number; }) {
    let { privateKey, nonce, from, to, gasPrice, gasLimit, amount, tokenAddress, callData,  decimal, maxPriorityFeePerGas, maxFeePerGas, chainId } = params;
    const transactionNonce = numberToHex(nonce);
    const gasLimits = numberToHex(gasLimit);
    const chainIdHex = numberToHex(chainId);
    let newAmount = BigNumber(amount).times((BigNumber(10).pow(decimal)));
    const numBalanceHex = numberToHex(newAmount);
    let txData: any = {
        nonce: transactionNonce,
        gasLimit: gasLimits,
        to,
        from,
        chainId: chainIdHex,
        value: numBalanceHex
    }
    if (maxFeePerGas && maxPriorityFeePerGas) {
        txData.maxFeePerGas = numberToHex(maxFeePerGas);
        txData.maxPriorityFeePerGas = numberToHex(maxPriorityFeePerGas);
    } else {
        txData.gasPrice = numberToHex(gasPrice);
    }
    if (tokenAddress && tokenAddress !== "0x00") {
        const ABI = ["function transfer(address to, uint amount)"];
        const iface = new Interface(ABI);
        if (params.callData) {
          txData.data = callData;        
          txData.value = "0x0";          
        } else {
          txData.data = iface.encodeFunctionData("transfer", [to, numBalanceHex]);
          txData.to = tokenAddress;      
        }
        txData.value = "0x0";           
    }
    let common: any, tx: any;
    if (txData.maxFeePerGas && txData.maxPriorityFeePerGas) {
        common = (Common as any).custom({
            chainId: chainId,
            defaultHardfork: "london"
        });
        tx = FeeMarketEIP1559Transaction.fromTxData(txData, {
            common
        });
    } else {
        common = (Common as any).custom({ chainId: chainId })
        tx = Transaction.fromTxData(txData, {
            common
        });
    }
    console.log("privatekey lenth:",privateKey.length);
    const privateKeyBuffer = Buffer.from(privateKey, "hex");
    console.log("privateKeyBuffer lenth:",privateKeyBuffer.length);
    const signedTx = tx.sign(privateKeyBuffer);
    const serializedTx = signedTx.serialize();
    if (!serializedTx) {
        throw new Error("sign is null or undefined");
    }
    return `0x${serializedTx.toString('hex')}`;
}


export function verifyAddress (params: any) {
    const { address } = params;
    return ethers.utils.isAddress(address);
}

export function importAddress (privateKey: string) {
    const wallet = new ethers.Wallet(Buffer.from(privateKey, 'hex'));
    return JSON.stringify({
        privateKey,
        address: wallet.address
    });
}

export function signTransactionFor(params: {
    privateKey: string;
    nonce: number;
    from: string;
    to: string;
    gasLimit: number;
    amount: string;
    gasPrice: number;
    decimal: number;
    chainId: number;
    tokenAddress: string;
    callData: string;
    maxPriorityFeePerGas?: number;
    maxFeePerGas?: number;
}) {
    let { privateKey, nonce, from, to, gasPrice, gasLimit, amount, tokenAddress, callData, decimal, maxPriorityFeePerGas, maxFeePerGas, chainId } = params;
    const transactionNonce = numberToHex(nonce);
    const gasLimits = numberToHex(gasLimit);
    const chainIdHex = numberToHex(chainId);
    let newAmount = BigNumber(amount).times(BigNumber(10).pow(decimal));
    const numBalanceHex = numberToHex(newAmount);
    let txData: any = {
        nonce: transactionNonce,
        gasLimit: gasLimits,
        to,
        from,
        chainId: chainIdHex,
        value: numBalanceHex,
        data: callData
    };

    if (maxFeePerGas && maxPriorityFeePerGas) {
        txData.maxFeePerGas = numberToHex(maxFeePerGas);
        txData.maxPriorityFeePerGas = numberToHex(maxPriorityFeePerGas);
    } else {
        txData.gasPrice = numberToHex(gasPrice);
    }

    let common: any, tx: any;
    if (txData.maxFeePerGas && txData.maxPriorityFeePerGas) {
        common = Common.custom({ chainId, defaultHardfork: "london" });
        tx = FeeMarketEIP1559Transaction.fromTxData(txData, { common });
    } else {
        common = Common.custom({ chainId });
        tx = Transaction.fromTxData(txData, { common });
    }

    const privateKeyBuffer = Buffer.from(privateKey, "hex");
    const signedTx = tx.sign(privateKeyBuffer);
    const serializedTx = signedTx.serialize();
    if (!serializedTx) throw new Error("sign is null or undefined");
    return `0x${serializedTx.toString("hex")}`;
}

type SignObj = {
    inputs: Array<{
        address: string;
        txid: string;
        vout: number;
        amount: number;
    }>;
    outputs: Array<{
        address: string;
        amount: number;
    }>;
};

export function signUtxoTransaction(params: { privateKey: string; signObj: SignObj; network: string; }): string {
    const { privateKey, signObj, network } = params;
    const net = bitcore.Networks[network];
    const inputs = signObj.inputs.map(input => {
        return {
            address: input.address,
            txId: input.txid,
            outputIndex: input.vout,
            script: new bitcore.Script.fromAddress(input.address).toHex(),
            satoshis: input.amount
        }
    });
    const outputs = signObj.outputs.map(output => {
        return {
            address: output.address,
            satoshis: output.amount
        };
    });
    const transaction = new bitcore.Transaction(net).from(inputs).to(outputs);
    transaction.version = 2;
    transaction.sign(privateKey);
    return transaction.toString();
}

export async function signBtcTaprootTransaction(params: { signObj: any; privateKey: any; }) {
    const { signObj, privateKey } = params
    const psbt = new bitcoin.Psbt({network: bitcoin.networks.bitcoin});

    const inputs = signObj.inputs.map((input: { txid: any; index: any; amount: any; output: any; publicKey: string | Buffer | Uint8Array; }) => {
        let pubkey = input.publicKey;
        if (typeof pubkey === 'string') {
            pubkey = Buffer.from(pubkey, 'hex');
        }
        return {
            hash: input.txid,
            index: input.index,
            witnessUtxo: {value: input.amount, script: input.output!},
            tapInternalKey: toXOnly(pubkey),
        }
    });
    psbt.addInputs(inputs);

    // 这里需要privateKey和chainCode，实际使用时请确保传入正确的chainCode
    // 这里只是示例，chainCode应从HDNode或bip32节点获取
    const dummyChainCode = Buffer.alloc(32, 0); // 仅示例
    const sendInternalKey = bip32.fromPrivateKey(privateKey, dummyChainCode);

    const output = signObj.output.map((output: { value: any; sendAddress: any; sendPubKey: any; }) => {
        return {
            value: output.value,
            address: output.sendAddress!,
            tapInternalKey: Buffer.isBuffer(output.sendPubKey) ? output.sendPubKey : Buffer.from(output.sendPubKey),
        }
    });

    psbt.addOutputs(output);

    const tweakedSigner = sendInternalKey.tweak(
        bitcoin.crypto.taggedHash('TapTweak', toXOnly(sendInternalKey.publicKey)),
    );

    await psbt.signInputAsync(0, tweakedSigner);
    psbt.finalizeAllInputs();
    const tx = psbt.extractTransaction();
    return tx.toBuffer().toString('hex');
}

function toXOnly(pubkey: Buffer | Uint8Array): Buffer | Uint8Array {
    if (Buffer.isBuffer(pubkey)) {
        return pubkey.length === 33 ? pubkey.slice(1, 33) : pubkey;
    } else {
        return pubkey.length === 33 ? pubkey.subarray(1, 33) : pubkey;
    }
}

export interface BtcAddressInfo {
    privateKey: string;
    publicKey: string;
    address: string;
}

const purposeMap: Record<string, number> = {
    p2pkh: 44,
    p2sh: 49,
    p2wpkh: 84,
    p2tr: 86
};

const bipNumberMap: { [key: string]: number } = {
    btc: 0,
    ltc: 2,
    doge: 3,
    bch: 145,
    bsv: 236, // 新增BSV
    // 可根据需要补充其他币种
};

// BCH cashaddress类型映射
const cashaddrTypeMap: Record<string, string> = {
    p2pkh: 'pubkeyhash',
    p2sh: 'scripthash'
};

export function createUtxoAddress(
    seedHex: string,
    receiveOrChange: "0" | "1",
    addressIndex: string,
    chain: string,
    typeAddress: string = 'p2pkh'
): BtcAddressInfo {
    const purpose = purposeMap[typeAddress];
    const bipNum = bipNumberMap[chain];
    if (purpose === undefined || bipNum === undefined) {
        throw new Error('Unsupported typeAddress or chain');
    }
    const root = bip32.fromSeed(Buffer.from(seedHex, 'hex'));
    let path = `m/${purpose}'/${bipNum}'/0'/${receiveOrChange}/${addressIndex}`;
    let child = root.derivePath(path);
    if (!child.privateKey) {
        throw new Error('Private key is undefined');
    }
    let address: any;

    function getChainConfig(chain: string) {
        switch (chain) {
            case 'btc':
                return bitcoin.networks.bitcoin;
            case 'ltc':
                // LTC 的 network 配置
                return {
                    messagePrefix: '\x19Litecoin Signed Message:\n',
                    bech32: 'ltc',
                    bip32: {
                        public: 0x019da462,
                        private: 0x019d9cfe,
                    },
                    pubKeyHash: 0x30,
                    scriptHash: 0x32,
                    wif: 0xb0,
                };
            case 'doge':
                // DOGE 的 network 配置
                return {
                    messagePrefix: '\x19Dogecoin Signed Message:\n',
                    bech32: null,
                    bip32: {
                        public: 0x02facafd,
                        private: 0x02fac398,
                    },
                    pubKeyHash: 0x1e,
                    scriptHash: 0x16,
                    wif: 0x9e,
                };
            case 'bch':
                // BCH 的 network 配置
                return {
                    messagePrefix: '\x18Bitcoin Cash Signed Message:\n',
                    bech32: 'bc', // BCH 没有 bech32 地址，但有些库需要
                    bip32: {
                        public: 0x0488b21e,
                        private: 0x0488ade4,
                    },
                    pubKeyHash: 0x00,
                    scriptHash: 0x05,
                    wif: 0x80,
                };
            case 'bsv':
                // BSV 的 network 配置
                return {
                    messagePrefix: '\x18Bitcoin SV Signed Message:\n',
                    bech32: null,
                    bip32: {
                        public: 0x0488b21e,
                        private: 0x0488ade4,
                    },
                    pubKeyHash: 0x00,
                    scriptHash: 0x05,
                    wif: 0x80,
                };
            default:
                throw new Error('Unsupported chain');
        }
    }

    let utxoNetwork = getChainConfig(chain);
    switch (typeAddress) {
        case 'p2pkh': // 支持所有格式的地址生成
            const p2pkhAddress = bitcoin.payments.p2pkh({
                pubkey: Buffer.from(child.publicKey),
                network: utxoNetwork
            });
            if (chain === "bch") {
                address = cashaddr.encode('bitcoincash', cashaddrTypeMap[typeAddress] || 'pubkeyhash', Buffer.from(p2pkhAddress.hash));
            } else {
                address = p2pkhAddress.address;
            }
            break;
        case 'p2wpkh': // 支持 BTC 和 LTC；不支持 Doge, BCH 和 BSV
            if (chain === "doge" || chain === "bch" || chain === "bsv") {
                throw new Error('Do not support this chain');
            }
            const p2wpkhAddress = bitcoin.payments.p2wpkh({
                pubkey: Buffer.from(child.publicKey),
                network: utxoNetwork
            });
            address = p2wpkhAddress.address;
            break;
        case 'p2sh': // 支持 BTC, LTC 和 Doge; 不支持 BCH
            if (chain === "bch") {
                throw new Error('Do not support this chain');
            }
            const p2shAddress = bitcoin.payments.p2sh({
                redeem: bitcoin.payments.p2wpkh({
                    pubkey: Buffer.from(child.publicKey),
                    network: utxoNetwork
                })
            });
            address = p2shAddress.address;
            break;
        case 'p2tr': // 仅仅支持 BTC; 其他格式的地址不支持
            if (chain !== "btc") {
                throw new Error('Only bitcoin support p2tr format address');
            }
            if (!child.privateKey) {
                throw new Error('Private key is undefined');
            }
            const p2trAddress = bitcoin.payments.p2tr({
                internalPubkey: Buffer.from(child.publicKey).slice(1, 33), // Slice to extract x-only pubkey
                network: bitcoin.networks.bitcoin
            });
            address = p2trAddress.address;
            break;
        default:
            throw new Error('This way can not support');
    }
    if (!address) {
        throw new Error('Address generation failed');
    }
    return {
        privateKey: Buffer.from(child.privateKey).toString('hex'),
        publicKey: Buffer.from(child.publicKey).toString('hex'),
        address
    };
}