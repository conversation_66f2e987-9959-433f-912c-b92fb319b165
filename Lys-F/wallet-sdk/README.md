1. Op
 ERC20 代币跨链 
 充值成功 
 console.log
   📨 Sent! Tx Hash: 0x7f8cb1891ec71da1f00220bed0b9b431ba610b450cbf0548c913ec243b89906c
 1.部署L1 原生ERC20 代币 授权L1合约额度
 2.按Op要求实现接口 L2 token 部署,部署时传入L1 TOKEN 地址和L2 合约地址，部署完成 授权L2合约额度。
 3.充值L1 TOKEN 成功，但是不知道为啥内部合约没有发起跨链消息到L2 上 后续流程阻塞无法进行。
    L1 ERC20代币成功减少被锁定到L1 合约里,但是 L2 合约里查不到ERC20代币 可能跟测试网有关系。
2. 测试离线地址生成和 Bitcoin 离线签名，
   btc address: {
   privateKey: '10e40fcf040c222609e21bc3005287d5b4ace441b9feee7ead18b794255c9fb9',
   publicKey: '0255b72bc52dfa0ffc40742b1a3eb01858714341c1f72bc1f8fdc731098323e96e',
   address: '**********************************'
   }

    ltc address: {
    privateKey: '4239ba537308e21e0935e019bcc7b1affdc874b4491cc44a4f8d7e3d0c426a04',
    publicKey: '02d07bfc1d322c75d8bb562322a25c811a5e5c885648ef96311052c79b4ba4fc67',
    address: 'MKfWdS8K94TQgJip7mp8tPzJEFKr18YYBV'
    }

   DOGE address: {
   privateKey: '6f2efe28f38e28a6380921a28afb8233343a0aa702a581d1f65b7b3552365d1f',
   publicKey: '0216eee30bdd974cffa795ae6248a24ccc466f49b539018a335f5801965414d84e',
   address: 'DKg3pNmz1QqfyL71xTp7g8kTxxRLehxm6T'
   }
   console.log
   bch address: {
   privateKey: '58d0314a3ba66ca662dc42c9c8cdf5f63bd2def4f29d631f092ef63a63d2f60e',
   publicKey: '029ccc150fbff353dbccf93f7091517a200d9a9bd91b57ffdae5cce604cabec5e0',
   address: 'bitcoincash:qrlum98rwhfqpzvmv4ae8jgahvqrdln5xvv0ezp7dd'
   }

   console.log
   BSV address: {
   privateKey: '7225ad002883faca3ccc2c506981f366d8577fbc2618ffa6989584d41b2ee48f',
   publicKey: '027b09574ab94255db2fd8ea2c34d3ab4d7a6fb93401b3d6aa2be6c140fc763518',
   address: '**********************************'
   }
3. TapRoot地址
   console.log
   btc TR address: {
   privateKey: '17464389333790f220dfbbc5c39cec928578a4275f4dba3e059db0f71c77673e',
   publicKey: '03d848c164c1668c0d1c8a22fbfc3ddbbfee9980c1c68df8ce23c71162c6b1f990',
   address: '**************************************************************'
   }
4. 找一个 bitcoin rosetta api 节点，尝试调用
   https://nd-202-842-353.p2pify.com/788f110831fe13808302bd79796d55e8
request:
   {
     "method": "getblock",
     "params": [
       "000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f"
     ]
   }
 response:
{
   "result": {
   "hash": "000000000019d6689c085ae165831e934ff763ae46a2a6c172b3f1b60a8ce26f",
   "confirmations": 904588,
   "height": 0,
   "version": 1,
   "versionHex": "00000001",
   "merkleroot": "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b",
   "time": 1231006505,
   "mediantime": 1231006505,
   "nonce": 2083236893,
   "bits": "1d00ffff",
   "difficulty": 1,
   "chainwork": "0000000000000000000000000000000000000000000000000000000100010001",
   "nTx": 1,
   "nextblockhash": "00000000839a8e6886ab5951d76f411475428afc90947ee320161bbf18eb6048",
   "strippedsize": 285,
   "size": 285,
   "weight": 1140,
   "tx": [
   "4a5e1e4baab89f3a32518a88c31bc87f618f76673e2cc77ab2127b7afdeda33b"
   ]
   },
   "error": null,
   "id": 0
}

