{
  "compilerOptions": {
    "resolveJsonModule": true,
    "strict": true,
    "declaration": true,
    "skipLibCheck": true,
    "baseUrl": "./",
    "paths": {
      "@/*": ["./wallet/*"]
    },
    "module": "ESNext",                // 关键：输出为 ESModule
    "target": "ES2017",                // 或更高
    "moduleResolution": "Node",        // Node 风格模块解析
    "esModuleInterop": true,           // 允许默认导入
    "allowSyntheticDefaultImports": true, // 允许默认导入
    "outDir": "./dist",                // 输出目录
    "strict": true
  },
  "include": ["wallet/**/*"],
  "exclude": ["node_modules", "dist"]
}

