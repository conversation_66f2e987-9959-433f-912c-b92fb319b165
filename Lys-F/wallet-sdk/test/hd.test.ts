import { generateMnemonic, mnemonicToSeed } from "../wallet/bip/bip";
import {createAddress, signTransaction, importAddress, signUtxoTransaction, createUtxoAddress} from "../wallet";
const ethers = require("ethers");
const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessageStatus } = require('@eth-optimism/sdk');
const provider = new ethers.providers.JsonRpcProvider(
  "https://endpoints.omniatech.io/v1/op/sepolia/public"
);

describe("eth unit test case", () => {
  // Test address creation
  test("createAddress", () => {
    const mnemonic = generateMnemonic({ number: 12, language: "english" });
    const params = {
      mnemonic: mnemonic,
      password: "",
    };
    const seed = mnemonicToSeed(params);
    const account = createAddress(seed.toString("hex"), "0");
    console.log(account);
  });

  // Test address import
  test("importAddress", () => {
    const account = importAddress("YOUR_PRIVATE_KEY");
    console.log(account);
  });

  // Test Token Transfer
  test("sign and broadcast token transfer", async () => {
    const privateKey = "0x90dfa4925f6bcb8ff1eb50edafde10b5fb0720cb0d4d2a0e70a202ee62a0909f";
    const wallet = new ethers.Wallet(privateKey, provider);
    
    // Transfer parameters
    const transferParams = {
      tokenAddress: "******************************************",  // USDT contract address
      to: "******************************************",          // Recipient address
      amount: "1",                                                 // Transfer amount
      decimal: 6                                                   // USDT decimals
    };

    // Get current gas prices
    const feeData = await provider.getFeeData();
    console.log("Current gas prices:", {
      maxFeePerGas: ethers.utils.formatUnits(feeData.maxFeePerGas || 0, "gwei"),
      maxPriorityFeePerGas: ethers.utils.formatUnits(feeData.maxPriorityFeePerGas || 0, "gwei"),
    });

    // Get current nonce
    const nonce = await provider.getTransactionCount(wallet.address);
    console.log("Current nonce:", nonce);

    // Sign transaction
    const rawHex = signTransaction({
      privateKey: privateKey,
      nonce: Number(nonce),
      from: wallet.address,
      to: transferParams.to,
      gasLimit: 100000,
      maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
      maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
      gasPrice: 0,
      amount: transferParams.amount,
      decimal: transferParams.decimal,
      chainId: 11155420,
      tokenAddress: transferParams.tokenAddress,
      callData: "",
    });

    // Broadcast transaction
    const tx = await provider.sendTransaction(rawHex);
    console.log("Transaction hash:", tx.hash);

    // Wait for confirmation
    const receipt = await tx.wait(1);
    console.log("Transaction confirmed in block:", receipt.blockNumber);
    console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
  }, 6000000);

  // Test Contract Call (Activity Creation)
  test("sign and broadcast contract call", async () => {
    const testActivity = {
      businessName: "test1",
      activityContent: '{"activityContentDescription":"test1","activityContentAddress":"test1","activityContentLink":"test1"}',
      latitude: 25.0329636,
      longitude: 121.5654268,
      activityDeadLine: **********,
      totalDropAmts: ethers.utils.parseUnits("1", 6),
      dropType: 1,
      dropNumber: 1,
      minDropAmt: ethers.utils.parseUnits("1", 6),
      maxDropAmt: ethers.utils.parseUnits("1", 6),
      tokenAddress: "******************************************",
    };

    const privateKey = "YOUR_PRIVATE_KEY";
    const wallet = new ethers.Wallet(privateKey, provider);
    const spenderAddress = "******************************************";

    // Create contract interface
    const activityInterface = new ethers.utils.Interface([
      "function activityAdd(string, string, string, uint256, uint256, uint8, uint256, uint256, uint256, address) public returns(bool, uint256)",
    ]);

    // Encode function call data
    const callData = activityInterface.encodeFunctionData("activityAdd", [
      testActivity.businessName,
      testActivity.activityContent,
      `${testActivity.latitude},${testActivity.longitude}`,
      testActivity.activityDeadLine,
      testActivity.totalDropAmts,
      testActivity.dropType,
      testActivity.dropNumber,
      testActivity.minDropAmt,
      testActivity.maxDropAmt,
      testActivity.tokenAddress,
    ]);

    // Sign transaction
    const rawHex = signTransaction({
      privateKey: privateKey,
      nonce: Number(await provider.getTransactionCount(wallet.address)),
      from: wallet.address,
      to: spenderAddress,
      gasLimit: 500000,
      maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
      maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
      gasPrice: 0,
      amount: "0",
      decimal: 6,
      chainId: 137,
      tokenAddress: testActivity.tokenAddress,
      callData: callData,
    });

    // Broadcast transaction
    const tx = await provider.sendTransaction(rawHex);
    console.log("Transaction hash:", tx.hash);

    // Wait for confirmation
    const receipt = await tx.wait(1);
    console.log("Transaction confirmed in block:", receipt.blockNumber);
    console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
  }, 60000);



  // test op bridge deposit
  test("withdraw", async () => {
    // 你的钱包私钥（测试用）
    const privateKey = "90dfa4925f6bcb8ff1eb50edafde10b5fb0720cb0d4d2a0e70a202ee62a0909f";
    const wallet = new ethers.Wallet(privateKey);
    const from = wallet.address;

    const provider = new ethers.providers.JsonRpcProvider("https://optimism-sepolia.drpc.org");

    // Optimism L1 Bridge Sepolia 地址（官方地址）
    const bridge2Address = "******************************************";
    // 构建 calldata
    const bridgeInterface = new ethers.utils.Interface([
      "function withdraw(address, uint256, uint32, bytes)"
    ]);
    const amount2 = ethers.utils.parseUnits("100000", 7);
    const calldata = bridgeInterface.encodeFunctionData("withdraw", [
      "******************************************",
      amount2,
      2000000,  // l2 gas
      "0x"      // empty data
    ]);

    const amount = "0"; // ETH
    const decimal = 18;
    const BigNumber = require('bignumber.js');
    const valueInWei = BigNumber(amount).times("1e18");

    const nonce = await provider.getTransactionCount(from, "latest");
    const feeData = await provider.getFeeData();
    const estimate = await provider.estimateGas({
      from,
      to: bridge2Address,
      value: valueInWei.toFixed(0),
      data: calldata
    });

    const signed = signTransaction({
      privateKey,
      nonce,
      from,
      to: bridge2Address,
      gasLimit: estimate.toNumber(),
      amount,
      gasPrice: 0,
      decimal,
      chainId: 11155420,
      tokenAddress: "******************************************",
      callData: calldata,
      maxPriorityFeePerGas: parseInt(feeData.maxPriorityFeePerGas!.toString()),
      maxFeePerGas: parseInt(feeData.maxFeePerGas!.toString())
    });

    console.log("📤 Broadcasting tx...");
    const tx = await provider.sendTransaction(signed);
    console.log("📨 Sent! Tx Hash:", tx.hash);

    const receipt = await tx.wait();
    console.log("✅ Transaction confirmed");
    console.log(receipt.status === 1 ? "✅ 成功" : "❌ 执行失败");
    console.log(receipt);
  })

  // test op bridge deposit
  test("deposit", async () => {
    // 你的钱包私钥（测试用）
    const privateKey = "90dfa4925f6bcb8ff1eb50edafde10b5fb0720cb0d4d2a0e70a202ee62a0909f";
    const wallet = new ethers.Wallet(privateKey);
    const from = wallet.address;

    const provider = new ethers.providers.JsonRpcProvider("https://ethereum-sepolia-rpc.publicnode.com");

    // Optimism L1 Bridge Sepolia 地址（官方地址）
    const bridgeAddress = "******************************************";
    // 构建 calldata
    const bridgeInterface = new ethers.utils.Interface([
      "function depositERC20(address _l1Token,address _l2Token,uint256 _amount, uint32 _minGasLimit, bytes calldata _extraData)"
    ]);
    const amount2 = ethers.utils.parseUnits("10", 18);
    const calldata = bridgeInterface.encodeFunctionData("depositERC20",
        [
            "******************************************",
            "******************************************",
            amount2,
            21000,
            '0x'
        ]);

    const amount = "0"; // ETH
    const decimal = 18;
    const BigNumber = require('bignumber.js');
    const valueInWei = BigNumber(amount).times("1e18");

    const nonce = await provider.getTransactionCount(from, "latest");
    const feeData = await provider.getFeeData();
    const estimate = await provider.estimateGas({
      from,
      to: bridgeAddress,
      value: valueInWei.toFixed(0),
      data: calldata
    });

    const signed = signTransaction({
      privateKey,
      nonce,
      from,
      to: bridgeAddress,
      gasLimit: estimate.toNumber(),
      amount,
      gasPrice: 0,
      decimal,
      chainId: 11155111,
      tokenAddress: "******************************************",
      callData: calldata,
      maxPriorityFeePerGas: parseInt(feeData.maxPriorityFeePerGas!.toString()),
      maxFeePerGas: parseInt(feeData.maxFeePerGas!.toString())
    });

    console.log("📤 Broadcasting tx...");
    const tx = await provider.sendTransaction(signed);
    console.log("📨 Sent! Tx Hash:", tx.hash);

    const receipt = await tx.wait();
    console.log("✅ Transaction confirmed");
    console.log(receipt.status === 1 ? "✅ 成功" : "❌ 执行失败");
    console.log(receipt);
  })

  test('should check L1->L2 message status', async function () {
    // 配置 RPC
    const l1Rpc = 'https://ethereum-sepolia-rpc.publicnode.com';
    const l2Rpc = 'https://optimism-sepolia-rpc.publicnode.com';
    const l1Provider = new ethers.providers.JsonRpcProvider(l1Rpc);
    const l2Provider = new ethers.providers.JsonRpcProvider(l2Rpc);

    // 你的 L1 交易 hash
    const l1TxHash = '0x413eb285f759a9902d4b7dcd03bf1aed1c3c905f5a93794a1dfde1649ea392ff';

    // 初始化 messenger
    const messenger = new CrossChainMessenger({
      l1ChainId: 11155111, // Sepolia
      l2ChainId: 11155420, // OP Sepolia
      l1SignerOrProvider: l1Provider,
      l2SignerOrProvider: l2Provider,
    });

    // 查询消息
    const messages = await messenger.getMessagesByTransaction(l1TxHash, { direction: 'L1ToL2' });
    if (messages.length === 0) {
      console.log('❌ 没有找到 L1->L2 消息，可能不是桥接交易或参数有误');
      return;
    }
    for (const msg of messages) {
      const status = await messenger.getMessageStatus(msg);
      let statusText = '';
      switch (status) {
        case MessageStatus.UNCONFIRMED: statusText = '未确认（UNCONFIRMED）'; break;
        case MessageStatus.FAILED: statusText = '失败（FAILED）'; break;
        case MessageStatus.STATE_ROOT_NOT_PUBLISHED: statusText = '等待状态根（STATE_ROOT_NOT_PUBLISHED）'; break;
        case MessageStatus.READY_FOR_RELAY: statusText = '可 relay（READY_FOR_RELAY）'; break;
        case MessageStatus.RELAYED: statusText = '已 relay（RELAYED）'; break;
        default: statusText = '未知状态'; break;
      }
      console.log(`消息状态: ${statusText}`);
    }
  });

  test('sign', async () => {
    const data = {
      "outputs" : [
        {
          "amount" : 3000,
          "address" : "******************************************"
        },
        {
          "amount" : 1000,
          "address" : "******************************************"
        }
      ],
      "inputs" : [
        {
          "address" : "******************************************",
          "txid" : "b00771c6acc9d84e503edb1cab32325dee4d261762e84d23fb11fab26143ff18",
          "vout" : 1,
          "amount" : 5000
        }
      ]
    };

    var ss1 = signUtxoTransaction(
        {
          privateKey: "a45e1e7303bae8397499594963802b89dccc1c1c8891ab5cd1515bc7e90cde6e",
          signObj: data,
          network: "mainnet"
        }
    );

    console.log(ss1)
  });

  // BTC
  test('createUtxoAddress returns valid BTC address info', () => {
    const seedHex = '000102030405060708090a0b0c0d0e0f000102030405060708090a0b0c0d0e0f';
    const receiveOrChange = "0";
    const addressIndex = "0";
    const chain = "btc";
    const typeAddress = "p2pkh";

    const result = createUtxoAddress(seedHex, receiveOrChange, addressIndex, chain, typeAddress);
    console.log("btc address:",result);
    expect(result).toHaveProperty('privateKey');
    expect(result).toHaveProperty('publicKey');
    expect(result).toHaveProperty('address');

  });

  // LTC
  test('createUtxoAddress returns valid LTC address info', () => {
    const seedHex = '000102030405060708090a0b0c0d0e0f000102030405060708090a0b0c0d0e0f';
    const receiveOrChange = "0";
    const addressIndex = "0";
    const chain = "ltc";
    const typeAddress = "p2sh";

    const result = createUtxoAddress(seedHex, receiveOrChange, addressIndex, chain, typeAddress);
    console.log("ltc address:",result);
    expect(result).toHaveProperty('privateKey');
    expect(result).toHaveProperty('publicKey');
    expect(result).toHaveProperty('address');

  });

  // DOGE
  test('createUtxoAddress returns valid DOGE address info', () => {
    const seedHex = '000102030405060708090a0b0c0d0e0f000102030405060708090a0b0c0d0e0f';
    const receiveOrChange = "0";
    const addressIndex = "0";
    const chain = "doge";
    const typeAddress = "p2pkh";

    const result = createUtxoAddress(seedHex, receiveOrChange, addressIndex, chain, typeAddress);
    console.log("DOGE address:",result);
    expect(result).toHaveProperty('privateKey');
    expect(result).toHaveProperty('publicKey');
    expect(result).toHaveProperty('address');

  });

  // bch
  test('createUtxoAddress returns valid bch address info', () => {
    const seedHex = '000102030405060708090a0b0c0d0e0f000102030405060708090a0b0c0d0e0f';
    const receiveOrChange = "0";
    const addressIndex = "0";
    const chain = "bch";
    const typeAddress = "p2pkh";

    const result = createUtxoAddress(seedHex, receiveOrChange, addressIndex, chain, typeAddress);
    console.log("bch address:",result);
    expect(result).toHaveProperty('privateKey');
    expect(result).toHaveProperty('publicKey');
    expect(result).toHaveProperty('address');

  });


  // BSV
  test('createUtxoAddress returns valid TR address info', () => {
    const seedHex = '000102030405060708090a0b0c0d0e0f000102030405060708090a0b0c0d0e0f';
    const receiveOrChange = "0";
    const addressIndex = "0";
    const chain = "bsv";
    const typeAddress = "p2pkh";

    const result = createUtxoAddress(seedHex, receiveOrChange, addressIndex, chain, typeAddress);
    console.log("BSV address:",result);
    expect(result).toHaveProperty('privateKey');
    expect(result).toHaveProperty('publicKey');
    expect(result).toHaveProperty('address');

  });

  // BTC TR
  test('createUtxoAddress returns valid BTC TR address info', () => {
    const seedHex = '000102030405060708090a0b0c0d0e0f000102030405060708090a0b0c0d0e0f';
    const receiveOrChange = "0";
    const addressIndex = "0";
    const chain = "btc";
    const typeAddress = "p2tr";

    const result = createUtxoAddress(seedHex, receiveOrChange, addressIndex, chain, typeAddress);
    console.log("btc TR address:",result);
    expect(result).toHaveProperty('privateKey');
    expect(result).toHaveProperty('publicKey');
    expect(result).toHaveProperty('address');

  });
});
