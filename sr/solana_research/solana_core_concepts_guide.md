# Solana 核心概念完整指南

## 概述

Solana 是一个高性能的区块链平台，具有独特的架构设计。本文档详细介绍了 Solana 的核心概念，帮助开发者理解其工作原理。

## 1. Solana 账户模型 (Account Model)

### 核心概念
- **所有数据都存储在"账户"中**：Solana 将区块链数据组织为类似键值存储的结构
- **账户地址**：每个账户都有唯一的 32 字节地址（通常显示为 base58 编码字符串）
- **最大存储容量**：每个账户最多可存储 10MiB 数据

### 账户结构
每个账户包含以下字段：
```rust
pub struct Account {
    pub lamports: u64,        // 账户余额（以 lamports 为单位）
    pub data: Vec<u8>,        // 存储的数据
    pub owner: Pubkey,        // 拥有此账户的程序 ID
    pub executable: bool,     // 是否为可执行程序
    pub rent_epoch: Epoch,    // 租金相关（已弃用）
}
```

### 账户类型

#### 1. 系统账户 (System Accounts)
- 由系统程序拥有
- 用作"钱包"账户
- 只有系统程序拥有的账户才能支付交易费用

#### 2. 程序账户 (Program Accounts)
- 存储可执行代码
- 由加载器程序拥有
- 程序 ID 用于调用程序指令

#### 3. 数据账户 (Data Accounts)
- 由自定义程序创建和管理
- 存储程序状态数据
- 每个账户有唯一地址

#### 4. Sysvar 账户
- 特殊账户，存储网络集群状态数据
- 预定义地址
- 动态更新网络信息

### 重要规则
- **程序所有权**：只有拥有账户的程序才能修改其数据或扣除余额
- **租金机制**：账户需要保持与存储数据成比例的 SOL 余额作为"租金"
- **账户创建**：只有系统程序可以创建新账户

## 2. 交易和指令 (Transactions and Instructions)

### 交易结构
交易是处理一个或多个指令的原子操作：

```rust
pub struct Transaction {
    pub signatures: Vec<Signature>,  // 签名数组
    pub message: Message,           // 消息内容
}
```

### 消息结构
```rust
pub struct Message {
    pub header: MessageHeader,           // 消息头
    pub account_keys: Vec<Pubkey>,      // 账户地址数组
    pub recent_blockhash: Hash,         // 最近区块哈希
    pub instructions: Vec<CompiledInstruction>, // 指令数组
}
```

### 指令结构
每个指令包含三个关键信息：
```rust
pub struct Instruction {
    pub program_id: Pubkey,           // 程序 ID
    pub accounts: Vec<AccountMeta>,   // 账户元数据
    pub data: Vec<u8>,               // 指令数据
}
```

### AccountMeta
指定账户权限：
```rust
pub struct AccountMeta {
    pub pubkey: Pubkey,      // 账户地址
    pub is_signer: bool,     // 是否需要签名
    pub is_writable: bool,   // 是否可写
}
```

### 关键特性
- **原子性**：所有指令必须成功执行，否则整个交易失败
- **顺序执行**：指令按添加顺序执行
- **大小限制**：交易最大 1232 字节
- **并行处理**：不修改相同账户的交易可并行执行

## 3. 交易费用 (Transaction Fees)

### 费用类型

#### 基础交易费用
- **固定费用**：每个签名 5000 lamports
- **费用分配**：
  - 50% 被销毁
  - 50% 支付给处理交易的验证者

#### 优先级费用（可选）
- **计算公式**：优先级费用 = 计算单元限制 × 计算单元价格
- **计算单元限制**：交易可使用的最大计算单元（默认每指令 200,000，最大 1.4M）
- **计算单元价格**：每计算单元的价格（微 lamports）
- **分配**：100% 支付给验证者

### 费用设置指令
```typescript
// 设置计算单元限制
const limitInstruction = ComputeBudgetProgram.setComputeUnitLimit({
  units: 300_000
});

// 设置计算单元价格
const priceInstruction = ComputeBudgetProgram.setComputeUnitPrice({
  microLamports: 1
});
```

### 交易优先级计算
```
优先级 = ((计算单元限制 × 计算单元价格) + 基础费用) / (1 + 计算单元限制 + 签名CU + 写锁CU)
```

## 4. 程序 (Programs)

### 程序概念
- Solana 的"智能合约"称为程序
- 程序是包含可执行代码的账户
- 程序是无状态的，通过创建数据账户来存储状态

### 程序开发方式

#### 1. Anchor 框架（推荐）
- 使用 Rust 宏减少样板代码
- 提供更简单的开发体验
- 适合初学者

#### 2. Native Rust
- 直接使用 Rust 编写
- 更大的灵活性
- 更高的复杂性

### 程序部署和升级
- **升级权限**：指定账户可以修改程序
- **不可变程序**：移除升级权限后程序变为不可变
- **可验证构建**：允许验证链上代码与公开源代码的一致性

### 加载器程序
| 加载器 | 程序 ID | 状态 |
|--------|---------|------|
| v1 | `BPFLoader1111111111111111111111111111111111` | 管理指令已禁用 |
| v2 | `BPFLoader2111111111111111111111111111111111` | 管理指令已禁用 |
| v3 | `BPFLoaderUpgradeab1e11111111111111111111111` | 正在淘汰 |
| v4 | `LoaderV411111111111111111111111111111111111` | 预期成为标准 |

### 内置程序

#### 核心程序
- **系统程序**：创建账户、分配空间、转账
- **投票程序**：管理验证者投票状态
- **质押程序**：管理委托质押
- **计算预算程序**：设置计算限制和价格
- **地址查找表程序**：管理地址查找表

#### 预编译程序
- **Ed25519 程序**：验证 ed25519 签名
- **Secp256k1 程序**：验证 secp256k1 恢复操作
- **Secp256r1 程序**：验证 secp256r1 签名

## 5. 程序派生地址 (PDA)

### PDA 概念
- **确定性地址**：使用种子和程序 ID 确定性地派生地址
- **无私钥**：PDA 不在 Ed25519 曲线上，没有对应私钥
- **程序签名**：程序可以代表其派生的 PDA 进行签名

### PDA 派生
派生 PDA 需要三个输入：
1. **可选种子**：预定义输入（字符串、数字、账户地址等）
2. **Bump 种子**：从 255 开始递减，直到找到有效的离曲线地址
3. **程序 ID**：派生 PDA 的程序地址

### 派生示例
```typescript
import { getProgramDerivedAddress } from "@solana/kit";

const programAddress = "11111111111111111111111111111111";
const seeds = ["helloWorld"];

const [pda, bump] = await getProgramDerivedAddress({
  programAddress,
  seeds
});
```

### 规范 Bump
- 第一个产生有效 PDA 的 bump 值称为"规范 bump"
- 安全最佳实践：始终使用规范 bump
- 不同 bump 值可能产生不同的有效 PDA

### PDA 账户创建
```rust
#[account(
    init,
    seeds = [b"data", user.key().as_ref()],
    bump,
    payer = user,
    space = 8 + DataAccount::INIT_SPACE
)]
pub pda_account: Account<'info, DataAccount>,
```

## 6. 跨程序调用 (CPI)

### CPI 概念
- 一个程序调用另一个程序的指令
- 实现程序的可组合性
- 类似于 API 内部调用另一个 API

### CPI 特性
- **签名者权限扩展**：调用者的签名权限扩展到被调用程序
- **PDA 签名**：程序可以代表其派生的 PDA 签名
- **调用深度限制**：最大调用深度为 4 层
- **权限传递**：账户权限在程序间传递

### 基础 CPI
使用 `invoke` 函数进行不需要 PDA 签名的 CPI：
```rust
pub fn invoke(instruction: &Instruction, account_infos: &[AccountInfo]) -> ProgramResult {
    invoke_signed(instruction, account_infos, &[])
}
```

### 带 PDA 签名的 CPI
使用 `invoke_signed` 函数进行需要 PDA 签名的 CPI：
```rust
pub fn invoke_signed(
    instruction: &Instruction,
    account_infos: &[AccountInfo],
    signers_seeds: &[&[&[u8]]],
) -> ProgramResult
```

### Anchor 框架中的 CPI
```rust
let cpi_context = CpiContext::new(
    program_id,
    Transfer {
        from: from_pubkey,
        to: to_pubkey,
    },
).with_signer(signer_seeds);

transfer(cpi_context, amount)?;
```

### CPI 调用栈
- 最大指令栈深度：5
- 初始交易：栈高度 1
- 每次 CPI：栈高度 +1
- 最大 CPI 深度：4

## 总结

Solana 的核心概念相互关联，形成了一个高效的区块链架构：

1. **账户模型**提供了灵活的数据存储机制
2. **交易和指令**实现了原子操作和并行处理
3. **费用机制**确保网络资源的合理分配
4. **程序系统**支持复杂的智能合约逻辑
5. **PDA 机制**提供了确定性地址和程序签名能力
6. **CPI 机制**实现了程序间的可组合性

这些概念共同构成了 Solana 独特的高性能区块链平台，为开发者提供了强大而灵活的开发环境。
