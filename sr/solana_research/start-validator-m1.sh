#!/bin/bash

# Apple Silicon Mac 兼容的 Solana 验证者启动脚本
# 使用 --no-bpf-jit 标志解决兼容性问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 配置
LEDGER_DIR="./test-ledger"
RPC_PORT=8899
FAUCET_PORT=9900
FAUCET_SOL=1000000

# 检查 Solana CLI
check_solana() {
    if ! command -v solana-test-validator >/dev/null 2>&1; then
        print_message $RED "❌ 未找到 solana-test-validator"
        print_message $YELLOW "请先安装 Solana CLI:"
        echo "sh -c \"\$(curl -sSfL https://release.solana.com/v1.18.22/install)\""
        echo "export PATH=\"\$HOME/.local/share/solana/install/active_release/bin:\$PATH\""
        exit 1
    fi
    
    print_message $GREEN "✅ Solana CLI 已安装"
    solana --version
}

# 启动验证者
start_validator() {
    print_message $BLUE "🚀 启动 Apple Silicon 兼容的 Solana 验证者..."
    
    # 清理旧数据
    if [ "$1" = "--reset" ] || [ "$1" = "-r" ]; then
        print_message $YELLOW "🧹 清理旧数据..."
        rm -rf "$LEDGER_DIR"
        shift
    fi
    
    # 创建账本目录
    mkdir -p "$LEDGER_DIR"
    
    print_message $BLUE "📁 账本目录: $LEDGER_DIR"
    print_message $BLUE "📡 RPC 端点: http://localhost:$RPC_PORT"
    print_message $BLUE "💰 Faucet 端点: http://localhost:$FAUCET_PORT"
    print_message $BLUE "🔧 使用 Solana 1.18.20（已修复 Apple Silicon 兼容性）"

    # 启动验证者（Solana 1.18+ 已修复 Apple Silicon 兼容性问题）
    solana-test-validator \
        --ledger "$LEDGER_DIR" \
        --rpc-port $RPC_PORT \
        --faucet-port $FAUCET_PORT \
        --faucet-sol $FAUCET_SOL \
        --reset \
        "$@" &
    
    VALIDATOR_PID=$!
    echo $VALIDATOR_PID > validator.pid
    
    print_message $GREEN "✅ 验证者已启动 (PID: $VALIDATOR_PID)"
    
    # 等待启动
    print_message $YELLOW "⏳ 等待验证者启动..."
    sleep 15
    
    # 检查状态
    check_status
}

# 停止验证者
stop_validator() {
    print_message $BLUE "🛑 停止验证者..."
    
    if [ -f validator.pid ]; then
        PID=$(cat validator.pid)
        if kill -0 $PID 2>/dev/null; then
            kill $PID
            print_message $GREEN "✅ 验证者已停止"
        else
            print_message $YELLOW "⚠️  验证者进程不存在"
        fi
        rm -f validator.pid
    else
        print_message $YELLOW "⚠️  未找到验证者 PID 文件"
    fi
    
    # 强制清理
    pkill -f solana-test-validator 2>/dev/null || true
}

# 检查状态
check_status() {
    print_message $BLUE "📊 检查验证者状态..."
    
    # 检查进程
    if [ -f validator.pid ]; then
        PID=$(cat validator.pid)
        if kill -0 $PID 2>/dev/null; then
            print_message $GREEN "✅ 验证者进程运行中 (PID: $PID)"
        else
            print_message $RED "❌ 验证者进程已停止"
            rm -f validator.pid
            return 1
        fi
    else
        print_message $RED "❌ 未找到验证者 PID"
        return 1
    fi
    
    # 检查端口
    if lsof -i :$RPC_PORT >/dev/null 2>&1; then
        print_message $GREEN "✅ RPC 端口 $RPC_PORT 已占用"
    else
        print_message $RED "❌ RPC 端口 $RPC_PORT 未占用"
        return 1
    fi
    
    # 健康检查
    print_message $BLUE "🔍 测试 RPC 连接..."
    if curl -s -f http://localhost:$RPC_PORT/health >/dev/null 2>&1; then
        print_message $GREEN "✅ RPC 服务正常"
        
        # 获取版本信息
        VERSION=$(curl -s -X POST -H "Content-Type: application/json" \
            -d '{"jsonrpc":"2.0","id":1,"method":"getVersion"}' \
            http://localhost:$RPC_PORT 2>/dev/null | jq -r '.result["solana-core"]' 2>/dev/null || echo "unknown")
        print_message $GREEN "✅ Solana 版本: $VERSION"
        
        return 0
    else
        print_message $RED "❌ RPC 服务不可用"
        return 1
    fi
}

# 测试连接
test_connection() {
    print_message $BLUE "🧪 测试连接..."
    
    if ! check_status; then
        print_message $RED "❌ 验证者未运行"
        return 1
    fi
    
    # 配置 Solana CLI 使用本地网络
    print_message $BLUE "⚙️  配置 Solana CLI..."
    solana config set --url localhost
    
    # 创建测试钱包
    print_message $BLUE "🔑 创建测试钱包..."
    WALLET_FILE="/tmp/test-wallet-$(date +%s).json"
    solana-keygen new --outfile "$WALLET_FILE" --no-passphrase >/dev/null 2>&1
    
    WALLET_ADDRESS=$(solana-keygen pubkey "$WALLET_FILE")
    print_message $GREEN "✅ 钱包地址: $WALLET_ADDRESS"
    
    # 请求空投
    print_message $BLUE "💰 请求空投..."
    if solana airdrop 2 "$WALLET_ADDRESS" --url http://localhost:8899 >/dev/null 2>&1; then
        print_message $GREEN "✅ 空投成功"
        
        # 检查余额
        BALANCE=$(solana balance "$WALLET_ADDRESS" --url http://localhost:8899 2>/dev/null | cut -d' ' -f1)
        print_message $GREEN "✅ 余额: $BALANCE SOL"
    else
        print_message $RED "❌ 空投失败"
        rm -f "$WALLET_FILE"
        return 1
    fi
    
    # 清理测试文件
    rm -f "$WALLET_FILE"
    
    print_message $GREEN "🎉 连接测试成功！"
}

# 查看日志
show_logs() {
    print_message $BLUE "📋 显示验证者日志..."
    if [ -f validator.pid ]; then
        PID=$(cat validator.pid)
        if kill -0 $PID 2>/dev/null; then
            print_message $BLUE "📄 日志文件: $LEDGER_DIR/validator.log"
            if [ -f "$LEDGER_DIR/validator.log" ]; then
                tail -f "$LEDGER_DIR/validator.log"
            else
                print_message $YELLOW "⚠️  日志文件不存在"
            fi
        else
            print_message $RED "❌ 验证者未运行"
        fi
    else
        print_message $RED "❌ 验证者未运行"
    fi
}

# 显示帮助
show_help() {
    echo "Apple Silicon Mac 兼容的 Solana 验证者启动脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start [选项]  启动验证者"
    echo "  stop          停止验证者"
    echo "  restart       重启验证者"
    echo "  status        检查状态"
    echo "  test          测试连接"
    echo "  logs          查看日志"
    echo "  help          显示帮助"
    echo ""
    echo "选项:"
    echo "  --reset, -r   重置账本数据"
    echo ""
    echo "示例:"
    echo "  $0 start --reset    # 重置并启动验证者"
    echo "  $0 start            # 启动验证者（保留数据）"
    echo "  $0 stop             # 停止验证者"
    echo "  $0 test             # 测试连接"
    echo ""
    echo "注意: Solana 1.18+ 已修复 Apple Silicon Mac 兼容性问题"
    echo ""
    echo "🔗 有用的链接:"
    echo "• RPC 端点: http://localhost:8899"
    echo "• Faucet 端点: http://localhost:9900"
    echo "• 健康检查: http://localhost:8899/health"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            check_solana
            shift
            start_validator "$@"
            ;;
        stop)
            stop_validator
            ;;
        restart)
            stop_validator
            sleep 2
            check_solana
            start_validator --reset
            ;;
        status)
            check_status
            ;;
        test)
            test_connection
            ;;
        logs)
            show_logs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
