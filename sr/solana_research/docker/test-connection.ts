#!/usr/bin/env ts-node

import { Connection, Keypair, LAMPORTS_PER_SOL } from "@solana/web3.js";

const RPC_URL = "http://localhost:8899";

async function testSolanaConnection() {
  console.log("🔍 Testing Solana local validator connection...\n");

  try {
    // 创建连接
    const connection = new Connection(RPC_URL, "confirmed");
    console.log(`📡 Connecting to: ${RPC_URL}`);

    // 1. 检查健康状态
    console.log("\n1️⃣ Checking health status...");
    try {
      const health = await fetch(`${RPC_URL}/health`);
      console.log(
        `✅ Health check: ${health.status === 200 ? "OK" : "Failed"}`
      );
    } catch (error) {
      console.log(
        "❌ Health check failed:",
        error instanceof Error ? error.message : String(error)
      );
      return;
    }

    // 2. 获取版本信息
    console.log("\n2️⃣ Getting version info...");
    const version = await connection.getVersion();
    console.log(`✅ Solana version: ${version["solana-core"]}`);
    console.log(`✅ Feature set: ${version["feature-set"]}`);

    // 3. 获取集群信息
    console.log("\n3️⃣ Getting cluster info...");
    const epochInfo = await connection.getEpochInfo();
    console.log(`✅ Current epoch: ${epochInfo.epoch}`);
    console.log(`✅ Current slot: ${epochInfo.absoluteSlot}`);

    // 4. 生成测试钱包
    console.log("\n4️⃣ Generating test wallet...");
    const wallet = Keypair.generate();
    console.log(`✅ Wallet address: ${wallet.publicKey.toString()}`);

    // 5. 请求空投
    console.log("\n5️⃣ Requesting airdrop...");
    const airdropAmount = 2 * LAMPORTS_PER_SOL;
    const airdropSignature = await connection.requestAirdrop(
      wallet.publicKey,
      airdropAmount
    );
    console.log(`✅ Airdrop signature: ${airdropSignature}`);

    // 6. 等待确认
    console.log("\n6️⃣ Waiting for confirmation...");
    const { blockhash, lastValidBlockHeight } =
      await connection.getLatestBlockhash();
    await connection.confirmTransaction({
      signature: airdropSignature,
      blockhash,
      lastValidBlockHeight,
    });
    console.log("✅ Airdrop confirmed");

    // 7. 检查余额
    console.log("\n7️⃣ Checking balance...");
    const balance = await connection.getBalance(wallet.publicKey);
    console.log(`✅ Balance: ${balance / LAMPORTS_PER_SOL} SOL`);

    // 8. 获取最新区块哈希
    console.log("\n8️⃣ Getting latest blockhash...");
    const latestBlockhash = await connection.getLatestBlockhash();
    console.log(
      `✅ Latest blockhash: ${latestBlockhash.blockhash.slice(0, 8)}...`
    );
    console.log(
      `✅ Last valid block height: ${latestBlockhash.lastValidBlockHeight}`
    );

    // 9. 测试 WebSocket 连接
    console.log("\n9️⃣ Testing WebSocket connection...");
    const subscriptionId = connection.onAccountChange(
      wallet.publicKey,
      (accountInfo) => {
        console.log(
          `✅ Account change detected: ${accountInfo.lamports} lamports`
        );
      }
    );

    // 取消订阅
    setTimeout(() => {
      connection.removeAccountChangeListener(subscriptionId);
      console.log("✅ WebSocket subscription test completed");
    }, 1000);

    console.log(
      "\n🎉 All tests passed! Solana local validator is working correctly."
    );

    return {
      connection,
      wallet,
      version,
      epochInfo,
      balance,
    };
  } catch (error) {
    console.error(
      "\n❌ Test failed:",
      error instanceof Error ? error.message : String(error)
    );
    console.error("\n💡 Make sure the Solana validator is running:");
    console.error("   docker-compose up -d");
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testSolanaConnection()
    .then(() => {
      console.log("\n✨ Test completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Test failed:", error);
      process.exit(1);
    });
}

export { testSolanaConnection };
