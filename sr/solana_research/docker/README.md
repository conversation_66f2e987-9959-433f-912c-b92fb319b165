# Solana 本地区块链 Docker 部署

这个目录包含了使用 Docker 启动 Solana 本地区块链的配置文件。

## 快速启动

### 方法1: 使用 Docker Compose（推荐）

```bash
# 启动 Solana 本地验证者
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重置并重启（清除所有数据）
docker-compose down -v
docker-compose up -d
```

### 方法2: 使用单个 Docker 命令

```bash
# 直接使用官方镜像
docker run -d \
  --name solana-local \
  -p 8899:8899 \
  -p 8900:8900 \
  -p 9900:9900 \
  solanalabs/solana:v1.18.22 \
  solana-test-validator \
  --bind-address 0.0.0.0 \
  --rpc-port 8899 \
  --ws-port 8900 \
  --faucet-port 9900 \
  --faucet-sol 1000000 \
  --reset
```

### 方法3: 构建自定义镜像

```bash
# 构建镜像
docker build -t solana-local-validator .

# 运行容器
docker run -d \
  --name solana-local \
  -p 8899:8899 \
  -p 8900:8900 \
  -p 9900:9900 \
  -e RESET_LEDGER=true \
  solana-local-validator
```

## 端口说明

- **8899**: RPC 端口 - 用于 API 调用
- **8900**: WebSocket 端口 - 用于实时订阅
- **9900**: Faucet 端口 - 用于获取测试 SOL

## 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| LEDGER_DIR | /ledger | 账本数据目录 |
| RPC_PORT | 8899 | RPC 端口 |
| WS_PORT | 8900 | WebSocket 端口 |
| FAUCET_PORT | 9900 | Faucet 端口 |
| FAUCET_SOL | 1000000 | Faucet 中的 SOL 数量 |
| RESET_LEDGER | false | 是否重置账本数据 |
| EXTRA_ARGS | - | 额外的验证者参数 |

## 验证连接

```bash
# 检查 RPC 连接
curl -X POST -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"getHealth"}' \
  http://localhost:8899

# 检查版本
curl -X POST -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"getVersion"}' \
  http://localhost:8899

# 获取测试 SOL（需要钱包地址）
curl -X POST -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"requestAirdrop","params":["YOUR_WALLET_ADDRESS",**********]}' \
  http://localhost:8899
```

## 常用命令

```bash
# 查看容器状态
docker ps

# 查看日志
docker logs solana-local -f

# 进入容器
docker exec -it solana-local bash

# 停止容器
docker stop solana-local

# 删除容器
docker rm solana-local

# 清理数据卷
docker volume prune
```

## 故障排除

### 1. 端口被占用
```bash
# 检查端口占用
lsof -i :8899
netstat -tulpn | grep 8899

# 修改端口映射
docker run -p 8898:8899 ...
```

### 2. 容器启动失败
```bash
# 查看详细日志
docker logs solana-local

# 检查容器状态
docker inspect solana-local
```

### 3. 连接问题
```bash
# 检查网络连接
docker network ls
docker network inspect bridge
```

## 开发配置

在你的应用中使用以下配置连接到本地验证者：

```typescript
import { Connection } from '@solana/web3.js';

const connection = new Connection('http://localhost:8899', 'confirmed');
```

## 预加载程序和账户

如果需要预加载特定的程序或账户，可以修改 `start-validator.sh`：

```bash
# 在启动命令中添加
--clone EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v \  # USDC Mint
--clone TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA \    # Token Program
--bpf-program <PROGRAM_ID> <PROGRAM_SO_FILE>
```
