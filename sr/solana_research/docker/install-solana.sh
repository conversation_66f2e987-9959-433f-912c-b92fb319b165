#!/bin/bash

# Solana CLI 安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查系统类型
detect_system() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        SYSTEM="macOS"
        if [[ $(uname -m) == "arm64" ]]; then
            ARCH="Apple Silicon"
        else
            ARCH="Intel"
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        SYSTEM="Linux"
        ARCH=$(uname -m)
    else
        SYSTEM="Unknown"
        ARCH="Unknown"
    fi
    
    print_message $BLUE "🖥️  系统: $SYSTEM ($ARCH)"
}

# 检查是否已安装
check_existing() {
    if command -v solana >/dev/null 2>&1; then
        print_message $GREEN "✅ Solana CLI 已安装"
        solana --version
        print_message $YELLOW "是否要重新安装？(y/N)"
        read -r response
        if [[ ! "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            print_message $BLUE "使用现有安装"
            exit 0
        fi
    fi
}

# 安装 Solana CLI
install_solana() {
    print_message $BLUE "📦 开始安装 Solana CLI..."
    
    # 下载并安装
    sh -c "$(curl -sSfL https://release.solana.com/v1.18.22/install)"
    
    # 添加到 PATH
    SOLANA_PATH="$HOME/.local/share/solana/install/active_release/bin"
    
    # 检查 shell 类型并添加到相应的配置文件
    if [[ $SHELL == *"zsh"* ]]; then
        SHELL_RC="$HOME/.zshrc"
    elif [[ $SHELL == *"bash"* ]]; then
        SHELL_RC="$HOME/.bashrc"
    else
        SHELL_RC="$HOME/.profile"
    fi
    
    # 添加 PATH 到 shell 配置文件
    if ! grep -q "solana" "$SHELL_RC" 2>/dev/null; then
        echo "" >> "$SHELL_RC"
        echo "# Solana CLI" >> "$SHELL_RC"
        echo "export PATH=\"$SOLANA_PATH:\$PATH\"" >> "$SHELL_RC"
        print_message $GREEN "✅ 已添加 Solana CLI 到 PATH ($SHELL_RC)"
    fi
    
    # 临时添加到当前会话的 PATH
    export PATH="$SOLANA_PATH:$PATH"
    
    print_message $GREEN "✅ Solana CLI 安装完成"
}

# 验证安装
verify_installation() {
    print_message $BLUE "🔍 验证安装..."
    
    if command -v solana >/dev/null 2>&1; then
        print_message $GREEN "✅ solana 命令可用"
        solana --version
    else
        print_message $RED "❌ solana 命令不可用"
        return 1
    fi
    
    if command -v solana-test-validator >/dev/null 2>&1; then
        print_message $GREEN "✅ solana-test-validator 命令可用"
    else
        print_message $RED "❌ solana-test-validator 命令不可用"
        return 1
    fi
    
    if command -v solana-keygen >/dev/null 2>&1; then
        print_message $GREEN "✅ solana-keygen 命令可用"
    else
        print_message $RED "❌ solana-keygen 命令不可用"
        return 1
    fi
}

# 配置 Solana CLI
configure_solana() {
    print_message $BLUE "⚙️  配置 Solana CLI..."
    
    # 设置为本地集群
    solana config set --url localhost
    
    # 生成密钥对（如果不存在）
    if [ ! -f "$HOME/.config/solana/id.json" ]; then
        print_message $BLUE "🔑 生成新的密钥对..."
        solana-keygen new --outfile "$HOME/.config/solana/id.json" --no-passphrase
    else
        print_message $GREEN "✅ 密钥对已存在"
    fi
    
    # 显示配置
    print_message $GREEN "✅ Solana CLI 配置完成"
    solana config get
}

# 安装 Homebrew（macOS）
install_homebrew() {
    if [[ "$SYSTEM" == "macOS" ]] && ! command -v brew >/dev/null 2>&1; then
        print_message $YELLOW "🍺 Homebrew 未安装，是否要安装？(y/N)"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            print_message $BLUE "📦 安装 Homebrew..."
            /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        fi
    fi
}

# 使用 Homebrew 安装（macOS 替代方案）
install_via_homebrew() {
    if [[ "$SYSTEM" == "macOS" ]] && command -v brew >/dev/null 2>&1; then
        print_message $BLUE "🍺 使用 Homebrew 安装 Solana..."
        brew install solana
        return 0
    fi
    return 1
}

# 显示后续步骤
show_next_steps() {
    print_message $GREEN "🎉 安装完成！"
    echo ""
    print_message $BLUE "📋 后续步骤:"
    echo "1. 重新加载 shell 配置或重启终端:"
    echo "   source $SHELL_RC"
    echo ""
    echo "2. 启动本地验证者:"
    echo "   ./local-validator.sh start"
    echo ""
    echo "3. 测试连接:"
    echo "   ./manage.sh test"
    echo ""
    echo "4. 查看帮助:"
    echo "   ./local-validator.sh help"
}

# 主函数
main() {
    print_message $BLUE "🚀 Solana CLI 安装程序"
    echo ""
    
    detect_system
    check_existing
    
    # 尝试不同的安装方法
    if [[ "$SYSTEM" == "macOS" ]]; then
        install_homebrew
        if install_via_homebrew; then
            print_message $GREEN "✅ 通过 Homebrew 安装成功"
        else
            install_solana
        fi
    else
        install_solana
    fi
    
    # 验证安装
    if verify_installation; then
        configure_solana
        show_next_steps
    else
        print_message $RED "❌ 安装验证失败"
        print_message $YELLOW "请手动检查安装或重新运行脚本"
        exit 1
    fi
}

# 运行主函数
main "$@"
