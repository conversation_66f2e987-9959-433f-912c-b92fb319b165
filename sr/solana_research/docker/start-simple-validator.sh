#!/bin/bash

# 简单的 Solana 验证者启动脚本（解决 macOS 兼容性问题）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 清理函数
cleanup() {
    print_message $YELLOW "🧹 清理旧数据..."
    
    # 停止现有进程
    pkill -f solana-test-validator 2>/dev/null || true
    
    # 清理账本目录
    rm -rf test-ledger
    rm -rf /tmp/solana-test-ledger
    
    # 清理可能的隐藏文件
    find . -name "._*" -delete 2>/dev/null || true
    find /tmp -name "._*" -path "*/solana*" -delete 2>/dev/null || true
}

# 启动验证者
start_validator() {
    print_message $BLUE "🚀 启动 Solana 本地验证者..."
    
    # 清理旧数据
    cleanup
    
    # 使用临时目录避免文件系统问题
    LEDGER_DIR="/tmp/solana-test-ledger-$(date +%s)"
    
    print_message $BLUE "📁 使用账本目录: $LEDGER_DIR"
    
    # 启动验证者（使用最小参数）
    solana-test-validator \
        --ledger "$LEDGER_DIR" \
        --rpc-port 8899 \
        --faucet-port 9900 \
        --reset &
    
    VALIDATOR_PID=$!
    echo $VALIDATOR_PID > validator.pid
    
    print_message $GREEN "✅ 验证者已启动 (PID: $VALIDATOR_PID)"
    print_message $BLUE "📡 RPC 端点: http://localhost:8899"
    print_message $BLUE "💰 Faucet 端点: http://localhost:9900"
    
    # 等待启动
    print_message $YELLOW "⏳ 等待验证者启动..."
    sleep 15
    
    # 检查状态
    check_status
}

# 检查状态
check_status() {
    print_message $BLUE "📊 检查验证者状态..."
    
    # 检查进程
    if [ -f validator.pid ]; then
        PID=$(cat validator.pid)
        if kill -0 $PID 2>/dev/null; then
            print_message $GREEN "✅ 验证者进程运行中 (PID: $PID)"
        else
            print_message $RED "❌ 验证者进程已停止"
            rm -f validator.pid
            return 1
        fi
    else
        print_message $RED "❌ 未找到验证者 PID"
        return 1
    fi
    
    # 检查端口
    if lsof -i :8899 >/dev/null 2>&1; then
        print_message $GREEN "✅ RPC 端口 8899 已占用"
    else
        print_message $RED "❌ RPC 端口 8899 未占用"
        return 1
    fi
    
    # 健康检查
    print_message $BLUE "🔍 测试 RPC 连接..."
    if curl -s -f http://localhost:8899/health >/dev/null 2>&1; then
        print_message $GREEN "✅ RPC 服务正常"
        
        # 获取版本信息
        VERSION=$(curl -s -X POST -H "Content-Type: application/json" \
            -d '{"jsonrpc":"2.0","id":1,"method":"getVersion"}' \
            http://localhost:8899 | jq -r '.result["solana-core"]' 2>/dev/null || echo "unknown")
        print_message $GREEN "✅ Solana 版本: $VERSION"
        
        return 0
    else
        print_message $RED "❌ RPC 服务不可用"
        return 1
    fi
}

# 停止验证者
stop_validator() {
    print_message $BLUE "🛑 停止验证者..."
    
    if [ -f validator.pid ]; then
        PID=$(cat validator.pid)
        if kill -0 $PID 2>/dev/null; then
            kill $PID
            print_message $GREEN "✅ 验证者已停止"
        else
            print_message $YELLOW "⚠️  验证者进程不存在"
        fi
        rm -f validator.pid
    else
        print_message $YELLOW "⚠️  未找到验证者 PID 文件"
    fi
    
    # 强制清理
    pkill -f solana-test-validator 2>/dev/null || true
}

# 测试连接
test_connection() {
    print_message $BLUE "🧪 测试连接..."
    
    if ! check_status; then
        print_message $RED "❌ 验证者未运行"
        return 1
    fi
    
    # 创建测试钱包
    print_message $BLUE "🔑 创建测试钱包..."
    WALLET_FILE="/tmp/test-wallet.json"
    solana-keygen new --outfile "$WALLET_FILE" --no-passphrase >/dev/null 2>&1
    
    WALLET_ADDRESS=$(solana-keygen pubkey "$WALLET_FILE")
    print_message $GREEN "✅ 钱包地址: $WALLET_ADDRESS"
    
    # 请求空投
    print_message $BLUE "💰 请求空投..."
    if solana airdrop 2 "$WALLET_ADDRESS" --url http://localhost:8899 >/dev/null 2>&1; then
        print_message $GREEN "✅ 空投成功"
        
        # 检查余额
        BALANCE=$(solana balance "$WALLET_ADDRESS" --url http://localhost:8899 2>/dev/null | cut -d' ' -f1)
        print_message $GREEN "✅ 余额: $BALANCE SOL"
    else
        print_message $RED "❌ 空投失败"
        return 1
    fi
    
    # 清理测试文件
    rm -f "$WALLET_FILE"
    
    print_message $GREEN "🎉 连接测试成功！"
}

# 显示帮助
show_help() {
    echo "Solana 简单验证者启动脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动验证者"
    echo "  stop      停止验证者"
    echo "  status    检查状态"
    echo "  test      测试连接"
    echo "  restart   重启验证者"
    echo "  cleanup   清理数据"
    echo "  help      显示帮助"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_validator
            ;;
        stop)
            stop_validator
            ;;
        status)
            check_status
            ;;
        test)
            test_connection
            ;;
        restart)
            stop_validator
            sleep 2
            start_validator
            ;;
        cleanup)
            cleanup
            print_message $GREEN "✅ 清理完成"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
