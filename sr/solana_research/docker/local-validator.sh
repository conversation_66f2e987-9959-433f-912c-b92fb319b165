#!/bin/bash

# Solana 本地验证者启动脚本（适用于 Apple Silicon Mac）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
LEDGER_DIR="./ledger"
RPC_PORT=8899
WS_PORT=8900
FAUCET_SOL=1000000

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 Solana CLI 是否安装
check_solana_cli() {
    if ! command -v solana-test-validator >/dev/null 2>&1; then
        print_message $RED "❌ Solana CLI 未安装"
        print_message $YELLOW "请先安装 Solana CLI:"
        echo "sh -c \"\$(curl -sSfL https://release.solana.com/v1.18.22/install)\""
        echo "export PATH=\"\$HOME/.local/share/solana/install/active_release/bin:\$PATH\""
        exit 1
    fi
    
    print_message $GREEN "✅ Solana CLI 已安装"
    solana --version
}

# 启动验证者
start_validator() {
    print_message $BLUE "🚀 启动 Solana 本地验证者..."
    
    # 创建账本目录
    mkdir -p "$LEDGER_DIR"
    
    # 启动验证者
    solana-test-validator \
        --ledger "$LEDGER_DIR" \
        --bind-address 0.0.0.0 \
        --rpc-port $RPC_PORT \
        --faucet-port 9900 \
        --faucet-sol $FAUCET_SOL \
        --reset \
        --log &
    
    VALIDATOR_PID=$!
    echo $VALIDATOR_PID > validator.pid
    
    print_message $GREEN "✅ 验证者已启动 (PID: $VALIDATOR_PID)"
    print_message $BLUE "📡 RPC 端点: http://localhost:$RPC_PORT"
    print_message $BLUE "🔌 WebSocket 端点: ws://localhost:$WS_PORT"
    
    # 等待验证者启动
    print_message $YELLOW "⏳ 等待验证者启动..."
    sleep 10
    
    # 检查健康状态
    if curl -s -f http://localhost:$RPC_PORT/health >/dev/null; then
        print_message $GREEN "✅ 验证者已就绪"
    else
        print_message $YELLOW "⚠️  验证者可能还在启动中"
    fi
}

# 停止验证者
stop_validator() {
    print_message $BLUE "🛑 停止 Solana 验证者..."
    
    if [ -f validator.pid ]; then
        PID=$(cat validator.pid)
        if kill -0 $PID 2>/dev/null; then
            kill $PID
            rm validator.pid
            print_message $GREEN "✅ 验证者已停止"
        else
            print_message $YELLOW "⚠️  验证者进程不存在"
            rm -f validator.pid
        fi
    else
        print_message $YELLOW "⚠️  未找到验证者 PID 文件"
        # 尝试通过进程名杀死
        pkill -f solana-test-validator || true
    fi
}

# 检查状态
check_status() {
    print_message $BLUE "📊 验证者状态:"
    
    if [ -f validator.pid ]; then
        PID=$(cat validator.pid)
        if kill -0 $PID 2>/dev/null; then
            print_message $GREEN "✅ 验证者正在运行 (PID: $PID)"
        else
            print_message $RED "❌ 验证者进程不存在"
            rm -f validator.pid
        fi
    else
        print_message $RED "❌ 验证者未运行"
    fi
    
    # 检查端口
    if lsof -i :$RPC_PORT >/dev/null 2>&1; then
        print_message $GREEN "✅ RPC 端口 $RPC_PORT 已占用"
    else
        print_message $RED "❌ RPC 端口 $RPC_PORT 未占用"
    fi
    
    # 健康检查
    if curl -s -f http://localhost:$RPC_PORT/health >/dev/null; then
        print_message $GREEN "✅ RPC 服务正常"
    else
        print_message $RED "❌ RPC 服务不可用"
    fi
}

# 查看日志
show_logs() {
    print_message $BLUE "📋 显示验证者日志..."
    if [ -f validator.pid ]; then
        PID=$(cat validator.pid)
        if kill -0 $PID 2>/dev/null; then
            tail -f "$LEDGER_DIR/validator.log" 2>/dev/null || {
                print_message $YELLOW "⚠️  日志文件不存在，显示进程输出..."
                # 这里可能需要其他方式查看日志
            }
        else
            print_message $RED "❌ 验证者未运行"
        fi
    else
        print_message $RED "❌ 验证者未运行"
    fi
}

# 重置数据
reset_data() {
    print_message $YELLOW "⚠️  这将删除所有区块链数据，确定要继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        stop_validator
        print_message $BLUE "🧹 清理数据..."
        rm -rf "$LEDGER_DIR"
        print_message $GREEN "✅ 数据已清理"
        start_validator
    else
        print_message $YELLOW "❌ 操作已取消"
    fi
}

# 显示帮助
show_help() {
    echo "Solana 本地验证者管理脚本（适用于 Apple Silicon Mac）"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动验证者"
    echo "  stop      停止验证者"
    echo "  restart   重启验证者"
    echo "  status    查看状态"
    echo "  logs      查看日志"
    echo "  reset     重置数据"
    echo "  help      显示帮助"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            check_solana_cli
            start_validator
            ;;
        stop)
            stop_validator
            ;;
        restart)
            stop_validator
            sleep 2
            check_solana_cli
            start_validator
            ;;
        status)
            check_status
            ;;
        logs)
            show_logs
            ;;
        reset)
            reset_data
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
