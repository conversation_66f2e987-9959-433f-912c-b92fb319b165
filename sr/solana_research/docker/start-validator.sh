#!/bin/bash

echo "🚀 Starting Solana Test Validator..."

# 设置默认参数
LEDGER_DIR=${LEDGER_DIR:-/ledger}
RPC_PORT=${RPC_PORT:-8899}
WS_PORT=${WS_PORT:-8900}
FAUCET_PORT=${FAUCET_PORT:-9900}
FAUCET_SOL=${FAUCET_SOL:-1000000}

# 清理旧的账本数据（如果需要）
if [ "$RESET_LEDGER" = "true" ]; then
    echo "🧹 Resetting ledger data..."
    rm -rf $LEDGER_DIR/*
fi

# 启动验证者
exec solana-test-validator \
    --bind-address 0.0.0.0 \
    --rpc-port $RPC_PORT \
    --ws-port $WS_PORT \
    --ledger $LEDGER_DIR \
    --faucet-port $FAUCET_PORT \
    --faucet-sol $FAUCET_SOL \
    --log \
    --enable-rpc-transaction-history \
    --enable-extended-tx-metadata-storage \
    $EXTRA_ARGS
