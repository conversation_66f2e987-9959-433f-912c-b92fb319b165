# 🚀 Solana 开发环境快速启动

## ⚠️ Apple Silicon Mac 兼容性问题

在 Apple Silicon Mac 上，`solana-test-validator` 存在兼容性问题：

```
Error: Archive error: extra entry found: "._genesis.bin" Regular
```

## 🎯 推荐解决方案

### 方案 1: 使用 Devnet（强烈推荐）

这是最稳定的解决方案，适合所有开发需求：

```bash
# 配置使用 devnet
solana config set --url devnet

# 生成钱包（如果没有）
solana-keygen new --outfile ~/.config/solana/id.json --no-passphrase

# 获取测试 SOL
solana airdrop 2

# 查看余额和配置
solana balance
solana config get
```

### 方案 2: 使用现有的 TypeScript 脚本

你已经有配置好的测试脚本：

```bash
cd sr/solana_research
# 运行你的 getting-test-sol.ts 脚本
```

### 方案 3: 尝试本地验证者（可能失败）

```bash
# 使用 Apple Silicon 兼容脚本
./sr/solana_research/start-validator-m1.sh start --reset

# 如果失败，回退到 devnet
solana config set --url devnet
```

### 方案 4: Docker（仅适用于 Intel Mac/Linux）

```bash
# 启动 Docker 验证者
./manage.sh start
```

## 验证部署

```bash
# 测试连接
./manage.sh test

# 查看状态
./manage.sh status

# 查看日志
./manage.sh logs
```

## 基本使用

### 1. 启动验证者

```bash
./manage.sh start
```

输出示例：

```
🚀 启动 Solana 本地验证者...
✅ 服务启动成功
📡 RPC 端点: http://localhost:8899
🔌 WebSocket 端点: ws://localhost:8900
💰 Faucet 端点: http://localhost:9900
```

### 2. 测试连接

```bash
./manage.sh test
```

输出示例：

```
🔍 Testing Solana local validator connection...

📡 Connecting to: http://localhost:8899

1️⃣ Checking health status...
✅ Health check: OK

2️⃣ Getting version info...
✅ Solana version: 1.18.22

3️⃣ Getting cluster info...
✅ Current epoch: 0
✅ Current slot: 1234

4️⃣ Generating test wallet...
✅ Wallet address: 7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU

5️⃣ Requesting airdrop...
✅ Airdrop signature: 5j7s...

6️⃣ Waiting for confirmation...
✅ Airdrop confirmed

7️⃣ Checking balance...
✅ Balance: 2 SOL

🎉 All tests passed! Solana local validator is working correctly.
```

### 3. 在代码中使用

```typescript
import { Connection, Keypair, LAMPORTS_PER_SOL } from "@solana/web3.js";

// 连接到本地验证者
const connection = new Connection("http://localhost:8899", "confirmed");

// 生成钱包
const wallet = Keypair.generate();

// 请求空投
const signature = await connection.requestAirdrop(
  wallet.publicKey,
  2 * LAMPORTS_PER_SOL
);

// 确认交易
await connection.confirmTransaction(signature);

// 检查余额
const balance = await connection.getBalance(wallet.publicKey);
console.log(`余额: ${balance / LAMPORTS_PER_SOL} SOL`);
```

### 4. 使用 curl 测试

```bash
# 检查健康状态
curl http://localhost:8899/health

# 获取版本信息
curl -X POST -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"getVersion"}' \
  http://localhost:8899

# 请求空投（替换为你的钱包地址）
curl -X POST -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"requestAirdrop","params":["YOUR_WALLET_ADDRESS",**********]}' \
  http://localhost:8899
```

## 管理命令

| 命令                  | 说明           |
| --------------------- | -------------- |
| `./manage.sh start`   | 启动验证者     |
| `./manage.sh stop`    | 停止验证者     |
| `./manage.sh restart` | 重启验证者     |
| `./manage.sh reset`   | 重置数据并重启 |
| `./manage.sh logs`    | 查看实时日志   |
| `./manage.sh status`  | 查看状态       |
| `./manage.sh test`    | 测试连接       |
| `./manage.sh shell`   | 进入容器       |
| `./manage.sh clean`   | 清理所有数据   |

## 端口说明

- **8899**: RPC API 端口
- **8900**: WebSocket 端口
- **9900**: Faucet 端口

## 故障排除

### 1. 端口被占用

```bash
# 检查端口占用
lsof -i :8899

# 停止占用进程
kill -9 <PID>
```

### 2. Docker 权限问题

```bash
# 添加用户到 docker 组
sudo usermod -aG docker $USER

# 重新登录或运行
newgrp docker
```

### 3. 服务启动失败

```bash
# 查看详细日志
./manage.sh logs

# 重置并重启
./manage.sh reset
```

### 4. 连接测试失败

```bash
# 检查服务状态
./manage.sh status

# 等待服务完全启动
sleep 30 && ./manage.sh test
```

## 开发建议

1. **保持验证者运行**: 开发期间保持验证者运行，避免频繁重启
2. **使用固定钱包**: 保存测试钱包私钥，避免重复空投
3. **监控日志**: 使用 `./manage.sh logs` 监控交易和错误
4. **定期重置**: 测试完成后使用 `./manage.sh reset` 清理数据

## 下一步

- 查看完整文档: [README.md](./README.md)
- 学习 Solana 开发: [Solana Cookbook](https://solanacookbook.com/)
- 探索示例代码: [Program Examples](https://github.com/solana-developers/program-examples)
