#!/bin/bash

# Solana 本地验证者管理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="solana-local"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "Solana 本地验证者管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动 Solana 验证者"
    echo "  stop      停止 Solana 验证者"
    echo "  restart   重启 Solana 验证者"
    echo "  reset     重置并重启（清除所有数据）"
    echo "  logs      查看日志"
    echo "  status    查看状态"
    echo "  test      测试连接"
    echo "  shell     进入容器 shell"
    echo "  clean     清理所有数据和容器"
    echo "  build     构建自定义镜像"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动验证者"
    echo "  $0 logs     # 查看实时日志"
    echo "  $0 test     # 测试连接"
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_message $RED "❌ Docker 未运行，请先启动 Docker"
        exit 1
    fi
}

# 启动服务
start_service() {
    print_message $BLUE "🚀 启动 Solana 本地验证者..."
    cd "$SCRIPT_DIR"
    
    if docker-compose ps | grep -q "Up"; then
        print_message $YELLOW "⚠️  服务已经在运行中"
        return
    fi
    
    docker-compose up -d
    
    print_message $GREEN "✅ 服务启动成功"
    print_message $BLUE "📡 RPC 端点: http://localhost:8899"
    print_message $BLUE "🔌 WebSocket 端点: ws://localhost:8900"
    print_message $BLUE "💰 Faucet 端点: http://localhost:9900"
    
    # 等待服务启动
    print_message $YELLOW "⏳ 等待服务完全启动..."
    sleep 10
    
    # 检查健康状态
    if curl -s -f http://localhost:8899/health >/dev/null; then
        print_message $GREEN "✅ 验证者已就绪"
    else
        print_message $YELLOW "⚠️  验证者可能还在启动中，请稍等片刻"
    fi
}

# 停止服务
stop_service() {
    print_message $BLUE "🛑 停止 Solana 本地验证者..."
    cd "$SCRIPT_DIR"
    docker-compose down
    print_message $GREEN "✅ 服务已停止"
}

# 重启服务
restart_service() {
    print_message $BLUE "🔄 重启 Solana 本地验证者..."
    stop_service
    sleep 2
    start_service
}

# 重置服务
reset_service() {
    print_message $YELLOW "⚠️  这将删除所有区块链数据，确定要继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_message $BLUE "🧹 重置 Solana 本地验证者..."
        cd "$SCRIPT_DIR"
        docker-compose down -v
        sleep 2
        docker-compose up -d
        print_message $GREEN "✅ 服务已重置并重启"
    else
        print_message $YELLOW "❌ 操作已取消"
    fi
}

# 查看日志
show_logs() {
    print_message $BLUE "📋 显示 Solana 验证者日志..."
    cd "$SCRIPT_DIR"
    docker-compose logs -f
}

# 查看状态
show_status() {
    print_message $BLUE "📊 Solana 验证者状态:"
    cd "$SCRIPT_DIR"
    
    echo ""
    echo "Docker 容器状态:"
    docker-compose ps
    
    echo ""
    echo "端口占用情况:"
    netstat -tulpn 2>/dev/null | grep -E ":(8899|8900|9900)" || echo "未找到相关端口"
    
    echo ""
    echo "健康检查:"
    if curl -s -f http://localhost:8899/health >/dev/null; then
        print_message $GREEN "✅ RPC 服务正常"
    else
        print_message $RED "❌ RPC 服务不可用"
    fi
}

# 测试连接
test_connection() {
    print_message $BLUE "🧪 测试 Solana 连接..."
    
    # 检查是否安装了 Node.js 和 TypeScript
    if ! command -v node >/dev/null 2>&1; then
        print_message $RED "❌ 需要安装 Node.js 来运行测试"
        return 1
    fi
    
    if ! command -v npx >/dev/null 2>&1; then
        print_message $RED "❌ 需要安装 npm 来运行测试"
        return 1
    fi
    
    cd "$SCRIPT_DIR"
    
    # 检查是否有 package.json，如果没有则创建简单的测试
    if [ ! -f "package.json" ]; then
        print_message $YELLOW "📦 初始化测试环境..."
        npm init -y >/dev/null 2>&1
        npm install @solana/web3.js typescript ts-node @types/node >/dev/null 2>&1
    fi
    
    # 运行测试
    npx ts-node test-connection.ts
}

# 进入容器 shell
enter_shell() {
    print_message $BLUE "🐚 进入 Solana 容器 shell..."
    cd "$SCRIPT_DIR"
    
    if ! docker-compose ps | grep -q "Up"; then
        print_message $RED "❌ 服务未运行，请先启动服务"
        return 1
    fi
    
    docker-compose exec solana-test-validator bash
}

# 清理所有数据
clean_all() {
    print_message $YELLOW "⚠️  这将删除所有容器、镜像和数据，确定要继续吗？(y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_message $BLUE "🧹 清理所有数据..."
        cd "$SCRIPT_DIR"
        
        # 停止并删除容器
        docker-compose down -v
        
        # 删除相关镜像
        docker images | grep solana | awk '{print $3}' | xargs -r docker rmi -f
        
        # 清理未使用的卷
        docker volume prune -f
        
        print_message $GREEN "✅ 清理完成"
    else
        print_message $YELLOW "❌ 操作已取消"
    fi
}

# 构建自定义镜像
build_image() {
    print_message $BLUE "🔨 构建自定义 Solana 镜像..."
    cd "$SCRIPT_DIR"
    docker build -t solana-local-validator .
    print_message $GREEN "✅ 镜像构建完成"
}

# 主函数
main() {
    check_docker
    
    case "${1:-help}" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        reset)
            reset_service
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        test)
            test_connection
            ;;
        shell)
            enter_shell
            ;;
        clean)
            clean_all
            ;;
        build)
            build_image
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
