# 注意：官方镜像在 Apple Silicon Mac 上可能不兼容
# 如果遇到 AVX 指令集错误，请使用本地安装的 solana-test-validator

services:
  solana-test-validator:
    image: solanalabs/solana:v1.18.22
    container_name: solana-local-validator
    platform: linux/amd64  # 强制使用 amd64 平台
    ports:
      - "8899:8899"  # RPC port
      - "8900:8900"  # WebSocket port
      - "9900:9900"  # Faucet port
    command: >
      solana-test-validator
      --bind-address 0.0.0.0
      --rpc-port 8899
      --ws-port 8900
      --ledger /ledger
      --faucet-port 9900
      --faucet-sol 1000000
      --reset
      --quiet
    volumes:
      - solana_ledger:/ledger
    environment:
      - RUST_LOG=info
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8899/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

volumes:
  solana_ledger:
    driver: local
