{"name": "solana-local-validator-test", "version": "1.0.0", "description": "Solana local validator testing utilities", "main": "test-connection.ts", "scripts": {"test": "ts-node test-connection.ts", "start": "./manage.sh start", "stop": "./manage.sh stop", "restart": "./manage.sh restart", "reset": "./manage.sh reset", "logs": "./manage.sh logs", "status": "./manage.sh status"}, "dependencies": {"@solana/web3.js": "^1.87.6", "@solana/spl-token": "^0.3.9"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0", "ts-node": "^10.9.0"}, "keywords": ["solana", "blockchain", "docker", "testing"], "author": "", "license": "MIT"}