# Solana Research

## 账户模型还是 UTXO？

Solana 有三种确认级别：

### 1. Processed

- 交易已被接收并处理
- 区块在多数分叉上
- 区块包含目标交易

### 2. Confirmed

- 满足 Processed 的所有条件
- 66%+ 的质押权重已对该区块投票
- 提供较高的确定性

### 3. Finalized

- 满足 Confirmed 的所有条件
- 在该区块之上已构建 31+ 个确认区块
- 提供最高的确定性，几乎不可逆转

## 是否支持质押

### 质押机制

- **委托质押**: 代币持有者可以将 SOL 委托给验证者
- **验证者质押**: 验证者需要质押 SOL 来参与共识
- **质押奖励**: 质押者可以获得通胀奖励
- **惩罚机制**: 恶意行为会导致质押代币被削减

### 质押参数

- **最小质押量**: 验证者需要至少 1 SOL
- **解除质押期**: 需要等待一个 epoch（约 2-3 天）
- **奖励分配**: 基于质押权重和网络参与度

### 质押流程

#### 质押步骤

1. **选择验证者**: 研究验证者的性能和佣金率
2. **创建质押账户**: 使用系统程序创建质押账户
3. **委托质押**: 将 SOL 委托给选定的验证者
4. **等待激活**: 质押在下一个 epoch 生效
5. **获得奖励**: 每个 epoch 结束后获得奖励

#### 解除质押流程

1. **发起解除质押**: 提交解除质押交易
2. **冷却期**: 等待一个 epoch（约 2-3 天）
3. **提取资金**: 冷却期结束后可提取 SOL

#### 质押奖励

- **年化收益率**: 约 5-8%（根据网络参数变化）
- **奖励来源**: 通胀奖励 + 交易费用
- **分配机制**: 基于质押权重按比例分配

## 是否为多链结构？

单链，通过集成跨链桥协议来支持跨链功能

## 代币精度

1 SOL = 10^9 lamports

## 密码学算法

1. **Ed25519**:

   - 用于数字签名
   - 公钥和私钥生成
   - 交易签名验证

2. **SHA-256**:

   - Proof of History 哈希计算
   - 区块哈希计算
   - 数据完整性验证

3. **Blake3**:
   - 某些哈希计算场景
   - 性能优化的哈希算法

## 地址生成

1. **Ed25519 公钥地址**: 标准钱包地址
2. **程序派生地址 (PDA)**: 由程序和种子确定性生成
3. **关联代币账户地址**: 为特定代币自动生成的账户地址

## 交易签名

1. **构建交易消息**: 创建包含指令的交易
2. **序列化消息**: 将交易消息序列化为字节数组
3. **计算哈希**: 对序列化数据计算 SHA-256 哈希
4. **Ed25519 签名**: 使用私钥对哈希进行签名
5. **附加签名**: 将签名附加到交易中

## 是否支持 Tag/Memo？

支持 Memo
