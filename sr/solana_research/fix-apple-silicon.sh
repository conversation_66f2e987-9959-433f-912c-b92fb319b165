#!/bin/bash

# 修复 Apple Silicon Mac 上的 Solana 兼容性问题
# 基于: https://gist.github.com/lorisleiva/41ef16a92af68a205a8eea6703d8e6d5

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检测系统架构
detect_architecture() {
    if [[ $(uname -m) == "arm64" ]] && [[ "$OSTYPE" == "darwin"* ]]; then
        print_message $BLUE "🍎 检测到 Apple Silicon Mac"
        return 0
    else
        print_message $YELLOW "⚠️  不是 Apple Silicon Mac，可能不需要此修复"
        return 1
    fi
}

# 检查依赖
check_dependencies() {
    print_message $BLUE "🔍 检查依赖..."
    
    # 检查 Rust
    if ! command -v rustc >/dev/null 2>&1; then
        print_message $RED "❌ 未安装 Rust"
        print_message $YELLOW "请先安装 Rust: curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh"
        exit 1
    fi
    
    # 检查 Git
    if ! command -v git >/dev/null 2>&1; then
        print_message $RED "❌ 未安装 Git"
        exit 1
    fi
    
    print_message $GREEN "✅ 依赖检查通过"
}

# 编译 Solana
compile_solana() {
    print_message $BLUE "🔨 开始编译 Solana v1.8.2..."
    
    SOLANA_DIR="sr/solana_research/third-parties/solana"
    
    if [ ! -d "$SOLANA_DIR" ]; then
        print_message $RED "❌ 未找到 Solana 源码目录: $SOLANA_DIR"
        exit 1
    fi
    
    cd "$SOLANA_DIR"
    
    # 检查是否已经编译过
    if [ -d "bin" ] && [ -f "bin/solana-test-validator" ]; then
        print_message $GREEN "✅ Solana 已编译，跳过编译步骤"
        cd - >/dev/null
        return 0
    fi
    
    print_message $YELLOW "⏳ 编译过程可能需要 20-30 分钟，请耐心等待..."
    
    # 运行编译脚本
    if ./scripts/cargo-install-all.sh .; then
        print_message $GREEN "✅ Solana 编译成功"
    else
        print_message $RED "❌ Solana 编译失败"
        exit 1
    fi
    
    cd - >/dev/null
}

# 设置环境变量
setup_environment() {
    print_message $BLUE "⚙️  设置环境变量..."
    
    SOLANA_DIR="$(pwd)/sr/solana_research/third-parties/solana"
    SOLANA_BIN_DIR="$SOLANA_DIR/bin"
    
    if [ ! -d "$SOLANA_BIN_DIR" ]; then
        print_message $RED "❌ 未找到编译后的二进制文件目录: $SOLANA_BIN_DIR"
        exit 1
    fi
    
    # 检查 shell 类型
    if [[ $SHELL == *"zsh"* ]]; then
        SHELL_RC="$HOME/.zshrc"
    elif [[ $SHELL == *"bash"* ]]; then
        SHELL_RC="$HOME/.bashrc"
    else
        SHELL_RC="$HOME/.profile"
    fi
    
    # 添加到 PATH
    EXPORT_LINE="export PATH=\"$SOLANA_BIN_DIR:\$PATH\""
    
    if ! grep -q "$SOLANA_BIN_DIR" "$SHELL_RC" 2>/dev/null; then
        echo "" >> "$SHELL_RC"
        echo "# Solana (Apple Silicon 兼容版本)" >> "$SHELL_RC"
        echo "$EXPORT_LINE" >> "$SHELL_RC"
        print_message $GREEN "✅ 已添加到 $SHELL_RC"
    else
        print_message $GREEN "✅ PATH 已配置"
    fi
    
    # 临时设置当前会话的 PATH
    export PATH="$SOLANA_BIN_DIR:$PATH"
    
    print_message $BLUE "📋 请运行以下命令重新加载环境变量:"
    echo "source $SHELL_RC"
}

# 验证安装
verify_installation() {
    print_message $BLUE "🧪 验证安装..."
    
    # 检查 solana 命令
    if command -v solana >/dev/null 2>&1; then
        VERSION=$(solana --version)
        print_message $GREEN "✅ solana 命令可用: $VERSION"
    else
        print_message $RED "❌ solana 命令不可用"
        return 1
    fi
    
    # 检查 solana-test-validator 命令
    if command -v solana-test-validator >/dev/null 2>&1; then
        print_message $GREEN "✅ solana-test-validator 命令可用"
    else
        print_message $RED "❌ solana-test-validator 命令不可用"
        return 1
    fi
    
    # 检查 solana-keygen 命令
    if command -v solana-keygen >/dev/null 2>&1; then
        print_message $GREEN "✅ solana-keygen 命令可用"
    else
        print_message $RED "❌ solana-keygen 命令不可用"
        return 1
    fi
}

# 创建 Apple Silicon 兼容的启动脚本
create_validator_script() {
    print_message $BLUE "📝 创建 Apple Silicon 兼容的验证者启动脚本..."
    
    cat > sr/solana_research/start-validator-m1.sh << 'EOF'
#!/bin/bash

# Apple Silicon Mac 兼容的 Solana 验证者启动脚本
# 使用 --no-bpf-jit 标志解决兼容性问题

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 配置
LEDGER_DIR="./test-ledger"
RPC_PORT=8899
FAUCET_PORT=9900
FAUCET_SOL=1000000

# 启动验证者
start_validator() {
    print_message $BLUE "🚀 启动 Apple Silicon 兼容的 Solana 验证者..."
    
    # 清理旧数据
    if [ "$1" = "--reset" ] || [ "$1" = "-r" ]; then
        print_message $YELLOW "🧹 清理旧数据..."
        rm -rf "$LEDGER_DIR"
    fi
    
    # 创建账本目录
    mkdir -p "$LEDGER_DIR"
    
    print_message $BLUE "📁 账本目录: $LEDGER_DIR"
    print_message $BLUE "📡 RPC 端点: http://localhost:$RPC_PORT"
    print_message $BLUE "💰 Faucet 端点: http://localhost:$FAUCET_PORT"
    print_message $YELLOW "⚠️  使用 --no-bpf-jit 标志以兼容 Apple Silicon"
    
    # 启动验证者（关键：添加 --no-bpf-jit 标志）
    solana-test-validator \
        --ledger "$LEDGER_DIR" \
        --rpc-port $RPC_PORT \
        --faucet-port $FAUCET_PORT \
        --faucet-sol $FAUCET_SOL \
        --no-bpf-jit \
        --reset \
        "$@"
}

# 停止验证者
stop_validator() {
    print_message $BLUE "🛑 停止验证者..."
    pkill -f solana-test-validator || true
    print_message $GREEN "✅ 验证者已停止"
}

# 检查状态
check_status() {
    print_message $BLUE "📊 检查验证者状态..."
    
    if lsof -i :$RPC_PORT >/dev/null 2>&1; then
        print_message $GREEN "✅ RPC 端口 $RPC_PORT 已占用"
        
        if curl -s -f http://localhost:$RPC_PORT/health >/dev/null; then
            print_message $GREEN "✅ RPC 服务正常"
        else
            print_message $RED "❌ RPC 服务不可用"
        fi
    else
        print_message $RED "❌ 验证者未运行"
    fi
}

# 显示帮助
show_help() {
    echo "Apple Silicon Mac 兼容的 Solana 验证者启动脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start [选项]  启动验证者"
    echo "  stop          停止验证者"
    echo "  status        检查状态"
    echo "  help          显示帮助"
    echo ""
    echo "选项:"
    echo "  --reset, -r   重置账本数据"
    echo ""
    echo "示例:"
    echo "  $0 start --reset    # 重置并启动验证者"
    echo "  $0 start            # 启动验证者（保留数据）"
    echo "  $0 stop             # 停止验证者"
    echo ""
    echo "注意: 此脚本自动使用 --no-bpf-jit 标志以兼容 Apple Silicon Mac"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            shift
            start_validator "$@"
            ;;
        stop)
            stop_validator
            ;;
        status)
            check_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
EOF

    chmod +x sr/solana_research/start-validator-m1.sh
    print_message $GREEN "✅ 已创建 Apple Silicon 兼容脚本: sr/solana_research/start-validator-m1.sh"
}

# 测试验证者
test_validator() {
    print_message $BLUE "🧪 测试验证者启动..."
    
    # 启动验证者（后台运行）
    print_message $BLUE "🚀 启动测试验证者..."
    timeout 30 solana-test-validator --ledger ./test-ledger-test --reset --no-bpf-jit --quiet &
    VALIDATOR_PID=$!
    
    # 等待启动
    sleep 10
    
    # 检查是否成功
    if kill -0 $VALIDATOR_PID 2>/dev/null && curl -s http://localhost:8899/health >/dev/null; then
        print_message $GREEN "✅ 验证者测试成功"
        kill $VALIDATOR_PID 2>/dev/null || true
        rm -rf ./test-ledger-test
        return 0
    else
        print_message $RED "❌ 验证者测试失败"
        kill $VALIDATOR_PID 2>/dev/null || true
        rm -rf ./test-ledger-test
        return 1
    fi
}

# 显示完成信息
show_completion() {
    print_message $GREEN "🎉 Apple Silicon Mac Solana 修复完成！"
    echo ""
    print_message $BLUE "📋 使用方法:"
    echo "1. 重新加载环境变量:"
    echo "   source ~/.zshrc  # 或 ~/.bashrc"
    echo ""
    echo "2. 启动验证者:"
    echo "   ./sr/solana_research/start-validator-m1.sh start --reset"
    echo ""
    echo "3. 检查状态:"
    echo "   ./sr/solana_research/start-validator-m1.sh status"
    echo ""
    echo "4. 停止验证者:"
    echo "   ./sr/solana_research/start-validator-m1.sh stop"
    echo ""
    print_message $YELLOW "⚠️  重要提示:"
    echo "• 验证者启动时会自动使用 --no-bpf-jit 标志"
    echo "• 如果使用 Anchor，请使用 'anchor test --skip-local-validator'"
    echo "• 编译后的二进制文件位于: sr/solana_research/third-parties/solana/bin"
}

# 主函数
main() {
    print_message $BLUE "🔧 Apple Silicon Mac Solana 修复工具"
    echo ""
    
    # 检测架构
    if ! detect_architecture; then
        print_message $YELLOW "继续执行修复（可能不需要）..."
    fi
    
    # 检查依赖
    check_dependencies
    
    # 编译 Solana
    compile_solana
    
    # 设置环境
    setup_environment
    
    # 验证安装
    if verify_installation; then
        print_message $GREEN "✅ 安装验证成功"
    else
        print_message $RED "❌ 安装验证失败"
        exit 1
    fi
    
    # 创建启动脚本
    create_validator_script
    
    # 测试验证者
    if test_validator; then
        print_message $GREEN "✅ 验证者测试成功"
    else
        print_message $YELLOW "⚠️  验证者测试失败，但修复已完成"
    fi
    
    # 显示完成信息
    show_completion
}

# 运行主函数
main "$@"
