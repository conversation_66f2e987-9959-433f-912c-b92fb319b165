# Solana 钱包业务调研报告

## 1. 链的简单实现原理

Solana 是一个高性能的区块链平台，采用了独特的架构设计：

### 核心创新

- **Proof of History (PoH)**: Solana 的核心创新，提供了一个可验证的时间顺序，允许网络在不需要等待所有验证者达成共识的情况下继续处理交易
- **Tower BFT**: 基于 PoH 的共识机制，是 PBFT 的优化版本
- **Turbine**: 块传播协议，将数据分片传输以提高效率
- **Gulf Stream**: 无内存池的交易转发协议
- **Sealevel**: 并行智能合约运行时
- **Pipelining**: 交易处理的流水线优化
- **Cloudbreak**: 水平扩展的账户数据库
- **Archivers**: 分布式账本存储

## 2. 特点

### 性能特点

- **高吞吐量**: 理论上可达 65,000 TPS
- **低延迟**: 400ms 的区块时间
- **低成本**: 交易费用极低（约 $0.00025）
- **可扩展性**: 随着硬件性能提升而扩展

### 技术特点

- **单一全局状态**: 所有数据存储在统一的账户模型中
- **并行处理**: 支持智能合约并行执行
- **无分片**: 单链架构，避免了跨分片复杂性
- **开发者友好**: 支持 Rust、C、C++ 编程

## 3. 账户模型还是 UTXO？

**Solana 使用账户模型**，而非 UTXO 模型。

### 账户模型特点

- 所有数据存储在"账户"中，类似于键值对数据库
- 每个账户有唯一的 32 字节地址（通常是 Ed25519 公钥）
- 账户包含以下字段：
  - `data`: 存储任意数据的字节数组
  - `executable`: 标识账户是否为程序
  - `lamports`: 账户余额（以 lamports 为单位，1 SOL = 10^9 lamports）
  - `owner`: 拥有该账户的程序 ID
  - `rent_epoch`: 租金相关字段（已废弃）

### 账户类型

1. **系统账户**: 由系统程序拥有的普通钱包账户
2. **程序账户**: 存储可执行代码的智能合约
3. **数据账户**: 由程序创建用于存储状态的账户
4. **Sysvar 账户**: 存储网络集群状态的特殊账户

## 4. 共识机制

Solana 使用 **Tower BFT** 共识机制，这是基于 Proof of History (PoH) 的拜占庭容错算法。

### 核心组件

1. **Proof of History (PoH)**:

   - 提供可验证的时间顺序
   - 使用 SHA-256 哈希函数创建历史记录
   - 允许验证者独立验证事件顺序

2. **Leader Rotation**:

   - 验证者轮流担任领导者
   - 领导者负责排序交易和生成区块
   - 基于质押权重确定领导者顺序

3. **Voting Mechanism**:
   - 验证者对区块进行投票
   - 需要 66%+ 的质押权重确认区块
   - 支持乐观确认机制

## 5. 确认位

Solana 有三种确认级别：

### 1. Processed

- 交易已被接收并处理
- 区块在多数分叉上
- 区块包含目标交易

### 2. Confirmed

- 满足 Processed 的所有条件
- 66%+ 的质押权重已对该区块投票
- 提供较高的确定性

### 3. Finalized

- 满足 Confirmed 的所有条件
- 在该区块之上已构建 31+ 个确认区块
- 提供最高的确定性，几乎不可逆转

## 6. 是否支持质押

**是的，Solana 支持质押**。

### 质押机制

- **委托质押**: 代币持有者可以将 SOL 委托给验证者
- **验证者质押**: 验证者需要质押 SOL 来参与共识
- **质押奖励**: 质押者可以获得通胀奖励
- **惩罚机制**: 恶意行为会导致质押代币被削减

### 质押参数

- **最小质押量**: 验证者需要至少 1 SOL
- **解除质押期**: 需要等待一个 epoch（约 2-3 天）
- **奖励分配**: 基于质押权重和网络参与度

## 7. 是否为多链结构？

**Solana 不是多链结构**，它是单一链架构。

### 单链特点

- 所有交易在同一条链上处理
- 全局状态一致性
- 无需跨链桥接
- 简化了开发复杂性

### 集群概念

虽然是单链，但 Solana 有不同的网络集群：

- **Mainnet**: 生产环境 (`https://api.mainnet-beta.solana.com`)
- **Devnet**: 开发测试环境 (`https://api.devnet.solana.com`)
- **Testnet**: 验证者测试环境 (`https://api.testnet.solana.com`)

## 8. 多链支持

由于 Solana 是单链架构，原生不支持多链。但可以通过以下方式实现跨链：

### 跨链解决方案

1. **Wormhole**: 主要的跨链桥协议
2. **Allbridge**: 支持多链资产转移
3. **Portal Bridge**: 基于 Wormhole 的代币桥
4. **Sollet**: 包装代币协议

### 支持的链

- Ethereum
- BSC (Binance Smart Chain)
- Polygon
- Avalanche
- Terra
- 等其他主流区块链

## 9. 钱包跨链

钱包可以通过集成跨链桥协议来支持跨链功能：

### 实现方式

1. **集成跨链桥 API**: 如 Wormhole、Allbridge
2. **包装代币支持**: 支持 wrapped tokens
3. **多链地址管理**: 管理不同链的地址
4. **跨链交易追踪**: 追踪跨链交易状态

### 技术考虑

- 跨链交易确认时间较长
- 需要支付多条链的手续费
- 安全性依赖于桥协议
- 用户体验需要优化

## 10. 质押流程

### 质押步骤

1. **选择验证者**: 研究验证者的性能和佣金率
2. **创建质押账户**: 使用系统程序创建质押账户
3. **委托质押**: 将 SOL 委托给选定的验证者
4. **等待激活**: 质押在下一个 epoch 生效
5. **获得奖励**: 每个 epoch 结束后获得奖励

### 解除质押流程

1. **发起解除质押**: 提交解除质押交易
2. **冷却期**: 等待一个 epoch（约 2-3 天）
3. **提取资金**: 冷却期结束后可提取 SOL

### 质押奖励

- **年化收益率**: 约 5-8%（根据网络参数变化）
- **奖励来源**: 通胀奖励 + 交易费用
- **分配机制**: 基于质押权重按比例分配

## 11. 代币精度

### SOL 代币精度

- **精度**: 9 位小数
- **最小单位**: lamport (1 SOL = 10^9 lamports)
- **显示格式**: 通常显示到小数点后 9 位

### SPL 代币精度

- **可配置精度**: 0-9 位小数
- **常见精度**:
  - USDC: 6 位小数
  - USDT: 6 位小数
  - 大多数代币: 6-9 位小数

### 精度处理

```typescript
// 转换 SOL 到 lamports
const solAmount = 1.5; // 1.5 SOL
const lamports = solAmount * 1e9; // 1,500,000,000 lamports

// 转换 lamports 到 SOL
const lamportAmount = 1500000000;
const sol = lamportAmount / 1e9; // 1.5 SOL
```

## 12. 密码学算法

### 主要算法

1. **Ed25519**:

   - 用于数字签名
   - 公钥和私钥生成
   - 交易签名验证

2. **SHA-256**:

   - Proof of History 哈希计算
   - 区块哈希计算
   - 数据完整性验证

3. **Blake3**:
   - 某些哈希计算场景
   - 性能优化的哈希算法

### 密钥特点

- **公钥长度**: 32 字节
- **私钥长度**: 32 字节
- **签名长度**: 64 字节
- **地址格式**: Base58 编码的公钥

## 13. 地址生成

### 标准地址生成

```typescript
import { Keypair } from "@solana/web3.js";

// 生成新的密钥对
const keypair = Keypair.generate();
const publicKey = keypair.publicKey.toString(); // Base58 编码的地址
const secretKey = keypair.secretKey; // 64 字节的私钥
```

### 地址类型

1. **Ed25519 公钥地址**: 标准钱包地址
2. **程序派生地址 (PDA)**: 由程序和种子确定性生成
3. **关联代币账户地址**: 为特定代币自动生成的账户地址

### PDA 生成

```typescript
import { PublicKey } from "@solana/web3.js";

const [pda, bump] = await PublicKey.findProgramAddress(
  [Buffer.from("seed"), userPublicKey.toBuffer()],
  programId
);
```

## 14. 交易签名

### 签名流程

1. **构建交易消息**: 创建包含指令的交易
2. **序列化消息**: 将交易消息序列化为字节数组
3. **计算哈希**: 对序列化数据计算 SHA-256 哈希
4. **Ed25519 签名**: 使用私钥对哈希进行签名
5. **附加签名**: 将签名附加到交易中

### 签名示例

```typescript
import {
  Transaction,
  SystemProgram,
  PublicKey,
  Keypair,
} from "@solana/web3.js";

// 创建交易
const transaction = new Transaction().add(
  SystemProgram.transfer({
    fromPubkey: fromKeypair.publicKey,
    toPubkey: toPublicKey,
    lamports: **********, // 1 SOL
  })
);

// 设置最新区块哈希
transaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
transaction.feePayer = fromKeypair.publicKey;

// 签名交易
transaction.sign(fromKeypair);

// 发送交易
const signature = await connection.sendRawTransaction(transaction.serialize());
```

### 多重签名

```typescript
// 多个签名者
const signers = [keypair1, keypair2, keypair3];
transaction.sign(...signers);

// 部分签名
transaction.partialSign(keypair1);
transaction.partialSign(keypair2);
```

## 15. 是否支持 Tag/Memo？

**是的，Solana 支持 Memo**。

### Memo 程序

- **程序 ID**: `MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr`
- **功能**: 在交易中添加文本备注
- **用途**: 交易备注、合规要求、用户标识

### 使用示例

```typescript
import { TransactionInstruction, PublicKey } from "@solana/web3.js";

const MEMO_PROGRAM_ID = new PublicKey(
  "MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr"
);

// 创建 memo 指令
const memoInstruction = new TransactionInstruction({
  keys: [],
  programId: MEMO_PROGRAM_ID,
  data: Buffer.from("Payment for order #12345", "utf8"),
});

// 添加到交易中
transaction.add(memoInstruction);
```

### 应用场景

- **交易备注**: 记录交易目的
- **合规要求**: 满足监管要求
- **订单关联**: 关联电商订单
- **身份标识**: 标识交易方身份

## 16. 交易体系构建

### 交易结构

```typescript
interface Transaction {
  signatures: Array<Signature>; // 签名数组
  message: Message; // 交易消息
}

interface Message {
  header: MessageHeader; // 消息头
  accountKeys: Array<PublicKey>; // 账户公钥数组
  recentBlockhash: Blockhash; // 最新区块哈希
  instructions: Array<Instruction>; // 指令数组
}

interface Instruction {
  programIdIndex: number; // 程序 ID 索引
  accounts: Array<number>; // 账户索引数组
  data: Buffer; // 指令数据
}
```

### 交易生命周期

1. **构建**: 创建交易和指令
2. **签名**: 使用私钥签名
3. **发送**: 提交到网络
4. **确认**: 等待网络确认
5. **最终化**: 达到最终确认状态

## 17. 如何创建 Native Token 交易？

### SOL 转账交易

```typescript
import {
  Connection,
  PublicKey,
  Keypair,
  Transaction,
  SystemProgram,
  sendAndConfirmTransaction,
} from "@solana/web3.js";

async function createSOLTransfer(
  connection: Connection,
  fromKeypair: Keypair,
  toPublicKey: PublicKey,
  lamports: number
) {
  // 创建转账指令
  const transferInstruction = SystemProgram.transfer({
    fromPubkey: fromKeypair.publicKey,
    toPubkey: toPublicKey,
    lamports: lamports,
  });

  // 创建交易
  const transaction = new Transaction().add(transferInstruction);

  // 设置交易参数
  const { blockhash } = await connection.getLatestBlockhash();
  transaction.recentBlockhash = blockhash;
  transaction.feePayer = fromKeypair.publicKey;

  // 签名并发送
  const signature = await sendAndConfirmTransaction(connection, transaction, [
    fromKeypair,
  ]);

  return signature;
}
```

### 创建账户交易

```typescript
async function createAccount(
  connection: Connection,
  payerKeypair: Keypair,
  newAccountKeypair: Keypair,
  space: number,
  programId: PublicKey
) {
  // 计算租金
  const rentExemption = await connection.getMinimumBalanceForRentExemption(
    space
  );

  // 创建账户指令
  const createAccountInstruction = SystemProgram.createAccount({
    fromPubkey: payerKeypair.publicKey,
    newAccountPubkey: newAccountKeypair.publicKey,
    lamports: rentExemption,
    space: space,
    programId: programId,
  });

  const transaction = new Transaction().add(createAccountInstruction);

  // 设置交易参数并发送
  const { blockhash } = await connection.getLatestBlockhash();
  transaction.recentBlockhash = blockhash;
  transaction.feePayer = payerKeypair.publicKey;

  const signature = await sendAndConfirmTransaction(connection, transaction, [
    payerKeypair,
    newAccountKeypair,
  ]);

  return signature;
}
```

## 18. 支持同质化代币吗？如何创建同质化代币交易？

**是的，Solana 通过 SPL Token 程序支持同质化代币**。

### SPL Token 特点

- **程序 ID**: `TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA`
- **标准**: SPL Token Standard
- **功能**: 创建、铸造、转账、销毁代币

### 创建代币 Mint

```typescript
import {
  createMint,
  getOrCreateAssociatedTokenAccount,
  mintTo,
  transfer,
} from "@solana/spl-token";

// 创建新的代币 mint
async function createTokenMint(
  connection: Connection,
  payerKeypair: Keypair,
  mintAuthority: PublicKey,
  freezeAuthority: PublicKey | null,
  decimals: number
) {
  const mint = await createMint(
    connection,
    payerKeypair, // 付费者
    mintAuthority, // 铸造权限
    freezeAuthority, // 冻结权限
    decimals // 小数位数
  );

  return mint;
}
```

### 代币转账

```typescript
async function transferTokens(
  connection: Connection,
  fromKeypair: Keypair,
  toPublicKey: PublicKey,
  mintPublicKey: PublicKey,
  amount: number
) {
  // 获取或创建关联代币账户
  const fromTokenAccount = await getOrCreateAssociatedTokenAccount(
    connection,
    fromKeypair,
    mintPublicKey,
    fromKeypair.publicKey
  );

  const toTokenAccount = await getOrCreateAssociatedTokenAccount(
    connection,
    fromKeypair,
    mintPublicKey,
    toPublicKey
  );

  // 执行转账
  const signature = await transfer(
    connection,
    fromKeypair,
    fromTokenAccount.address,
    toTokenAccount.address,
    fromKeypair.publicKey,
    amount
  );

  return signature;
}
```

### 铸造代币

```typescript
async function mintTokens(
  connection: Connection,
  payerKeypair: Keypair,
  mintPublicKey: PublicKey,
  destinationPublicKey: PublicKey,
  mintAuthority: Keypair,
  amount: number
) {
  // 获取目标代币账户
  const tokenAccount = await getOrCreateAssociatedTokenAccount(
    connection,
    payerKeypair,
    mintPublicKey,
    destinationPublicKey
  );

  // 铸造代币
  const signature = await mintTo(
    connection,
    payerKeypair,
    mintPublicKey,
    tokenAccount.address,
    mintAuthority,
    amount
  );

  return signature;
}
```

## 19. 支持 NFT 吗？如何创建 NFT 交易？

**是的，Solana 支持 NFT**，主要通过 Metaplex 标准实现。

### NFT 特点

- **基于 SPL Token**: NFT 是供应量为 1 且小数位为 0 的 SPL Token
- **Metaplex 标准**: 提供元数据和版税功能
- **程序 ID**: `metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s`

### 创建 NFT

```typescript
import {
  Metaplex,
  keypairIdentity,
  bundlrStorage,
} from "@metaplex-foundation/js";
import { Connection, Keypair, clusterApiUrl } from "@solana/web3.js";

async function createNFT(
  connection: Connection,
  creatorKeypair: Keypair,
  metadata: {
    name: string;
    symbol: string;
    description: string;
    image: string;
    attributes?: Array<{ trait_type: string; value: string }>;
  }
) {
  // 初始化 Metaplex
  const metaplex = Metaplex.make(connection)
    .use(keypairIdentity(creatorKeypair))
    .use(bundlrStorage());

  // 上传元数据
  const { uri } = await metaplex.nfts().uploadMetadata(metadata);

  // 创建 NFT
  const { nft } = await metaplex.nfts().create({
    uri: uri,
    name: metadata.name,
    symbol: metadata.symbol,
    sellerFeeBasisPoints: 500, // 5% 版税
    creators: [
      {
        address: creatorKeypair.publicKey,
        verified: true,
        share: 100,
      },
    ],
  });

  return nft;
}
```

### NFT 转账

```typescript
async function transferNFT(
  connection: Connection,
  fromKeypair: Keypair,
  toPublicKey: PublicKey,
  mintPublicKey: PublicKey
) {
  const metaplex = Metaplex.make(connection).use(keypairIdentity(fromKeypair));

  // 转移 NFT
  const { response } = await metaplex.nfts().transfer({
    nftOrSft: { address: mintPublicKey, tokenStandard: 0 },
    toOwner: toPublicKey,
  });

  return response.signature;
}
```

### 查询 NFT 元数据

```typescript
async function getNFTMetadata(
  connection: Connection,
  mintPublicKey: PublicKey
) {
  const metaplex = Metaplex.make(connection);

  const nft = await metaplex.nfts().findByMint({
    mintAddress: mintPublicKey,
  });

  return {
    name: nft.name,
    symbol: nft.symbol,
    description: nft.json?.description,
    image: nft.json?.image,
    attributes: nft.json?.attributes,
    creators: nft.creators,
    royalty: nft.sellerFeeBasisPoints,
  };
}
```

## 20. 支持质押吗？如何创建质押交易？

**是的，Solana 原生支持质押**。

### 质押程序

- **程序 ID**: `Stake11111111111111111111111111111111111111`
- **功能**: 创建质押账户、委托、解除委托

### 创建质押账户

```typescript
import {
  StakeProgram,
  Authorized,
  Lockup,
  PublicKey,
  Keypair,
  Transaction,
} from "@solana/web3.js";

async function createStakeAccount(
  connection: Connection,
  payerKeypair: Keypair,
  stakeKeypair: Keypair,
  authorizedKeypair: Keypair,
  lamports: number
) {
  // 创建质押账户指令
  const createStakeAccountInstruction = StakeProgram.createAccount({
    fromPubkey: payerKeypair.publicKey,
    stakePubkey: stakeKeypair.publicKey,
    authorized: new Authorized(
      authorizedKeypair.publicKey, // 质押权限
      authorizedKeypair.publicKey // 提取权限
    ),
    lockup: new Lockup(0, 0, PublicKey.default), // 无锁定
    lamports: lamports,
  });

  const transaction = new Transaction().add(createStakeAccountInstruction);

  const { blockhash } = await connection.getLatestBlockhash();
  transaction.recentBlockhash = blockhash;
  transaction.feePayer = payerKeypair.publicKey;

  const signature = await sendAndConfirmTransaction(connection, transaction, [
    payerKeypair,
    stakeKeypair,
  ]);

  return signature;
}
```

### 委托质押

```typescript
async function delegateStake(
  connection: Connection,
  stakeKeypair: Keypair,
  authorizedKeypair: Keypair,
  validatorPublicKey: PublicKey
) {
  // 创建委托指令
  const delegateInstruction = StakeProgram.delegate({
    stakePubkey: stakeKeypair.publicKey,
    authorizedPubkey: authorizedKeypair.publicKey,
    votePubkey: validatorPublicKey,
  });

  const transaction = new Transaction().add(delegateInstruction);

  const { blockhash } = await connection.getLatestBlockhash();
  transaction.recentBlockhash = blockhash;
  transaction.feePayer = authorizedKeypair.publicKey;

  const signature = await sendAndConfirmTransaction(connection, transaction, [
    authorizedKeypair,
  ]);

  return signature;
}
```

### 解除质押

```typescript
async function deactivateStake(
  connection: Connection,
  stakeKeypair: Keypair,
  authorizedKeypair: Keypair
) {
  // 创建解除激活指令
  const deactivateInstruction = StakeProgram.deactivate({
    stakePubkey: stakeKeypair.publicKey,
    authorizedPubkey: authorizedKeypair.publicKey,
  });

  const transaction = new Transaction().add(deactivateInstruction);

  const { blockhash } = await connection.getLatestBlockhash();
  transaction.recentBlockhash = blockhash;
  transaction.feePayer = authorizedKeypair.publicKey;

  const signature = await sendAndConfirmTransaction(connection, transaction, [
    authorizedKeypair,
  ]);

  return signature;
}
```

### 提取质押奖励

```typescript
async function withdrawStakeRewards(
  connection: Connection,
  stakeKeypair: Keypair,
  authorizedKeypair: Keypair,
  recipientPublicKey: PublicKey,
  lamports: number
) {
  // 创建提取指令
  const withdrawInstruction = StakeProgram.withdraw({
    stakePubkey: stakeKeypair.publicKey,
    authorizedPubkey: authorizedKeypair.publicKey,
    toPubkey: recipientPublicKey,
    lamports: lamports,
  });

  const transaction = new Transaction().add(withdrawInstruction);

  const { blockhash } = await connection.getLatestBlockhash();
  transaction.recentBlockhash = blockhash;
  transaction.feePayer = authorizedKeypair.publicKey;

  const signature = await sendAndConfirmTransaction(connection, transaction, [
    authorizedKeypair,
  ]);

  return signature;
}
```

## 21. 交易解析

### 解析交易结构

```typescript
async function parseTransaction(connection: Connection, signature: string) {
  // 获取交易详情
  const transaction = await connection.getTransaction(signature, {
    maxSupportedTransactionVersion: 0,
  });

  if (!transaction) {
    throw new Error("Transaction not found");
  }

  const result = {
    signature: signature,
    slot: transaction.slot,
    blockTime: transaction.blockTime,
    fee: transaction.meta?.fee,
    status: transaction.meta?.err ? "failed" : "success",
    instructions: [],
    balanceChanges: [],
  };

  // 解析指令
  transaction.transaction.message.instructions.forEach((instruction, index) => {
    const programId =
      transaction.transaction.message.accountKeys[instruction.programIdIndex];

    result.instructions.push({
      index: index,
      programId: programId.toString(),
      accounts: instruction.accounts.map((accountIndex) =>
        transaction.transaction.message.accountKeys[accountIndex].toString()
      ),
      data: instruction.data,
    });
  });

  // 解析余额变化
  if (transaction.meta?.preBalances && transaction.meta?.postBalances) {
    transaction.meta.preBalances.forEach((preBalance, index) => {
      const postBalance = transaction.meta!.postBalances[index];
      const change = postBalance - preBalance;

      if (change !== 0) {
        result.balanceChanges.push({
          account:
            transaction.transaction.message.accountKeys[index].toString(),
          change: change,
          preBalance: preBalance,
          postBalance: postBalance,
        });
      }
    });
  }

  return result;
}
```

## 22. 如何确定是 Native Token 交易类型？

### 识别 SOL 转账交易

```typescript
function isSOLTransfer(instruction: any): boolean {
  const SYSTEM_PROGRAM_ID = "11111111111111111111111111111111";

  // 检查是否为系统程序调用
  if (instruction.programId !== SYSTEM_PROGRAM_ID) {
    return false;
  }

  // 检查指令数据，SOL 转账的指令类型为 2
  const instructionData = Buffer.from(instruction.data, "base64");
  return instructionData.length >= 4 && instructionData.readUInt32LE(0) === 2;
}

function parseSOLTransfer(instruction: any, accountKeys: string[]) {
  const data = Buffer.from(instruction.data, "base64");
  const lamports = data.readBigUInt64LE(4); // 从第4字节开始读取金额

  return {
    type: "SOL_TRANSFER",
    from: accountKeys[instruction.accounts[0]],
    to: accountKeys[instruction.accounts[1]],
    amount: lamports.toString(),
    amountSOL: Number(lamports) / 1e9,
  };
}
```

### 识别账户创建交易

```typescript
function isCreateAccount(instruction: any): boolean {
  const SYSTEM_PROGRAM_ID = "11111111111111111111111111111111";

  if (instruction.programId !== SYSTEM_PROGRAM_ID) {
    return false;
  }

  // 创建账户的指令类型为 0
  const instructionData = Buffer.from(instruction.data, "base64");
  return instructionData.length >= 4 && instructionData.readUInt32LE(0) === 0;
}
```

## 23. 支持同质化代币吗？如何解析同质化代币交易？

### 识别 SPL Token 交易

```typescript
function isSPLTokenTransfer(instruction: any): boolean {
  const TOKEN_PROGRAM_ID = "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA";
  const TOKEN_2022_PROGRAM_ID = "TokenzQdBNbLqP5VEhdkAS6EPFLC1PHnBqCXEpPxuEb";

  return (
    instruction.programId === TOKEN_PROGRAM_ID ||
    instruction.programId === TOKEN_2022_PROGRAM_ID
  );
}

function parseSPLTokenTransfer(instruction: any, accountKeys: string[]) {
  const data = Buffer.from(instruction.data, "base64");
  const instructionType = data.readUInt8(0);

  // 指令类型 3 = Transfer
  if (instructionType === 3) {
    const amount = data.readBigUInt64LE(1);

    return {
      type: "SPL_TOKEN_TRANSFER",
      source: accountKeys[instruction.accounts[0]], // 源代币账户
      destination: accountKeys[instruction.accounts[1]], // 目标代币账户
      owner: accountKeys[instruction.accounts[2]], // 所有者
      amount: amount.toString(),
    };
  }

  // 指令类型 7 = MintTo
  if (instructionType === 7) {
    const amount = data.readBigUInt64LE(1);

    return {
      type: "SPL_TOKEN_MINT",
      mint: accountKeys[instruction.accounts[0]], // Mint 账户
      destination: accountKeys[instruction.accounts[1]], // 目标代币账户
      authority: accountKeys[instruction.accounts[2]], // 铸造权限
      amount: amount.toString(),
    };
  }

  return null;
}
```

### 获取代币信息

```typescript
async function getTokenInfo(connection: Connection, mintAddress: string) {
  try {
    const mintPublicKey = new PublicKey(mintAddress);
    const mintInfo = await getMint(connection, mintPublicKey);

    return {
      address: mintAddress,
      decimals: mintInfo.decimals,
      supply: mintInfo.supply.toString(),
      mintAuthority: mintInfo.mintAuthority?.toString(),
      freezeAuthority: mintInfo.freezeAuthority?.toString(),
    };
  } catch (error) {
    console.error("Failed to get token info:", error);
    return null;
  }
}
```

## 24. 支持 NFT 吗？如何解析 NFT 交易？

### 识别 NFT 交易

```typescript
function isNFTTransaction(instruction: any): boolean {
  const METAPLEX_PROGRAM_ID = "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s";
  const TOKEN_METADATA_PROGRAM_ID =
    "metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s";

  return (
    instruction.programId === METAPLEX_PROGRAM_ID ||
    instruction.programId === TOKEN_METADATA_PROGRAM_ID
  );
}

async function parseNFTTransaction(
  connection: Connection,
  instruction: any,
  accountKeys: string[]
) {
  const data = Buffer.from(instruction.data, "base64");
  const instructionType = data.readUInt8(0);

  // 根据不同的指令类型解析
  switch (instructionType) {
    case 0: // CreateMetadataAccount
      return {
        type: "NFT_CREATE_METADATA",
        metadata: accountKeys[instruction.accounts[0]],
        mint: accountKeys[instruction.accounts[1]],
        mintAuthority: accountKeys[instruction.accounts[2]],
        payer: accountKeys[instruction.accounts[3]],
        updateAuthority: accountKeys[instruction.accounts[4]],
      };

    case 1: // UpdateMetadataAccount
      return {
        type: "NFT_UPDATE_METADATA",
        metadata: accountKeys[instruction.accounts[0]],
        updateAuthority: accountKeys[instruction.accounts[1]],
      };

    case 2: // DeprecatedCreateMasterEdition
    case 17: // CreateMasterEditionV3
      return {
        type: "NFT_CREATE_MASTER_EDITION",
        edition: accountKeys[instruction.accounts[0]],
        mint: accountKeys[instruction.accounts[1]],
        updateAuthority: accountKeys[instruction.accounts[2]],
        mintAuthority: accountKeys[instruction.accounts[3]],
      };

    default:
      return {
        type: "NFT_UNKNOWN",
        instructionType: instructionType,
      };
  }
}
```

### 检查是否为 NFT

```typescript
async function isNFT(
  connection: Connection,
  mintAddress: string
): Promise<boolean> {
  try {
    const mintPublicKey = new PublicKey(mintAddress);
    const mintInfo = await getMint(connection, mintPublicKey);

    // NFT 特征：供应量为 1，小数位为 0
    return mintInfo.supply === BigInt(1) && mintInfo.decimals === 0;
  } catch (error) {
    return false;
  }
}
```

## 25. 支持质押吗？如何解析质押交易？

### 识别质押交易

```typescript
function isStakeTransaction(instruction: any): boolean {
  const STAKE_PROGRAM_ID = "Stake11111111111111111111111111111111111111";
  return instruction.programId === STAKE_PROGRAM_ID;
}

function parseStakeTransaction(instruction: any, accountKeys: string[]) {
  const data = Buffer.from(instruction.data, "base64");
  const instructionType = data.readUInt32LE(0);

  switch (instructionType) {
    case 0: // Initialize
      return {
        type: "STAKE_INITIALIZE",
        stakeAccount: accountKeys[instruction.accounts[0]],
        rentSysvar: accountKeys[instruction.accounts[1]],
      };

    case 1: // Authorize
      return {
        type: "STAKE_AUTHORIZE",
        stakeAccount: accountKeys[instruction.accounts[0]],
        clockSysvar: accountKeys[instruction.accounts[1]],
        authority: accountKeys[instruction.accounts[2]],
      };

    case 2: // DelegateStake
      return {
        type: "STAKE_DELEGATE",
        stakeAccount: accountKeys[instruction.accounts[0]],
        voteAccount: accountKeys[instruction.accounts[1]],
        clockSysvar: accountKeys[instruction.accounts[2]],
        stakeHistorySysvar: accountKeys[instruction.accounts[3]],
        stakeConfigAccount: accountKeys[instruction.accounts[4]],
        stakeAuthority: accountKeys[instruction.accounts[5]],
      };

    case 3: // Split
      return {
        type: "STAKE_SPLIT",
        stakeAccount: accountKeys[instruction.accounts[0]],
        newStakeAccount: accountKeys[instruction.accounts[1]],
        authority: accountKeys[instruction.accounts[2]],
      };

    case 4: // Withdraw
      const lamports = data.readBigUInt64LE(4);
      return {
        type: "STAKE_WITHDRAW",
        stakeAccount: accountKeys[instruction.accounts[0]],
        destination: accountKeys[instruction.accounts[1]],
        clockSysvar: accountKeys[instruction.accounts[2]],
        stakeHistorySysvar: accountKeys[instruction.accounts[3]],
        withdrawAuthority: accountKeys[instruction.accounts[4]],
        amount: lamports.toString(),
      };

    case 5: // Deactivate
      return {
        type: "STAKE_DEACTIVATE",
        stakeAccount: accountKeys[instruction.accounts[0]],
        clockSysvar: accountKeys[instruction.accounts[1]],
        stakeAuthority: accountKeys[instruction.accounts[2]],
      };

    default:
      return {
        type: "STAKE_UNKNOWN",
        instructionType: instructionType,
      };
  }
}
```

### 查询质押账户信息

```typescript
async function getStakeAccountInfo(
  connection: Connection,
  stakeAddress: string
) {
  try {
    const stakePublicKey = new PublicKey(stakeAddress);
    const accountInfo = await connection.getAccountInfo(stakePublicKey);

    if (!accountInfo) {
      return null;
    }

    // 解析质押账户数据
    const stakeAccount = StakeProgram.parseStakeAccount(accountInfo);

    return {
      address: stakeAddress,
      lamports: accountInfo.lamports,
      state: stakeAccount.type,
      meta: stakeAccount.meta,
      stake: stakeAccount.stake,
    };
  } catch (error) {
    console.error("Failed to get stake account info:", error);
    return null;
  }
}
```

## 26. 地址生成方式

### 标准地址生成流程

1. **生成私钥**: 32 字节随机数
2. **计算公钥**: 使用 Ed25519 算法从私钥计算公钥
3. **Base58 编码**: 将公钥进行 Base58 编码得到地址

```typescript
import { Keypair, PublicKey } from "@solana/web3.js";
import bs58 from "bs58";

// 方法1: 使用 Solana SDK
const keypair = Keypair.generate();
const address = keypair.publicKey.toString(); // Base58 编码的地址

// 方法2: 从私钥生成
const secretKey = new Uint8Array(64); // 64字节私钥
const keypairFromSecret = Keypair.fromSecretKey(secretKey);

// 方法3: 从助记词生成
import { derivePath } from "ed25519-hd-key";
import * as bip39 from "bip39";

function generateFromMnemonic(mnemonic: string, accountIndex: number = 0) {
  const seed = bip39.mnemonicToSeedSync(mnemonic);
  const derivationPath = `m/44'/501'/${accountIndex}'/0'`;
  const derivedSeed = derivePath(derivationPath, seed.toString("hex")).key;
  const keypair = Keypair.fromSeed(derivedSeed);
  return keypair;
}
```

## 27. 有哪些地址格式

### 地址类型

1. **标准地址**: Ed25519 公钥，32 字节，Base58 编码
2. **程序派生地址 (PDA)**: 由程序和种子确定性生成
3. **关联代币账户地址**: 为特定用户和代币自动生成

### 地址特征

- **长度**: 32-44 个字符（Base58 编码后）
- **字符集**: Base58 字符集（不包含 0、O、I、l）
- **校验**: 无内置校验位，需要程序验证

```typescript
// 验证地址格式
function isValidSolanaAddress(address: string): boolean {
  try {
    const publicKey = new PublicKey(address);
    return PublicKey.isOnCurve(publicKey.toBytes());
  } catch {
    return false;
  }
}
```

## 28. 地址格式编码流程

### Base58 编码流程

1. **输入**: 32 字节公钥
2. **Base58 编码**: 使用比特币的 Base58 字符集
3. **输出**: 32-44 字符的地址字符串

```typescript
import bs58 from "bs58";

// 编码流程
function encodeAddress(publicKeyBytes: Uint8Array): string {
  return bs58.encode(publicKeyBytes);
}

// 解码流程
function decodeAddress(address: string): Uint8Array {
  return bs58.decode(address);
}

// Base58 字符集
const BASE58_ALPHABET =
  "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";
```

## 29. 是否有官方 SDK？支持哪些编程语言？

### 官方和社区 SDK

| 语言       | SDK             | 仓库                                                                                               | 状态 |
| ---------- | --------------- | -------------------------------------------------------------------------------------------------- | ---- |
| TypeScript | @solana/web3.js | [solana-web3.js](https://github.com/anza-xyz/solana-web3.js)                                       | 官方 |
| Rust       | solana_sdk      | [agave](https://github.com/anza-xyz/agave)                                                         | 官方 |
| Python     | solders         | [solders](https://github.com/kevinheavey/solders)                                                  | 社区 |
| Java       | sava            | [sava.software](https://sava.software)                                                             | 社区 |
| Java       | solanaj         | [solanaj](https://github.com/skynetcap/solanaj)                                                    | 社区 |
| Java       | solana4j        | [solana4j](https://github.com/LMAX-Exchange/solana4j)                                              | 社区 |
| C++        | solcpp          | [solcpp](https://github.com/mschneider/solcpp)                                                     | 社区 |
| C++        | solana-c        | [solana-c-sdk](https://github.com/VAR-META-Tech/solana-c-sdk)                                      | 社区 |
| Go         | solana-go       | [solana-go](https://github.com/gagliardetto/solana-go)                                             | 社区 |
| Kotlin     | sol4k           | [sol4k](https://github.com/sol4k/sol4k)                                                            | 社区 |
| Kotlin     | solanaKT        | [solanaKT](https://github.com/metaplex-foundation/SolanaKT)                                        | 社区 |
| Dart       | solana          | [espresso-cash](https://github.com/espresso-cash/espresso-cash-public/tree/master/packages/solana) | 社区 |
| C#         | solnet          | [Solnet](https://github.com/bmresearch/Solnet)                                                     | 社区 |
| GdScript   | godot           | [godot-solana-sdk](https://github.com/Virus-Axel/godot-solana-sdk/)                                | 社区 |

## 30. 完整的创建地址代码实现 (TypeScript)

```typescript
import { Keypair, PublicKey } from "@solana/web3.js";
import * as bip39 from "bip39";
import { derivePath } from "ed25519-hd-key";
import bs58 from "bs58";

class SolanaWallet {
  private keypair: Keypair;

  constructor(keypair: Keypair) {
    this.keypair = keypair;
  }

  // 生成新钱包
  static generate(): SolanaWallet {
    const keypair = Keypair.generate();
    return new SolanaWallet(keypair);
  }

  // 从私钥创建钱包
  static fromSecretKey(secretKey: Uint8Array): SolanaWallet {
    const keypair = Keypair.fromSecretKey(secretKey);
    return new SolanaWallet(keypair);
  }

  // 从助记词创建钱包
  static fromMnemonic(
    mnemonic: string,
    accountIndex: number = 0
  ): SolanaWallet {
    if (!bip39.validateMnemonic(mnemonic)) {
      throw new Error("Invalid mnemonic");
    }

    const seed = bip39.mnemonicToSeedSync(mnemonic);
    const derivationPath = `m/44'/501'/${accountIndex}'/0'`;
    const derivedSeed = derivePath(derivationPath, seed.toString("hex")).key;
    const keypair = Keypair.fromSeed(derivedSeed);

    return new SolanaWallet(keypair);
  }

  // 生成助记词
  static generateMnemonic(): string {
    return bip39.generateMnemonic();
  }

  // 获取地址
  getAddress(): string {
    return this.keypair.publicKey.toString();
  }

  // 获取公钥
  getPublicKey(): PublicKey {
    return this.keypair.publicKey;
  }

  // 获取私钥（Base58编码）
  getPrivateKey(): string {
    return bs58.encode(this.keypair.secretKey);
  }

  // 获取私钥（字节数组）
  getSecretKey(): Uint8Array {
    return this.keypair.secretKey;
  }

  // 获取 Keypair 对象
  getKeypair(): Keypair {
    return this.keypair;
  }

  // 验证地址格式
  static isValidAddress(address: string): boolean {
    try {
      const publicKey = new PublicKey(address);
      return PublicKey.isOnCurve(publicKey.toBytes());
    } catch {
      return false;
    }
  }

  // 生成程序派生地址
  static async findProgramAddress(
    seeds: (Buffer | Uint8Array)[],
    programId: PublicKey
  ): Promise<[PublicKey, number]> {
    return await PublicKey.findProgramAddress(seeds, programId);
  }

  // 生成关联代币账户地址
  static async getAssociatedTokenAddress(
    mint: PublicKey,
    owner: PublicKey,
    allowOwnerOffCurve: boolean = false
  ): Promise<PublicKey> {
    const { getAssociatedTokenAddress } = await import("@solana/spl-token");
    return await getAssociatedTokenAddress(mint, owner, allowOwnerOffCurve);
  }
}

// 使用示例
async function example() {
  // 生成新钱包
  const wallet = SolanaWallet.generate();
  console.log("Address:", wallet.getAddress());
  console.log("Private Key:", wallet.getPrivateKey());

  // 从助记词创建钱包
  const mnemonic = SolanaWallet.generateMnemonic();
  const walletFromMnemonic = SolanaWallet.fromMnemonic(mnemonic, 0);
  console.log("Mnemonic:", mnemonic);
  console.log("Address from mnemonic:", walletFromMnemonic.getAddress());

  // 验证地址
  const isValid = SolanaWallet.isValidAddress(wallet.getAddress());
  console.log("Address is valid:", isValid);

  // 生成 PDA
  const [pda, bump] = await SolanaWallet.findProgramAddress(
    [Buffer.from("metadata"), wallet.getPublicKey().toBuffer()],
    new PublicKey("metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s")
  );
  console.log("PDA:", pda.toString());
  console.log("Bump:", bump);
}
```

## 31. 完整的签名交易代码实现 (TypeScript)

```typescript
import {
  Connection,
  Transaction,
  SystemProgram,
  PublicKey,
  Keypair,
  sendAndConfirmTransaction,
  TransactionInstruction,
  ComputeBudgetProgram,
} from "@solana/web3.js";

class SolanaTransactionBuilder {
  private connection: Connection;
  private feePayer: Keypair;

  constructor(connection: Connection, feePayer: Keypair) {
    this.connection = connection;
    this.feePayer = feePayer;
  }

  // 创建并签名 SOL 转账交易
  async createAndSignSOLTransfer(
    toAddress: string,
    lamports: number,
    priorityFee?: number
  ): Promise<string> {
    const transaction = new Transaction();

    // 添加优先费用（可选）
    if (priorityFee) {
      const priorityFeeInstruction = ComputeBudgetProgram.setComputeUnitPrice({
        microLamports: priorityFee,
      });
      transaction.add(priorityFeeInstruction);
    }

    // 添加转账指令
    const transferInstruction = SystemProgram.transfer({
      fromPubkey: this.feePayer.publicKey,
      toPubkey: new PublicKey(toAddress),
      lamports: lamports,
    });
    transaction.add(transferInstruction);

    // 设置交易参数
    const { blockhash, lastValidBlockHeight } =
      await this.connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = this.feePayer.publicKey;

    // 签名交易
    transaction.sign(this.feePayer);

    // 发送交易
    const signature = await this.connection.sendRawTransaction(
      transaction.serialize(),
      {
        skipPreflight: false,
        preflightCommitment: "confirmed",
      }
    );

    // 确认交易
    await this.connection.confirmTransaction({
      signature,
      blockhash,
      lastValidBlockHeight,
    });

    return signature;
  }

  // 创建并签名 SPL Token 转账交易
  async createAndSignTokenTransfer(
    mintAddress: string,
    toAddress: string,
    amount: number,
    decimals: number
  ): Promise<string> {
    const {
      getOrCreateAssociatedTokenAccount,
      transfer,
      getAssociatedTokenAddress,
    } = await import("@solana/spl-token");

    const mint = new PublicKey(mintAddress);
    const toPublicKey = new PublicKey(toAddress);

    // 获取或创建关联代币账户
    const fromTokenAccount = await getOrCreateAssociatedTokenAccount(
      this.connection,
      this.feePayer,
      mint,
      this.feePayer.publicKey
    );

    const toTokenAccount = await getOrCreateAssociatedTokenAccount(
      this.connection,
      this.feePayer,
      mint,
      toPublicKey
    );

    // 执行转账
    const signature = await transfer(
      this.connection,
      this.feePayer,
      fromTokenAccount.address,
      toTokenAccount.address,
      this.feePayer.publicKey,
      amount * Math.pow(10, decimals)
    );

    return signature;
  }

  // 批量签名交易
  async createAndSignBatchTransaction(
    instructions: TransactionInstruction[]
  ): Promise<string> {
    const transaction = new Transaction();

    // 添加所有指令
    instructions.forEach((instruction) => {
      transaction.add(instruction);
    });

    // 设置交易参数
    const { blockhash, lastValidBlockHeight } =
      await this.connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = this.feePayer.publicKey;

    // 签名交易
    transaction.sign(this.feePayer);

    // 发送并确认交易
    const signature = await sendAndConfirmTransaction(
      this.connection,
      transaction,
      [this.feePayer],
      {
        commitment: "confirmed",
        preflightCommitment: "confirmed",
      }
    );

    return signature;
  }

  // 多重签名交易
  async createMultiSigTransaction(
    instructions: TransactionInstruction[],
    signers: Keypair[]
  ): Promise<string> {
    const transaction = new Transaction();

    instructions.forEach((instruction) => {
      transaction.add(instruction);
    });

    const { blockhash, lastValidBlockHeight } =
      await this.connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = this.feePayer.publicKey;

    // 多重签名
    transaction.sign(...signers);

    const signature = await this.connection.sendRawTransaction(
      transaction.serialize()
    );

    await this.connection.confirmTransaction({
      signature,
      blockhash,
      lastValidBlockHeight,
    });

    return signature;
  }

  // 估算交易费用
  async estimateTransactionFee(
    instructions: TransactionInstruction[]
  ): Promise<number> {
    const transaction = new Transaction();
    instructions.forEach((instruction) => {
      transaction.add(instruction);
    });

    const { blockhash } = await this.connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = this.feePayer.publicKey;

    // 模拟交易以获取费用
    const response = await this.connection.getFeeForMessage(
      transaction.compileMessage()
    );

    return response.value || 0;
  }
}

// 使用示例
async function transactionExample() {
  const connection = new Connection("https://api.devnet.solana.com");
  const feePayer = Keypair.generate();

  const txBuilder = new SolanaTransactionBuilder(connection, feePayer);

  try {
    // SOL 转账
    const solSignature = await txBuilder.createAndSignSOLTransfer(
      "target_address_here",
      **********, // 1 SOL
      1000 // 优先费用
    );
    console.log("SOL Transfer signature:", solSignature);

    // Token 转账
    const tokenSignature = await txBuilder.createAndSignTokenTransfer(
      "token_mint_address_here",
      "target_address_here",
      100, // 100 tokens
      6 // decimals
    );
    console.log("Token Transfer signature:", tokenSignature);
  } catch (error) {
    console.error("Transaction failed:", error);
  }
}
```

## 32. 手续费

### 手续费组成

1. **基础交易费**: 每个签名 5,000 lamports
2. **计算单元费**: 基于交易复杂度
3. **优先费**: 可选的额外费用，提高交易优先级
4. **租金**: 账户存储费用（可回收）

### 手续费计算公式

```
总费用 = (签名数量 × 5,000) + (计算单元 × 计算单元价格) + 优先费
```

## 33. 手续费如何计算？

```typescript
// 计算基础交易费
function calculateBaseFee(signatureCount: number): number {
  return signatureCount * 5000; // 每个签名 5,000 lamports
}

// 获取交易费用
async function getTransactionFee(
  connection: Connection,
  transaction: Transaction
): Promise<number> {
  const message = transaction.compileMessage();
  const response = await connection.getFeeForMessage(message);
  return response.value || 0;
}

// 计算计算单元费用
function calculateComputeUnitFee(
  computeUnits: number,
  microLamportsPerUnit: number
): number {
  return Math.ceil((computeUnits * microLamportsPerUnit) / 1_000_000);
}
```

## 34. 如何预测手续费？

```typescript
async function predictTransactionFee(
  connection: Connection,
  instructions: TransactionInstruction[],
  feePayer: PublicKey
): Promise<{
  baseFee: number;
  computeFee: number;
  totalFee: number;
}> {
  // 创建模拟交易
  const transaction = new Transaction();
  instructions.forEach((instruction) => {
    transaction.add(instruction);
  });

  const { blockhash } = await connection.getLatestBlockhash();
  transaction.recentBlockhash = blockhash;
  transaction.feePayer = feePayer;

  // 模拟交易
  const simulation = await connection.simulateTransaction(transaction);

  if (simulation.value.err) {
    throw new Error(`Simulation failed: ${simulation.value.err}`);
  }

  // 获取费用信息
  const message = transaction.compileMessage();
  const feeResponse = await connection.getFeeForMessage(message);
  const baseFee = feeResponse.value || 0;

  // 计算计算单元费用
  const computeUnits = simulation.value.unitsConsumed || 0;
  const computeFee = 0; // 默认计算单元价格为 0

  return {
    baseFee,
    computeFee,
    totalFee: baseFee + computeFee,
  };
}
```

## 35. 如何节省手续费？

### 优化策略

1. **批量交易**: 将多个操作合并到一个交易中
2. **优化计算单元**: 减少不必要的计算
3. **合理设置优先费**: 根据网络拥堵情况调整
4. **使用查找表**: 减少账户数量

```typescript
// 批量操作示例
async function batchTransfers(
  connection: Connection,
  feePayer: Keypair,
  transfers: Array<{ to: string; amount: number }>
): Promise<string> {
  const transaction = new Transaction();

  // 添加多个转账指令到同一交易
  transfers.forEach(({ to, amount }) => {
    const instruction = SystemProgram.transfer({
      fromPubkey: feePayer.publicKey,
      toPubkey: new PublicKey(to),
      lamports: amount,
    });
    transaction.add(instruction);
  });

  // 设置计算单元限制
  const computeLimitInstruction = ComputeBudgetProgram.setComputeUnitLimit({
    units: 300_000, // 根据实际需要调整
  });
  transaction.add(computeLimitInstruction);

  const { blockhash } = await connection.getLatestBlockhash();
  transaction.recentBlockhash = blockhash;
  transaction.feePayer = feePayer.publicKey;

  transaction.sign(feePayer);

  const signature = await connection.sendRawTransaction(
    transaction.serialize()
  );

  return signature;
}
```

## 36. 重要的 RPC 接口有哪些？

### 核心 RPC 接口

| 接口名称                | 方法 | 说明             | 主要参数                          | 返回值                   |
| ----------------------- | ---- | ---------------- | --------------------------------- | ------------------------ |
| getAccountInfo          | POST | 获取账户信息     | address, encoding, commitment     | 账户数据、余额、所有者等 |
| getBalance              | POST | 获取账户余额     | address, commitment               | lamports 余额            |
| getTransaction          | POST | 获取交易详情     | signature, encoding, commitment   | 交易完整信息             |
| sendTransaction         | POST | 发送交易         | transaction, encoding, options    | 交易签名                 |
| simulateTransaction     | POST | 模拟交易执行     | transaction, commitment, options  | 模拟结果、费用、日志     |
| getLatestBlockhash      | POST | 获取最新区块哈希 | commitment                        | 区块哈希、有效高度       |
| confirmTransaction      | POST | 确认交易状态     | signature, commitment             | 确认状态                 |
| getTokenAccountsByOwner | POST | 获取代币账户     | owner, mint/programId, commitment | 代币账户列表             |
| getProgramAccounts      | POST | 获取程序账户     | programId, filters, commitment    | 账户列表                 |
| getSlot                 | POST | 获取当前槽位     | commitment                        | 当前槽位号               |

### 详细接口说明

#### 1. getAccountInfo

```typescript
// 获取账户信息
const accountInfo = await connection.getAccountInfo(
  new PublicKey("account_address"),
  {
    encoding: "base64",
    commitment: "confirmed",
  }
);
```

**参数说明**:

- `address`: 账户地址 (string)
- `encoding`: 数据编码格式 ('base58' | 'base64' | 'jsonParsed')
- `commitment`: 确认级别 ('processed' | 'confirmed' | 'finalized')

**返回值**:

```json
{
  "lamports": **********,
  "data": ["base64_encoded_data", "base64"],
  "owner": "11111111111111111111111111111111",
  "executable": false,
  "rentEpoch": 361
}
```

#### 2. getBalance

```typescript
// 获取余额
const balance = await connection.getBalance(
  new PublicKey("account_address"),
  "confirmed"
);
```

**参数说明**:

- `address`: 账户地址 (string)
- `commitment`: 确认级别 (string)

**返回值**: lamports 数量 (number)

#### 3. getTransaction

```typescript
// 获取交易详情
const transaction = await connection.getTransaction("transaction_signature", {
  encoding: "json",
  commitment: "confirmed",
  maxSupportedTransactionVersion: 0,
});
```

**参数说明**:

- `signature`: 交易签名 (string)
- `encoding`: 编码格式 ('json' | 'base58' | 'base64')
- `commitment`: 确认级别 (string)
- `maxSupportedTransactionVersion`: 支持的交易版本 (number)

#### 4. sendTransaction

```typescript
// 发送交易
const signature = await connection.sendRawTransaction(transaction.serialize(), {
  skipPreflight: false,
  preflightCommitment: "confirmed",
  maxRetries: 3,
});
```

**参数说明**:

- `transaction`: 序列化的交易数据 (string)
- `skipPreflight`: 是否跳过预检 (boolean)
- `preflightCommitment`: 预检确认级别 (string)
- `maxRetries`: 最大重试次数 (number)

#### 5. getTokenAccountsByOwner

```typescript
// 获取用户的代币账户
const tokenAccounts = await connection.getTokenAccountsByOwner(
  new PublicKey("owner_address"),
  {
    mint: new PublicKey("token_mint_address"),
  },
  {
    encoding: "jsonParsed",
    commitment: "confirmed",
  }
);
```

**参数说明**:

- `owner`: 所有者地址 (string)
- `filter`: 过滤条件 (mint 或 programId)
- `encoding`: 编码格式 (string)
- `commitment`: 确认级别 (string)

### 订阅接口 (WebSocket)

#### 1. accountSubscribe

```typescript
// 订阅账户变化
const subscriptionId = connection.onAccountChange(
  new PublicKey("account_address"),
  (accountInfo, context) => {
    console.log("Account changed:", accountInfo);
  },
  "confirmed"
);
```

#### 2. logsSubscribe

```typescript
// 订阅程序日志
const subscriptionId = connection.onLogs(
  new PublicKey("program_id"),
  (logs, context) => {
    console.log("Program logs:", logs);
  },
  "confirmed"
);
```

#### 3. signatureSubscribe

```typescript
// 订阅交易确认
const subscriptionId = connection.onSignature(
  "transaction_signature",
  (result, context) => {
    console.log("Transaction confirmed:", result);
  },
  "confirmed"
);
```

## 37. 参考文档

### 官方文档

1. **Solana 官方文档**: https://solana.com/docs
2. **Solana 开发者文档**: https://solana.com/developers
3. **Solana RPC API**: https://solana.com/docs/rpc
4. **Solana Cookbook**: https://solana.com/developers/cookbook
5. **Anza 验证者文档**: https://docs.anza.xyz

### SDK 文档

1. **@solana/web3.js**: https://solana-labs.github.io/solana-web3.js/
2. **@solana/spl-token**: https://spl.solana.com/token
3. **Metaplex JS SDK**: https://docs.metaplex.com/js/
4. **Anchor Framework**: https://www.anchor-lang.com/

### 技术规范

1. **SPL Token 标准**: https://spl.solana.com/token
2. **Metaplex Token Metadata**: https://docs.metaplex.com/programs/token-metadata/
3. **Solana 程序库**: https://spl.solana.com/
4. **BPF 程序开发**: https://solana.com/docs/programs

### 社区资源

1. **Solana Stack Exchange**: https://solana.stackexchange.com/
2. **Solana Discord**: https://solana.com/discord
3. **Solana Forum**: https://forum.solana.com/
4. **Solana GitHub**: https://github.com/solana-labs

## 38. 官方 GitHub 仓库有哪些？

### 核心仓库

1. **Agave (Solana 验证者客户端)**: https://github.com/anza-xyz/agave
2. **Solana Web3.js**: https://github.com/anza-xyz/solana-web3.js
3. **Solana Program Library (SPL)**: https://github.com/solana-labs/solana-program-library
4. **Solana 官网**: https://github.com/solana-foundation/solana-com

### 开发工具

1. **Anchor Framework**: https://github.com/coral-xyz/anchor
2. **Solana CLI**: https://github.com/anza-xyz/agave/tree/master/cli
3. **Solana Explorer**: https://github.com/solana-labs/explorer
4. **Solana Beach Explorer**: https://github.com/solanabeach/solanabeach-frontend

### Metaplex 生态

1. **Metaplex Program Library**: https://github.com/metaplex-foundation/metaplex-program-library
2. **Metaplex JS SDK**: https://github.com/metaplex-foundation/js
3. **Sugar CLI**: https://github.com/metaplex-foundation/sugar
4. **Candy Machine**: https://github.com/metaplex-foundation/mpl-candy-machine

### 钱包和工具

1. **Phantom Wallet**: https://github.com/phantom-labs
2. **Solflare Wallet**: https://github.com/solflare-wallet
3. **Sollet Wallet**: https://github.com/project-serum/sol-wallet-adapter
4. **Wallet Adapter**: https://github.com/anza-xyz/wallet-adapter

### DeFi 项目

1. **Serum DEX**: https://github.com/project-serum/serum-dex
2. **Raydium**: https://github.com/raydium-io
3. **Orca**: https://github.com/orca-so
4. **Mango Markets**: https://github.com/blockworks-foundation/mango-v3

### 开发示例

1. **Solana Cookbook**: https://github.com/solana-developers/solana-cookbook
2. **Program Examples**: https://github.com/solana-developers/program-examples
3. **dApp Scaffold**: https://github.com/solana-labs/dapp-scaffold
4. **Token Creator**: https://github.com/solana-developers/token-creator

### 基础设施

1. **Geyser Plugin**: https://github.com/solana-labs/solana-accountsdb-plugin-postgres
2. **Solana Bigtable**: https://github.com/solana-labs/solana-bigtable
3. **Solana Metrics**: https://github.com/solana-labs/solana/tree/master/metrics
4. **Testnet Faucet**: https://github.com/solana-labs/solana/tree/master/faucet

### 监控和分析

1. **Solana Beach API**: https://github.com/solanabeach/solanabeach-api
2. **Solscan**: https://github.com/solscan-io
3. **Step Finance**: https://github.com/step-finance
4. **Solana FM**: https://github.com/solana-fm

这些仓库涵盖了 Solana 生态系统的各个方面，从核心协议到应用层，为开发者提供了丰富的资源和示例代码。
