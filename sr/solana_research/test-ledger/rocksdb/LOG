2025/07/13-10:26:06.630761 ********** RocksDB version: 8.1.1
2025/07/13-10:26:06.631272 ********** Compile date 2023-04-06 16:38:52
2025/07/13-10:26:06.631274 ********** DB SUMMARY
2025/07/13-10:26:06.631274 ********** DB Session ID:  ESGGJOHY6A47TING5YZR
2025/07/13-10:26:06.631298 ********** SST files in test-ledger/rocksdb dir, Total Num: 0, files: 
2025/07/13-10:26:06.631299 ********** Write Ahead Log file in test-ledger/rocksdb: 
2025/07/13-10:26:06.631300 **********                         Options.error_if_exists: 0
2025/07/13-10:26:06.631300 **********                       Options.create_if_missing: 1
2025/07/13-10:26:06.631301 **********                         Options.paranoid_checks: 1
2025/07/13-10:26:06.631301 **********             Options.flush_verify_memtable_count: 1
2025/07/13-10:26:06.631301 **********                               Options.track_and_verify_wals_in_manifest: 0
2025/07/13-10:26:06.631302 **********        Options.verify_sst_unique_id_in_manifest: 1
2025/07/13-10:26:06.631302 **********                                     Options.env: 0x104fb95b8
2025/07/13-10:26:06.631303 **********                                      Options.fs: PosixFileSystem
2025/07/13-10:26:06.631303 **********                                Options.info_log: 0x148f06318
2025/07/13-10:26:06.631303 **********                Options.max_file_opening_threads: 16
2025/07/13-10:26:06.631304 **********                              Options.statistics: 0x0
2025/07/13-10:26:06.631304 **********                               Options.use_fsync: 0
2025/07/13-10:26:06.631304 **********                       Options.max_log_file_size: 0
2025/07/13-10:26:06.631305 **********                  Options.max_manifest_file_size: 1073741824
2025/07/13-10:26:06.631305 **********                   Options.log_file_time_to_roll: 0
2025/07/13-10:26:06.631306 **********                       Options.keep_log_file_num: 1000
2025/07/13-10:26:06.631306 **********                    Options.recycle_log_file_num: 0
2025/07/13-10:26:06.631306 **********                         Options.allow_fallocate: 1
2025/07/13-10:26:06.631307 **********                        Options.allow_mmap_reads: 0
2025/07/13-10:26:06.631307 **********                       Options.allow_mmap_writes: 0
2025/07/13-10:26:06.631307 **********                        Options.use_direct_reads: 0
2025/07/13-10:26:06.631308 **********                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/13-10:26:06.631308 **********          Options.create_missing_column_families: 1
2025/07/13-10:26:06.631308 **********                              Options.db_log_dir: 
2025/07/13-10:26:06.631309 **********                                 Options.wal_dir: 
2025/07/13-10:26:06.631309 **********                Options.table_cache_numshardbits: 6
2025/07/13-10:26:06.631309 **********                         Options.WAL_ttl_seconds: 0
2025/07/13-10:26:06.631310 **********                       Options.WAL_size_limit_MB: 0
2025/07/13-10:26:06.631310 **********                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/13-10:26:06.631310 **********             Options.manifest_preallocation_size: 4194304
2025/07/13-10:26:06.631311 **********                     Options.is_fd_close_on_exec: 1
2025/07/13-10:26:06.631311 **********                   Options.advise_random_on_open: 1
2025/07/13-10:26:06.631311 **********                    Options.db_write_buffer_size: 0
2025/07/13-10:26:06.631312 **********                    Options.write_buffer_manager: 0x148f06500
2025/07/13-10:26:06.631312 **********         Options.access_hint_on_compaction_start: 1
2025/07/13-10:26:06.631312 **********           Options.random_access_max_buffer_size: 1048576
2025/07/13-10:26:06.631313 **********                      Options.use_adaptive_mutex: 0
2025/07/13-10:26:06.631313 **********                            Options.rate_limiter: 0x0
2025/07/13-10:26:06.631314 **********     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/13-10:26:06.631314 **********                       Options.wal_recovery_mode: 2
2025/07/13-10:26:06.631314 **********                  Options.enable_thread_tracking: 0
2025/07/13-10:26:06.631315 **********                  Options.enable_pipelined_write: 0
2025/07/13-10:26:06.631315 **********                  Options.unordered_write: 0
2025/07/13-10:26:06.631315 **********         Options.allow_concurrent_memtable_write: 1
2025/07/13-10:26:06.631316 **********      Options.enable_write_thread_adaptive_yield: 1
2025/07/13-10:26:06.631316 **********             Options.write_thread_max_yield_usec: 100
2025/07/13-10:26:06.631316 **********            Options.write_thread_slow_yield_usec: 3
2025/07/13-10:26:06.631317 **********                               Options.row_cache: None
2025/07/13-10:26:06.631317 **********                              Options.wal_filter: None
2025/07/13-10:26:06.631318 **********             Options.avoid_flush_during_recovery: 0
2025/07/13-10:26:06.631318 **********             Options.allow_ingest_behind: 0
2025/07/13-10:26:06.631318 **********             Options.two_write_queues: 0
2025/07/13-10:26:06.631319 **********             Options.manual_wal_flush: 0
2025/07/13-10:26:06.631319 **********             Options.wal_compression: 0
2025/07/13-10:26:06.631319 **********             Options.atomic_flush: 0
2025/07/13-10:26:06.631320 **********             Options.avoid_unnecessary_blocking_io: 0
2025/07/13-10:26:06.631320 **********                 Options.persist_stats_to_disk: 0
2025/07/13-10:26:06.631321 **********                 Options.write_dbid_to_manifest: 0
2025/07/13-10:26:06.631321 **********                 Options.log_readahead_size: 0
2025/07/13-10:26:06.631321 **********                 Options.file_checksum_gen_factory: Unknown
2025/07/13-10:26:06.631322 **********                 Options.best_efforts_recovery: 0
2025/07/13-10:26:06.631322 **********                Options.max_bgerror_resume_count: 2147483647
2025/07/13-10:26:06.631322 **********            Options.bgerror_resume_retry_interval: 1000000
2025/07/13-10:26:06.631323 **********             Options.allow_data_in_errors: 0
2025/07/13-10:26:06.631323 **********             Options.db_host_id: __hostname__
2025/07/13-10:26:06.631324 **********             Options.enforce_single_del_contracts: true
2025/07/13-10:26:06.631324 **********             Options.max_background_jobs: 10
2025/07/13-10:26:06.631324 **********             Options.max_background_compactions: -1
2025/07/13-10:26:06.631325 **********             Options.max_subcompactions: 1
2025/07/13-10:26:06.631325 **********             Options.avoid_flush_during_shutdown: 0
2025/07/13-10:26:06.631325 **********           Options.writable_file_max_buffer_size: 1048576
2025/07/13-10:26:06.631326 **********             Options.delayed_write_rate : 16777216
2025/07/13-10:26:06.631326 **********             Options.max_total_wal_size: 4294967296
2025/07/13-10:26:06.631327 **********             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/13-10:26:06.631327 **********                   Options.stats_dump_period_sec: 600
2025/07/13-10:26:06.631327 **********                 Options.stats_persist_period_sec: 600
2025/07/13-10:26:06.631328 **********                 Options.stats_history_buffer_size: 1048576
2025/07/13-10:26:06.631328 **********                          Options.max_open_files: -1
2025/07/13-10:26:06.631328 **********                          Options.bytes_per_sync: 0
2025/07/13-10:26:06.631329 **********                      Options.wal_bytes_per_sync: 0
2025/07/13-10:26:06.631329 **********                   Options.strict_bytes_per_sync: 0
2025/07/13-10:26:06.631329 **********       Options.compaction_readahead_size: 0
2025/07/13-10:26:06.631330 **********                  Options.max_background_flushes: -1
2025/07/13-10:26:06.631330 ********** Compression algorithms supported:
2025/07/13-10:26:06.631331 ********** 	kZSTD supported: 0
2025/07/13-10:26:06.631331 ********** 	kZlibCompression supported: 0
2025/07/13-10:26:06.631332 ********** 	kXpressCompression supported: 0
2025/07/13-10:26:06.631332 ********** 	kSnappyCompression supported: 0
2025/07/13-10:26:06.631332 ********** 	kZSTDNotFinalCompression supported: 0
2025/07/13-10:26:06.631333 ********** 	kLZ4HCCompression supported: 1
2025/07/13-10:26:06.631333 ********** 	kLZ4Compression supported: 1
2025/07/13-10:26:06.631333 ********** 	kBZip2Compression supported: 0
2025/07/13-10:26:06.631338 ********** Fast CRC32 supported: Supported on Arm64
2025/07/13-10:26:06.631338 ********** DMutex implementation: pthread_mutex_t
2025/07/13-10:26:06.631544 ********** [db/db_impl/db_impl_open.cc:315] Creating manifest 1 
2025/07/13-10:26:06.631765 ********** [db/version_set.cc:5662] Recovering from manifest file: test-ledger/rocksdb/MANIFEST-000001
2025/07/13-10:26:06.631818 ********** [db/column_family.cc:621] --------------- Options for column family [default]:
2025/07/13-10:26:06.631819 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/13-10:26:06.631820 **********           Options.merge_operator: None
2025/07/13-10:26:06.631820 **********        Options.compaction_filter: None
2025/07/13-10:26:06.631821 **********        Options.compaction_filter_factory: None
2025/07/13-10:26:06.631821 **********  Options.sst_partitioner_factory: None
2025/07/13-10:26:06.631821 **********         Options.memtable_factory: SkipListFactory
2025/07/13-10:26:06.631822 **********            Options.table_factory: BlockBasedTable
2025/07/13-10:26:06.631831 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x148e30bc0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x148e3a5e8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/13-10:26:06.631832 **********        Options.write_buffer_size: 67108864
2025/07/13-10:26:06.631833 **********  Options.max_write_buffer_number: 2
2025/07/13-10:26:06.631833 **********          Options.compression: NoCompression
2025/07/13-10:26:06.631834 **********                  Options.bottommost_compression: Disabled
2025/07/13-10:26:06.631834 **********       Options.prefix_extractor: nullptr
2025/07/13-10:26:06.631834 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/13-10:26:06.631835 **********             Options.num_levels: 7
2025/07/13-10:26:06.631835 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/13-10:26:06.631836 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/13-10:26:06.631836 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/13-10:26:06.631836 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/13-10:26:06.631837 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/13-10:26:06.631837 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/13-10:26:06.631838 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.631838 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.631838 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/13-10:26:06.631839 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/13-10:26:06.631839 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.631840 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.631840 **********            Options.compression_opts.window_bits: -14
2025/07/13-10:26:06.631840 **********                  Options.compression_opts.level: 32767
2025/07/13-10:26:06.631841 **********               Options.compression_opts.strategy: 0
2025/07/13-10:26:06.631841 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.631841 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.631842 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.631842 **********         Options.compression_opts.parallel_threads: 1
2025/07/13-10:26:06.631843 **********                  Options.compression_opts.enabled: false
2025/07/13-10:26:06.631843 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.631843 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.631844 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.631844 **********              Options.level0_stop_writes_trigger: 36
2025/07/13-10:26:06.631844 **********                   Options.target_file_size_base: 67108864
2025/07/13-10:26:06.631845 **********             Options.target_file_size_multiplier: 1
2025/07/13-10:26:06.631845 **********                Options.max_bytes_for_level_base: 268435456
2025/07/13-10:26:06.631845 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/13-10:26:06.631846 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.631846 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/13-10:26:06.631847 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/13-10:26:06.631847 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/13-10:26:06.631848 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/13-10:26:06.631848 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/13-10:26:06.631848 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/13-10:26:06.631849 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/13-10:26:06.631849 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.631849 **********                    Options.max_compaction_bytes: 1677721600
2025/07/13-10:26:06.631850 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.631850 **********                        Options.arena_block_size: 1048576
2025/07/13-10:26:06.631851 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.631851 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.631851 **********                Options.disable_auto_compactions: 0
2025/07/13-10:26:06.631852 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/13-10:26:06.631852 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/13-10:26:06.631853 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/13-10:26:06.631853 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/13-10:26:06.631853 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/13-10:26:06.631854 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/13-10:26:06.631854 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/13-10:26:06.631855 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/13-10:26:06.631855 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/13-10:26:06.631855 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/13-10:26:06.631857 **********                   Options.table_properties_collectors: 
2025/07/13-10:26:06.631857 **********                   Options.inplace_update_support: 0
2025/07/13-10:26:06.631858 **********                 Options.inplace_update_num_locks: 10000
2025/07/13-10:26:06.631858 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/13-10:26:06.631859 **********               Options.memtable_whole_key_filtering: 0
2025/07/13-10:26:06.631859 **********   Options.memtable_huge_page_size: 0
2025/07/13-10:26:06.631859 **********                           Options.bloom_locality: 0
2025/07/13-10:26:06.631860 **********                    Options.max_successive_merges: 0
2025/07/13-10:26:06.631860 **********                Options.optimize_filters_for_hits: 0
2025/07/13-10:26:06.631861 **********                Options.paranoid_file_checks: 0
2025/07/13-10:26:06.631861 **********                Options.force_consistency_checks: 1
2025/07/13-10:26:06.631861 **********                Options.report_bg_io_stats: 0
2025/07/13-10:26:06.631862 **********                               Options.ttl: 2592000
2025/07/13-10:26:06.631862 **********          Options.periodic_compaction_seconds: 0
2025/07/13-10:26:06.631862 **********  Options.preclude_last_level_data_seconds: 0
2025/07/13-10:26:06.631863 **********    Options.preserve_internal_time_seconds: 0
2025/07/13-10:26:06.631863 **********                       Options.enable_blob_files: false
2025/07/13-10:26:06.631863 **********                           Options.min_blob_size: 0
2025/07/13-10:26:06.631864 **********                          Options.blob_file_size: 268435456
2025/07/13-10:26:06.631864 **********                   Options.blob_compression_type: NoCompression
2025/07/13-10:26:06.631865 **********          Options.enable_blob_garbage_collection: false
2025/07/13-10:26:06.631865 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.631865 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.631866 **********          Options.blob_compaction_readahead_size: 0
2025/07/13-10:26:06.631866 **********                Options.blob_file_starting_level: 0
2025/07/13-10:26:06.631867 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.632220 ********** [db/version_set.cc:5713] Recovered from manifest file:test-ledger/rocksdb/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/13-10:26:06.632222 ********** [db/version_set.cc:5722] Column family [default] (ID 0), log number is 0
2025/07/13-10:26:06.632255 ********** [db/db_impl/db_impl_open.cc:537] DB ID: 6d9b9b8d-6d44-42c9-8298-b001ca30a6e0
2025/07/13-10:26:06.632333 ********** [db/version_set.cc:5180] Creating manifest 5
2025/07/13-10:26:06.632880 ********** [db/column_family.cc:621] --------------- Options for column family [meta]:
2025/07/13-10:26:06.632881 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/13-10:26:06.632881 **********           Options.merge_operator: None
2025/07/13-10:26:06.632882 **********        Options.compaction_filter: None
2025/07/13-10:26:06.632882 **********        Options.compaction_filter_factory: None
2025/07/13-10:26:06.632882 **********  Options.sst_partitioner_factory: None
2025/07/13-10:26:06.632883 **********         Options.memtable_factory: SkipListFactory
2025/07/13-10:26:06.632883 **********            Options.table_factory: BlockBasedTable
2025/07/13-10:26:06.632889 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14a0093a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14a0093f8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/13-10:26:06.632889 **********        Options.write_buffer_size: 268435456
2025/07/13-10:26:06.632890 **********  Options.max_write_buffer_number: 8
2025/07/13-10:26:06.632890 **********          Options.compression: NoCompression
2025/07/13-10:26:06.632890 **********                  Options.bottommost_compression: Disabled
2025/07/13-10:26:06.632891 **********       Options.prefix_extractor: nullptr
2025/07/13-10:26:06.632891 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/13-10:26:06.632891 **********             Options.num_levels: 7
2025/07/13-10:26:06.632892 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/13-10:26:06.632892 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/13-10:26:06.632893 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/13-10:26:06.632893 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/13-10:26:06.632893 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/13-10:26:06.632894 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/13-10:26:06.632894 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.632894 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.632895 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/13-10:26:06.632895 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/13-10:26:06.632896 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.632896 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.632896 **********            Options.compression_opts.window_bits: -14
2025/07/13-10:26:06.632897 **********                  Options.compression_opts.level: 32767
2025/07/13-10:26:06.632897 **********               Options.compression_opts.strategy: 0
2025/07/13-10:26:06.632897 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.632898 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.632898 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.632898 **********         Options.compression_opts.parallel_threads: 1
2025/07/13-10:26:06.632899 **********                  Options.compression_opts.enabled: false
2025/07/13-10:26:06.632899 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.632900 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.632900 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.632900 **********              Options.level0_stop_writes_trigger: 36
2025/07/13-10:26:06.632901 **********                   Options.target_file_size_base: 107374182
2025/07/13-10:26:06.632901 **********             Options.target_file_size_multiplier: 1
2025/07/13-10:26:06.632901 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.632902 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/13-10:26:06.632902 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.632903 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/13-10:26:06.632903 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/13-10:26:06.632903 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/13-10:26:06.632904 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/13-10:26:06.632904 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/13-10:26:06.632904 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/13-10:26:06.632905 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/13-10:26:06.632905 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.632905 **********                    Options.max_compaction_bytes: 2684354550
2025/07/13-10:26:06.632906 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.632906 **********                        Options.arena_block_size: 1048576
2025/07/13-10:26:06.632907 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.632907 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.632907 **********                Options.disable_auto_compactions: 0
2025/07/13-10:26:06.632908 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/13-10:26:06.632908 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/13-10:26:06.632908 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/13-10:26:06.632909 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/13-10:26:06.632909 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/13-10:26:06.632910 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/13-10:26:06.632910 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/13-10:26:06.632911 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/13-10:26:06.632911 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/13-10:26:06.632911 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/13-10:26:06.632912 **********                   Options.table_properties_collectors: 
2025/07/13-10:26:06.632912 **********                   Options.inplace_update_support: 0
2025/07/13-10:26:06.632912 **********                 Options.inplace_update_num_locks: 10000
2025/07/13-10:26:06.632913 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/13-10:26:06.632913 **********               Options.memtable_whole_key_filtering: 0
2025/07/13-10:26:06.632914 **********   Options.memtable_huge_page_size: 0
2025/07/13-10:26:06.632914 **********                           Options.bloom_locality: 0
2025/07/13-10:26:06.632914 **********                    Options.max_successive_merges: 0
2025/07/13-10:26:06.632915 **********                Options.optimize_filters_for_hits: 0
2025/07/13-10:26:06.632915 **********                Options.paranoid_file_checks: 0
2025/07/13-10:26:06.632916 **********                Options.force_consistency_checks: 1
2025/07/13-10:26:06.632916 **********                Options.report_bg_io_stats: 0
2025/07/13-10:26:06.632916 **********                               Options.ttl: 2592000
2025/07/13-10:26:06.632917 **********          Options.periodic_compaction_seconds: 0
2025/07/13-10:26:06.632917 **********  Options.preclude_last_level_data_seconds: 0
2025/07/13-10:26:06.632917 **********    Options.preserve_internal_time_seconds: 0
2025/07/13-10:26:06.632918 **********                       Options.enable_blob_files: false
2025/07/13-10:26:06.632918 **********                           Options.min_blob_size: 0
2025/07/13-10:26:06.632919 **********                          Options.blob_file_size: 268435456
2025/07/13-10:26:06.632919 **********                   Options.blob_compression_type: NoCompression
2025/07/13-10:26:06.632919 **********          Options.enable_blob_garbage_collection: false
2025/07/13-10:26:06.632920 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.632920 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.632921 **********          Options.blob_compaction_readahead_size: 0
2025/07/13-10:26:06.632921 **********                Options.blob_file_starting_level: 0
2025/07/13-10:26:06.632921 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.632945 ********** [db/db_impl/db_impl.cc:3200] Created column family [meta] (ID 1)
2025/07/13-10:26:06.633933 ********** [db/column_family.cc:621] --------------- Options for column family [dead_slots]:
2025/07/13-10:26:06.633934 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/13-10:26:06.633934 **********           Options.merge_operator: None
2025/07/13-10:26:06.633935 **********        Options.compaction_filter: None
2025/07/13-10:26:06.633935 **********        Options.compaction_filter_factory: None
2025/07/13-10:26:06.633935 **********  Options.sst_partitioner_factory: None
2025/07/13-10:26:06.633936 **********         Options.memtable_factory: SkipListFactory
2025/07/13-10:26:06.633936 **********            Options.table_factory: BlockBasedTable
2025/07/13-10:26:06.633943 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14a00a290)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14a00a2e8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/13-10:26:06.633944 **********        Options.write_buffer_size: 268435456
2025/07/13-10:26:06.633944 **********  Options.max_write_buffer_number: 8
2025/07/13-10:26:06.633945 **********          Options.compression: NoCompression
2025/07/13-10:26:06.633945 **********                  Options.bottommost_compression: Disabled
2025/07/13-10:26:06.633945 **********       Options.prefix_extractor: nullptr
2025/07/13-10:26:06.633946 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/13-10:26:06.633946 **********             Options.num_levels: 7
2025/07/13-10:26:06.633946 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/13-10:26:06.633947 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/13-10:26:06.633947 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/13-10:26:06.633947 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/13-10:26:06.633948 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/13-10:26:06.633948 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/13-10:26:06.633949 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.633949 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.633949 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/13-10:26:06.633950 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/13-10:26:06.633950 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.633950 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.633951 **********            Options.compression_opts.window_bits: -14
2025/07/13-10:26:06.633951 **********                  Options.compression_opts.level: 32767
2025/07/13-10:26:06.633952 **********               Options.compression_opts.strategy: 0
2025/07/13-10:26:06.633952 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.633952 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.633953 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.633953 **********         Options.compression_opts.parallel_threads: 1
2025/07/13-10:26:06.633953 **********                  Options.compression_opts.enabled: false
2025/07/13-10:26:06.633954 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.633954 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.633954 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.633955 **********              Options.level0_stop_writes_trigger: 36
2025/07/13-10:26:06.633955 **********                   Options.target_file_size_base: 107374182
2025/07/13-10:26:06.633956 **********             Options.target_file_size_multiplier: 1
2025/07/13-10:26:06.633956 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.633956 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/13-10:26:06.633957 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.633957 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/13-10:26:06.633957 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/13-10:26:06.633958 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/13-10:26:06.633958 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/13-10:26:06.633958 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/13-10:26:06.633959 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/13-10:26:06.633959 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/13-10:26:06.633960 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.633960 **********                    Options.max_compaction_bytes: 2684354550
2025/07/13-10:26:06.633960 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.633961 **********                        Options.arena_block_size: 1048576
2025/07/13-10:26:06.633961 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.633961 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.633962 **********                Options.disable_auto_compactions: 0
2025/07/13-10:26:06.633962 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/13-10:26:06.633962 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/13-10:26:06.633963 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/13-10:26:06.633963 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/13-10:26:06.633964 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/13-10:26:06.633964 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/13-10:26:06.633964 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/13-10:26:06.633965 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/13-10:26:06.633965 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/13-10:26:06.633966 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/13-10:26:06.633966 **********                   Options.table_properties_collectors: 
2025/07/13-10:26:06.633966 **********                   Options.inplace_update_support: 0
2025/07/13-10:26:06.633967 **********                 Options.inplace_update_num_locks: 10000
2025/07/13-10:26:06.633967 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/13-10:26:06.633967 **********               Options.memtable_whole_key_filtering: 0
2025/07/13-10:26:06.633968 **********   Options.memtable_huge_page_size: 0
2025/07/13-10:26:06.633968 **********                           Options.bloom_locality: 0
2025/07/13-10:26:06.633968 **********                    Options.max_successive_merges: 0
2025/07/13-10:26:06.633969 **********                Options.optimize_filters_for_hits: 0
2025/07/13-10:26:06.633969 **********                Options.paranoid_file_checks: 0
2025/07/13-10:26:06.633970 **********                Options.force_consistency_checks: 1
2025/07/13-10:26:06.633970 **********                Options.report_bg_io_stats: 0
2025/07/13-10:26:06.633970 **********                               Options.ttl: 2592000
2025/07/13-10:26:06.633971 **********          Options.periodic_compaction_seconds: 0
2025/07/13-10:26:06.633971 **********  Options.preclude_last_level_data_seconds: 0
2025/07/13-10:26:06.633971 **********    Options.preserve_internal_time_seconds: 0
2025/07/13-10:26:06.633972 **********                       Options.enable_blob_files: false
2025/07/13-10:26:06.633972 **********                           Options.min_blob_size: 0
2025/07/13-10:26:06.633972 **********                          Options.blob_file_size: 268435456
2025/07/13-10:26:06.633973 **********                   Options.blob_compression_type: NoCompression
2025/07/13-10:26:06.633973 **********          Options.enable_blob_garbage_collection: false
2025/07/13-10:26:06.633973 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.633974 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.633974 **********          Options.blob_compaction_readahead_size: 0
2025/07/13-10:26:06.633975 **********                Options.blob_file_starting_level: 0
2025/07/13-10:26:06.633975 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.633987 ********** [db/db_impl/db_impl.cc:3200] Created column family [dead_slots] (ID 2)
2025/07/13-10:26:06.634808 ********** [db/column_family.cc:621] --------------- Options for column family [duplicate_slots]:
2025/07/13-10:26:06.634809 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/13-10:26:06.634809 **********           Options.merge_operator: None
2025/07/13-10:26:06.634809 **********        Options.compaction_filter: None
2025/07/13-10:26:06.634810 **********        Options.compaction_filter_factory: None
2025/07/13-10:26:06.634810 **********  Options.sst_partitioner_factory: None
2025/07/13-10:26:06.634810 **********         Options.memtable_factory: SkipListFactory
2025/07/13-10:26:06.634811 **********            Options.table_factory: BlockBasedTable
2025/07/13-10:26:06.634816 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14a00b190)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14a00b1e8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/13-10:26:06.634816 **********        Options.write_buffer_size: 268435456
2025/07/13-10:26:06.634817 **********  Options.max_write_buffer_number: 8
2025/07/13-10:26:06.634817 **********          Options.compression: NoCompression
2025/07/13-10:26:06.634818 **********                  Options.bottommost_compression: Disabled
2025/07/13-10:26:06.634818 **********       Options.prefix_extractor: nullptr
2025/07/13-10:26:06.634818 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/13-10:26:06.634819 **********             Options.num_levels: 7
2025/07/13-10:26:06.634819 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/13-10:26:06.634819 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/13-10:26:06.634820 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/13-10:26:06.634820 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/13-10:26:06.634820 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/13-10:26:06.634821 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/13-10:26:06.634821 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.634822 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.634822 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/13-10:26:06.634822 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/13-10:26:06.634823 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.634823 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.634823 **********            Options.compression_opts.window_bits: -14
2025/07/13-10:26:06.634824 **********                  Options.compression_opts.level: 32767
2025/07/13-10:26:06.634824 **********               Options.compression_opts.strategy: 0
2025/07/13-10:26:06.634824 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.634825 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.634825 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.634826 **********         Options.compression_opts.parallel_threads: 1
2025/07/13-10:26:06.634826 **********                  Options.compression_opts.enabled: false
2025/07/13-10:26:06.634826 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.634827 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.634827 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.634827 **********              Options.level0_stop_writes_trigger: 36
2025/07/13-10:26:06.634828 **********                   Options.target_file_size_base: 107374182
2025/07/13-10:26:06.634828 **********             Options.target_file_size_multiplier: 1
2025/07/13-10:26:06.634828 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.634829 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/13-10:26:06.634829 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.634830 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/13-10:26:06.634830 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/13-10:26:06.634830 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/13-10:26:06.634831 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/13-10:26:06.634831 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/13-10:26:06.634831 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/13-10:26:06.634832 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/13-10:26:06.634832 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.634832 **********                    Options.max_compaction_bytes: 2684354550
2025/07/13-10:26:06.634833 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.634833 **********                        Options.arena_block_size: 1048576
2025/07/13-10:26:06.634834 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.634834 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.634834 **********                Options.disable_auto_compactions: 0
2025/07/13-10:26:06.634835 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/13-10:26:06.634835 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/13-10:26:06.634835 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/13-10:26:06.634836 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/13-10:26:06.634836 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/13-10:26:06.634836 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/13-10:26:06.634837 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/13-10:26:06.634837 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/13-10:26:06.634838 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/13-10:26:06.634838 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/13-10:26:06.634838 **********                   Options.table_properties_collectors: 
2025/07/13-10:26:06.634839 **********                   Options.inplace_update_support: 0
2025/07/13-10:26:06.634839 **********                 Options.inplace_update_num_locks: 10000
2025/07/13-10:26:06.634840 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/13-10:26:06.634840 **********               Options.memtable_whole_key_filtering: 0
2025/07/13-10:26:06.634840 **********   Options.memtable_huge_page_size: 0
2025/07/13-10:26:06.634841 **********                           Options.bloom_locality: 0
2025/07/13-10:26:06.634841 **********                    Options.max_successive_merges: 0
2025/07/13-10:26:06.634841 **********                Options.optimize_filters_for_hits: 0
2025/07/13-10:26:06.634842 **********                Options.paranoid_file_checks: 0
2025/07/13-10:26:06.634842 **********                Options.force_consistency_checks: 1
2025/07/13-10:26:06.634843 **********                Options.report_bg_io_stats: 0
2025/07/13-10:26:06.634843 **********                               Options.ttl: 2592000
2025/07/13-10:26:06.634843 **********          Options.periodic_compaction_seconds: 0
2025/07/13-10:26:06.634844 **********  Options.preclude_last_level_data_seconds: 0
2025/07/13-10:26:06.634844 **********    Options.preserve_internal_time_seconds: 0
2025/07/13-10:26:06.634844 **********                       Options.enable_blob_files: false
2025/07/13-10:26:06.634845 **********                           Options.min_blob_size: 0
2025/07/13-10:26:06.634845 **********                          Options.blob_file_size: 268435456
2025/07/13-10:26:06.634845 **********                   Options.blob_compression_type: NoCompression
2025/07/13-10:26:06.634846 **********          Options.enable_blob_garbage_collection: false
2025/07/13-10:26:06.634846 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.634847 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.634847 **********          Options.blob_compaction_readahead_size: 0
2025/07/13-10:26:06.634847 **********                Options.blob_file_starting_level: 0
2025/07/13-10:26:06.634848 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.634859 ********** [db/db_impl/db_impl.cc:3200] Created column family [duplicate_slots] (ID 3)
2025/07/13-10:26:06.635789 ********** [db/column_family.cc:621] --------------- Options for column family [erasure_meta]:
2025/07/13-10:26:06.635790 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/13-10:26:06.635790 **********           Options.merge_operator: None
2025/07/13-10:26:06.635791 **********        Options.compaction_filter: None
2025/07/13-10:26:06.635791 **********        Options.compaction_filter_factory: None
2025/07/13-10:26:06.635791 **********  Options.sst_partitioner_factory: None
2025/07/13-10:26:06.635792 **********         Options.memtable_factory: SkipListFactory
2025/07/13-10:26:06.635792 **********            Options.table_factory: BlockBasedTable
2025/07/13-10:26:06.635798 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14a00c090)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14a00c0e8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/13-10:26:06.635798 **********        Options.write_buffer_size: 268435456
2025/07/13-10:26:06.635798 **********  Options.max_write_buffer_number: 8
2025/07/13-10:26:06.635799 **********          Options.compression: NoCompression
2025/07/13-10:26:06.635799 **********                  Options.bottommost_compression: Disabled
2025/07/13-10:26:06.635800 **********       Options.prefix_extractor: nullptr
2025/07/13-10:26:06.635800 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/13-10:26:06.635800 **********             Options.num_levels: 7
2025/07/13-10:26:06.635801 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/13-10:26:06.635801 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/13-10:26:06.635801 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/13-10:26:06.635802 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/13-10:26:06.635802 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/13-10:26:06.635802 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/13-10:26:06.635803 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.635803 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.635803 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/13-10:26:06.635804 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/13-10:26:06.635804 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.635805 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.635805 **********            Options.compression_opts.window_bits: -14
2025/07/13-10:26:06.635805 **********                  Options.compression_opts.level: 32767
2025/07/13-10:26:06.635806 **********               Options.compression_opts.strategy: 0
2025/07/13-10:26:06.635806 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.635806 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.635807 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.635807 **********         Options.compression_opts.parallel_threads: 1
2025/07/13-10:26:06.635807 **********                  Options.compression_opts.enabled: false
2025/07/13-10:26:06.635808 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.635808 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.635808 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.635809 **********              Options.level0_stop_writes_trigger: 36
2025/07/13-10:26:06.635809 **********                   Options.target_file_size_base: 107374182
2025/07/13-10:26:06.635810 **********             Options.target_file_size_multiplier: 1
2025/07/13-10:26:06.635810 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.635810 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/13-10:26:06.635811 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.635811 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/13-10:26:06.635811 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/13-10:26:06.635812 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/13-10:26:06.635812 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/13-10:26:06.635812 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/13-10:26:06.635813 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/13-10:26:06.635813 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/13-10:26:06.635814 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.635814 **********                    Options.max_compaction_bytes: 2684354550
2025/07/13-10:26:06.635814 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.635815 **********                        Options.arena_block_size: 1048576
2025/07/13-10:26:06.635815 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.635815 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.635816 **********                Options.disable_auto_compactions: 0
2025/07/13-10:26:06.635816 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/13-10:26:06.635816 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/13-10:26:06.635817 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/13-10:26:06.635817 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/13-10:26:06.635817 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/13-10:26:06.635818 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/13-10:26:06.635818 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/13-10:26:06.635819 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/13-10:26:06.635819 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/13-10:26:06.635819 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/13-10:26:06.635820 **********                   Options.table_properties_collectors: 
2025/07/13-10:26:06.635820 **********                   Options.inplace_update_support: 0
2025/07/13-10:26:06.635821 **********                 Options.inplace_update_num_locks: 10000
2025/07/13-10:26:06.635821 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/13-10:26:06.635821 **********               Options.memtable_whole_key_filtering: 0
2025/07/13-10:26:06.635822 **********   Options.memtable_huge_page_size: 0
2025/07/13-10:26:06.635822 **********                           Options.bloom_locality: 0
2025/07/13-10:26:06.635822 **********                    Options.max_successive_merges: 0
2025/07/13-10:26:06.635823 **********                Options.optimize_filters_for_hits: 0
2025/07/13-10:26:06.635823 **********                Options.paranoid_file_checks: 0
2025/07/13-10:26:06.635823 **********                Options.force_consistency_checks: 1
2025/07/13-10:26:06.635824 **********                Options.report_bg_io_stats: 0
2025/07/13-10:26:06.635824 **********                               Options.ttl: 2592000
2025/07/13-10:26:06.635824 **********          Options.periodic_compaction_seconds: 0
2025/07/13-10:26:06.635825 **********  Options.preclude_last_level_data_seconds: 0
2025/07/13-10:26:06.635825 **********    Options.preserve_internal_time_seconds: 0
2025/07/13-10:26:06.635825 **********                       Options.enable_blob_files: false
2025/07/13-10:26:06.635826 **********                           Options.min_blob_size: 0
2025/07/13-10:26:06.635826 **********                          Options.blob_file_size: 268435456
2025/07/13-10:26:06.635827 **********                   Options.blob_compression_type: NoCompression
2025/07/13-10:26:06.635827 **********          Options.enable_blob_garbage_collection: false
2025/07/13-10:26:06.635827 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.635828 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.635828 **********          Options.blob_compaction_readahead_size: 0
2025/07/13-10:26:06.635828 **********                Options.blob_file_starting_level: 0
2025/07/13-10:26:06.635829 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.635839 ********** [db/db_impl/db_impl.cc:3200] Created column family [erasure_meta] (ID 4)
2025/07/13-10:26:06.637529 ********** [db/column_family.cc:621] --------------- Options for column family [orphans]:
2025/07/13-10:26:06.637531 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/13-10:26:06.637532 **********           Options.merge_operator: None
2025/07/13-10:26:06.637532 **********        Options.compaction_filter: None
2025/07/13-10:26:06.637533 **********        Options.compaction_filter_factory: None
2025/07/13-10:26:06.637533 **********  Options.sst_partitioner_factory: None
2025/07/13-10:26:06.637534 **********         Options.memtable_factory: SkipListFactory
2025/07/13-10:26:06.637534 **********            Options.table_factory: BlockBasedTable
2025/07/13-10:26:06.637541 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14a00cf90)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14a00cfe8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/13-10:26:06.637544 **********        Options.write_buffer_size: 268435456
2025/07/13-10:26:06.637544 **********  Options.max_write_buffer_number: 8
2025/07/13-10:26:06.637544 **********          Options.compression: NoCompression
2025/07/13-10:26:06.637545 **********                  Options.bottommost_compression: Disabled
2025/07/13-10:26:06.637545 **********       Options.prefix_extractor: nullptr
2025/07/13-10:26:06.637545 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/13-10:26:06.637546 **********             Options.num_levels: 7
2025/07/13-10:26:06.637546 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/13-10:26:06.637547 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/13-10:26:06.637547 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/13-10:26:06.637547 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/13-10:26:06.637548 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/13-10:26:06.637548 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/13-10:26:06.637549 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.637549 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.637549 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/13-10:26:06.637550 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/13-10:26:06.637550 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.637550 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.637551 **********            Options.compression_opts.window_bits: -14
2025/07/13-10:26:06.637551 **********                  Options.compression_opts.level: 32767
2025/07/13-10:26:06.637552 **********               Options.compression_opts.strategy: 0
2025/07/13-10:26:06.637552 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.637552 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.637553 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.637553 **********         Options.compression_opts.parallel_threads: 1
2025/07/13-10:26:06.637553 **********                  Options.compression_opts.enabled: false
2025/07/13-10:26:06.637554 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.637554 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.637555 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.637555 **********              Options.level0_stop_writes_trigger: 36
2025/07/13-10:26:06.637555 **********                   Options.target_file_size_base: 107374182
2025/07/13-10:26:06.637556 **********             Options.target_file_size_multiplier: 1
2025/07/13-10:26:06.637556 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.637556 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/13-10:26:06.637557 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.637557 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/13-10:26:06.637558 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/13-10:26:06.637558 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/13-10:26:06.637558 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/13-10:26:06.637559 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/13-10:26:06.637559 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/13-10:26:06.637559 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/13-10:26:06.637560 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.637560 **********                    Options.max_compaction_bytes: 2684354550
2025/07/13-10:26:06.637560 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.637561 **********                        Options.arena_block_size: 1048576
2025/07/13-10:26:06.637561 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.637562 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.637562 **********                Options.disable_auto_compactions: 0
2025/07/13-10:26:06.637562 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/13-10:26:06.637563 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/13-10:26:06.637563 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/13-10:26:06.637564 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/13-10:26:06.637564 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/13-10:26:06.637564 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/13-10:26:06.637565 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/13-10:26:06.637565 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/13-10:26:06.637566 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/13-10:26:06.637566 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/13-10:26:06.637567 **********                   Options.table_properties_collectors: 
2025/07/13-10:26:06.637567 **********                   Options.inplace_update_support: 0
2025/07/13-10:26:06.637567 **********                 Options.inplace_update_num_locks: 10000
2025/07/13-10:26:06.637568 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/13-10:26:06.637568 **********               Options.memtable_whole_key_filtering: 0
2025/07/13-10:26:06.637568 **********   Options.memtable_huge_page_size: 0
2025/07/13-10:26:06.637569 **********                           Options.bloom_locality: 0
2025/07/13-10:26:06.637569 **********                    Options.max_successive_merges: 0
2025/07/13-10:26:06.637570 **********                Options.optimize_filters_for_hits: 0
2025/07/13-10:26:06.637570 **********                Options.paranoid_file_checks: 0
2025/07/13-10:26:06.637570 **********                Options.force_consistency_checks: 1
2025/07/13-10:26:06.637571 **********                Options.report_bg_io_stats: 0
2025/07/13-10:26:06.637571 **********                               Options.ttl: 2592000
2025/07/13-10:26:06.637571 **********          Options.periodic_compaction_seconds: 0
2025/07/13-10:26:06.637572 **********  Options.preclude_last_level_data_seconds: 0
2025/07/13-10:26:06.637572 **********    Options.preserve_internal_time_seconds: 0
2025/07/13-10:26:06.637572 **********                       Options.enable_blob_files: false
2025/07/13-10:26:06.637573 **********                           Options.min_blob_size: 0
2025/07/13-10:26:06.637573 **********                          Options.blob_file_size: 268435456
2025/07/13-10:26:06.637574 **********                   Options.blob_compression_type: NoCompression
2025/07/13-10:26:06.637574 **********          Options.enable_blob_garbage_collection: false
2025/07/13-10:26:06.637574 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.637575 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.637575 **********          Options.blob_compaction_readahead_size: 0
2025/07/13-10:26:06.637575 **********                Options.blob_file_starting_level: 0
2025/07/13-10:26:06.637576 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.637592 ********** [db/db_impl/db_impl.cc:3200] Created column family [orphans] (ID 5)
2025/07/13-10:26:06.638890 ********** [db/column_family.cc:621] --------------- Options for column family [bank_hashes]:
2025/07/13-10:26:06.638891 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/13-10:26:06.638891 **********           Options.merge_operator: None
2025/07/13-10:26:06.638892 **********        Options.compaction_filter: None
2025/07/13-10:26:06.638892 **********        Options.compaction_filter_factory: None
2025/07/13-10:26:06.638892 **********  Options.sst_partitioner_factory: None
2025/07/13-10:26:06.638893 **********         Options.memtable_factory: SkipListFactory
2025/07/13-10:26:06.638893 **********            Options.table_factory: BlockBasedTable
2025/07/13-10:26:06.638899 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14a00de90)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14a00dee8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/13-10:26:06.638900 **********        Options.write_buffer_size: 268435456
2025/07/13-10:26:06.638900 **********  Options.max_write_buffer_number: 8
2025/07/13-10:26:06.638901 **********          Options.compression: NoCompression
2025/07/13-10:26:06.638901 **********                  Options.bottommost_compression: Disabled
2025/07/13-10:26:06.638901 **********       Options.prefix_extractor: nullptr
2025/07/13-10:26:06.638902 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/13-10:26:06.638902 **********             Options.num_levels: 7
2025/07/13-10:26:06.638902 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/13-10:26:06.638903 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/13-10:26:06.638903 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/13-10:26:06.638904 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/13-10:26:06.638904 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/13-10:26:06.638904 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/13-10:26:06.638905 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.638905 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.638905 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/13-10:26:06.638906 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/13-10:26:06.638906 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.638907 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.638907 **********            Options.compression_opts.window_bits: -14
2025/07/13-10:26:06.638907 **********                  Options.compression_opts.level: 32767
2025/07/13-10:26:06.638908 **********               Options.compression_opts.strategy: 0
2025/07/13-10:26:06.638908 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.638909 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.638909 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.638909 **********         Options.compression_opts.parallel_threads: 1
2025/07/13-10:26:06.638910 **********                  Options.compression_opts.enabled: false
2025/07/13-10:26:06.638910 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.638910 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.638911 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.638911 **********              Options.level0_stop_writes_trigger: 36
2025/07/13-10:26:06.638911 **********                   Options.target_file_size_base: 107374182
2025/07/13-10:26:06.638912 **********             Options.target_file_size_multiplier: 1
2025/07/13-10:26:06.638912 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.638913 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/13-10:26:06.638913 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.638913 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/13-10:26:06.638914 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/13-10:26:06.638914 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/13-10:26:06.638915 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/13-10:26:06.638915 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/13-10:26:06.638915 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/13-10:26:06.638916 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/13-10:26:06.638916 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.638916 **********                    Options.max_compaction_bytes: 2684354550
2025/07/13-10:26:06.638917 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.638917 **********                        Options.arena_block_size: 1048576
2025/07/13-10:26:06.638918 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.638918 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.638918 **********                Options.disable_auto_compactions: 0
2025/07/13-10:26:06.638919 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/13-10:26:06.638919 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/13-10:26:06.638919 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/13-10:26:06.638920 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/13-10:26:06.638920 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/13-10:26:06.638921 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/13-10:26:06.638921 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/13-10:26:06.638921 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/13-10:26:06.638922 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/13-10:26:06.638922 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/13-10:26:06.638923 **********                   Options.table_properties_collectors: 
2025/07/13-10:26:06.638923 **********                   Options.inplace_update_support: 0
2025/07/13-10:26:06.638923 **********                 Options.inplace_update_num_locks: 10000
2025/07/13-10:26:06.638924 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/13-10:26:06.638924 **********               Options.memtable_whole_key_filtering: 0
2025/07/13-10:26:06.638925 **********   Options.memtable_huge_page_size: 0
2025/07/13-10:26:06.638925 **********                           Options.bloom_locality: 0
2025/07/13-10:26:06.638925 **********                    Options.max_successive_merges: 0
2025/07/13-10:26:06.638926 **********                Options.optimize_filters_for_hits: 0
2025/07/13-10:26:06.638926 **********                Options.paranoid_file_checks: 0
2025/07/13-10:26:06.638926 **********                Options.force_consistency_checks: 1
2025/07/13-10:26:06.638927 **********                Options.report_bg_io_stats: 0
2025/07/13-10:26:06.638927 **********                               Options.ttl: 2592000
2025/07/13-10:26:06.638927 **********          Options.periodic_compaction_seconds: 0
2025/07/13-10:26:06.638928 **********  Options.preclude_last_level_data_seconds: 0
2025/07/13-10:26:06.638928 **********    Options.preserve_internal_time_seconds: 0
2025/07/13-10:26:06.638929 **********                       Options.enable_blob_files: false
2025/07/13-10:26:06.638929 **********                           Options.min_blob_size: 0
2025/07/13-10:26:06.638929 **********                          Options.blob_file_size: 268435456
2025/07/13-10:26:06.638930 **********                   Options.blob_compression_type: NoCompression
2025/07/13-10:26:06.638930 **********          Options.enable_blob_garbage_collection: false
2025/07/13-10:26:06.638930 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.638931 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.638931 **********          Options.blob_compaction_readahead_size: 0
2025/07/13-10:26:06.638932 **********                Options.blob_file_starting_level: 0
2025/07/13-10:26:06.638932 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.638945 ********** [db/db_impl/db_impl.cc:3200] Created column family [bank_hashes] (ID 6)
2025/07/13-10:26:06.640599 ********** [db/column_family.cc:621] --------------- Options for column family [root]:
2025/07/13-10:26:06.640600 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/13-10:26:06.640601 **********           Options.merge_operator: None
2025/07/13-10:26:06.640601 **********        Options.compaction_filter: None
2025/07/13-10:26:06.640602 **********        Options.compaction_filter_factory: None
2025/07/13-10:26:06.640602 **********  Options.sst_partitioner_factory: None
2025/07/13-10:26:06.640603 **********         Options.memtable_factory: SkipListFactory
2025/07/13-10:26:06.640603 **********            Options.table_factory: BlockBasedTable
2025/07/13-10:26:06.640613 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14a00ed90)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14a00ede8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/13-10:26:06.640614 **********        Options.write_buffer_size: 268435456
2025/07/13-10:26:06.640614 **********  Options.max_write_buffer_number: 8
2025/07/13-10:26:06.640615 **********          Options.compression: NoCompression
2025/07/13-10:26:06.640615 **********                  Options.bottommost_compression: Disabled
2025/07/13-10:26:06.640616 **********       Options.prefix_extractor: nullptr
2025/07/13-10:26:06.640616 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/13-10:26:06.640616 **********             Options.num_levels: 7
2025/07/13-10:26:06.640617 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/13-10:26:06.640617 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/13-10:26:06.640617 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/13-10:26:06.640618 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/13-10:26:06.640618 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/13-10:26:06.640619 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/13-10:26:06.640619 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.640619 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.640620 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/13-10:26:06.640620 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/13-10:26:06.640620 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.640621 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.640621 **********            Options.compression_opts.window_bits: -14
2025/07/13-10:26:06.640622 **********                  Options.compression_opts.level: 32767
2025/07/13-10:26:06.640622 **********               Options.compression_opts.strategy: 0
2025/07/13-10:26:06.640622 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.640623 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.640623 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.640623 **********         Options.compression_opts.parallel_threads: 1
2025/07/13-10:26:06.640624 **********                  Options.compression_opts.enabled: false
2025/07/13-10:26:06.640624 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.640625 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.640625 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.640625 **********              Options.level0_stop_writes_trigger: 36
2025/07/13-10:26:06.640626 **********                   Options.target_file_size_base: 107374182
2025/07/13-10:26:06.640626 **********             Options.target_file_size_multiplier: 1
2025/07/13-10:26:06.640626 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.640627 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/13-10:26:06.640627 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.640628 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/13-10:26:06.640628 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/13-10:26:06.640628 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/13-10:26:06.640629 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/13-10:26:06.640629 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/13-10:26:06.640630 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/13-10:26:06.640630 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/13-10:26:06.640630 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.640631 **********                    Options.max_compaction_bytes: 2684354550
2025/07/13-10:26:06.640631 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.640631 **********                        Options.arena_block_size: 1048576
2025/07/13-10:26:06.640632 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.640632 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.640633 **********                Options.disable_auto_compactions: 0
2025/07/13-10:26:06.640633 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/13-10:26:06.640633 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/13-10:26:06.640634 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/13-10:26:06.640634 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/13-10:26:06.640634 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/13-10:26:06.640635 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/13-10:26:06.640635 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/13-10:26:06.640636 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/13-10:26:06.640636 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/13-10:26:06.640637 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/13-10:26:06.640637 **********                   Options.table_properties_collectors: 
2025/07/13-10:26:06.640637 **********                   Options.inplace_update_support: 0
2025/07/13-10:26:06.640638 **********                 Options.inplace_update_num_locks: 10000
2025/07/13-10:26:06.640638 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/13-10:26:06.640638 **********               Options.memtable_whole_key_filtering: 0
2025/07/13-10:26:06.640639 **********   Options.memtable_huge_page_size: 0
2025/07/13-10:26:06.640639 **********                           Options.bloom_locality: 0
2025/07/13-10:26:06.640640 **********                    Options.max_successive_merges: 0
2025/07/13-10:26:06.640640 **********                Options.optimize_filters_for_hits: 0
2025/07/13-10:26:06.640640 **********                Options.paranoid_file_checks: 0
2025/07/13-10:26:06.640641 **********                Options.force_consistency_checks: 1
2025/07/13-10:26:06.640641 **********                Options.report_bg_io_stats: 0
2025/07/13-10:26:06.640641 **********                               Options.ttl: 2592000
2025/07/13-10:26:06.640642 **********          Options.periodic_compaction_seconds: 0
2025/07/13-10:26:06.640642 **********  Options.preclude_last_level_data_seconds: 0
2025/07/13-10:26:06.640642 **********    Options.preserve_internal_time_seconds: 0
2025/07/13-10:26:06.640643 **********                       Options.enable_blob_files: false
2025/07/13-10:26:06.640643 **********                           Options.min_blob_size: 0
2025/07/13-10:26:06.640644 **********                          Options.blob_file_size: 268435456
2025/07/13-10:26:06.640644 **********                   Options.blob_compression_type: NoCompression
2025/07/13-10:26:06.640644 **********          Options.enable_blob_garbage_collection: false
2025/07/13-10:26:06.640645 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.640645 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.640645 **********          Options.blob_compaction_readahead_size: 0
2025/07/13-10:26:06.640646 **********                Options.blob_file_starting_level: 0
2025/07/13-10:26:06.640646 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.640659 ********** [db/db_impl/db_impl.cc:3200] Created column family [root] (ID 7)
2025/07/13-10:26:06.643131 ********** [db/column_family.cc:621] --------------- Options for column family [index]:
2025/07/13-10:26:06.643134 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/13-10:26:06.643135 **********           Options.merge_operator: None
2025/07/13-10:26:06.643135 **********        Options.compaction_filter: None
2025/07/13-10:26:06.643136 **********        Options.compaction_filter_factory: None
2025/07/13-10:26:06.643136 **********  Options.sst_partitioner_factory: None
2025/07/13-10:26:06.643136 **********         Options.memtable_factory: SkipListFactory
2025/07/13-10:26:06.643137 **********            Options.table_factory: BlockBasedTable
2025/07/13-10:26:06.643145 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14a00fc90)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14a00fce8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/13-10:26:06.643147 **********        Options.write_buffer_size: 268435456
2025/07/13-10:26:06.643147 **********  Options.max_write_buffer_number: 8
2025/07/13-10:26:06.643147 **********          Options.compression: NoCompression
2025/07/13-10:26:06.643148 **********                  Options.bottommost_compression: Disabled
2025/07/13-10:26:06.643148 **********       Options.prefix_extractor: nullptr
2025/07/13-10:26:06.643149 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/13-10:26:06.643149 **********             Options.num_levels: 7
2025/07/13-10:26:06.643150 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/13-10:26:06.643150 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/13-10:26:06.643150 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/13-10:26:06.643151 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/13-10:26:06.643151 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/13-10:26:06.643152 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/13-10:26:06.643152 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.643152 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.643153 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/13-10:26:06.643153 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/13-10:26:06.643154 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.643154 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.643154 **********            Options.compression_opts.window_bits: -14
2025/07/13-10:26:06.643155 **********                  Options.compression_opts.level: 32767
2025/07/13-10:26:06.643155 **********               Options.compression_opts.strategy: 0
2025/07/13-10:26:06.643156 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.643156 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.643156 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.643157 **********         Options.compression_opts.parallel_threads: 1
2025/07/13-10:26:06.643157 **********                  Options.compression_opts.enabled: false
2025/07/13-10:26:06.643158 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.643158 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.643158 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.643159 **********              Options.level0_stop_writes_trigger: 36
2025/07/13-10:26:06.643159 **********                   Options.target_file_size_base: 107374182
2025/07/13-10:26:06.643160 **********             Options.target_file_size_multiplier: 1
2025/07/13-10:26:06.643160 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.643160 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/13-10:26:06.643161 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.643161 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/13-10:26:06.643162 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/13-10:26:06.643162 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/13-10:26:06.643162 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/13-10:26:06.643163 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/13-10:26:06.643163 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/13-10:26:06.643164 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/13-10:26:06.643164 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.643164 **********                    Options.max_compaction_bytes: 2684354550
2025/07/13-10:26:06.643165 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.643165 **********                        Options.arena_block_size: 1048576
2025/07/13-10:26:06.643165 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.643166 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.643166 **********                Options.disable_auto_compactions: 0
2025/07/13-10:26:06.643167 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/13-10:26:06.643167 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/13-10:26:06.643168 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/13-10:26:06.643168 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/13-10:26:06.643168 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/13-10:26:06.643169 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/13-10:26:06.643169 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/13-10:26:06.643170 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/13-10:26:06.643170 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/13-10:26:06.643170 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/13-10:26:06.643171 **********                   Options.table_properties_collectors: 
2025/07/13-10:26:06.643172 **********                   Options.inplace_update_support: 0
2025/07/13-10:26:06.643172 **********                 Options.inplace_update_num_locks: 10000
2025/07/13-10:26:06.643172 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/13-10:26:06.643173 **********               Options.memtable_whole_key_filtering: 0
2025/07/13-10:26:06.643173 **********   Options.memtable_huge_page_size: 0
2025/07/13-10:26:06.643174 **********                           Options.bloom_locality: 0
2025/07/13-10:26:06.643174 **********                    Options.max_successive_merges: 0
2025/07/13-10:26:06.643174 **********                Options.optimize_filters_for_hits: 0
2025/07/13-10:26:06.643175 **********                Options.paranoid_file_checks: 0
2025/07/13-10:26:06.643175 **********                Options.force_consistency_checks: 1
2025/07/13-10:26:06.643176 **********                Options.report_bg_io_stats: 0
2025/07/13-10:26:06.643176 **********                               Options.ttl: 2592000
2025/07/13-10:26:06.643176 **********          Options.periodic_compaction_seconds: 0
2025/07/13-10:26:06.643177 **********  Options.preclude_last_level_data_seconds: 0
2025/07/13-10:26:06.643177 **********    Options.preserve_internal_time_seconds: 0
2025/07/13-10:26:06.643177 **********                       Options.enable_blob_files: false
2025/07/13-10:26:06.643178 **********                           Options.min_blob_size: 0
2025/07/13-10:26:06.643178 **********                          Options.blob_file_size: 268435456
2025/07/13-10:26:06.643179 **********                   Options.blob_compression_type: NoCompression
2025/07/13-10:26:06.643179 **********          Options.enable_blob_garbage_collection: false
2025/07/13-10:26:06.643179 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.643180 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.643180 **********          Options.blob_compaction_readahead_size: 0
2025/07/13-10:26:06.643181 **********                Options.blob_file_starting_level: 0
2025/07/13-10:26:06.643181 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.643198 ********** [db/db_impl/db_impl.cc:3200] Created column family [index] (ID 8)
2025/07/13-10:26:06.645121 ********** [db/column_family.cc:621] --------------- Options for column family [data_shred]:
2025/07/13-10:26:06.645123 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/13-10:26:06.645123 **********           Options.merge_operator: None
2025/07/13-10:26:06.645124 **********        Options.compaction_filter: None
2025/07/13-10:26:06.645124 **********        Options.compaction_filter_factory: None
2025/07/13-10:26:06.645124 **********  Options.sst_partitioner_factory: None
2025/07/13-10:26:06.645125 **********         Options.memtable_factory: SkipListFactory
2025/07/13-10:26:06.645125 **********            Options.table_factory: BlockBasedTable
2025/07/13-10:26:06.645130 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14a007300)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14a007358
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/13-10:26:06.645132 **********        Options.write_buffer_size: 268435456
2025/07/13-10:26:06.645133 **********  Options.max_write_buffer_number: 8
2025/07/13-10:26:06.645133 **********          Options.compression: NoCompression
2025/07/13-10:26:06.645133 **********                  Options.bottommost_compression: Disabled
2025/07/13-10:26:06.645134 **********       Options.prefix_extractor: nullptr
2025/07/13-10:26:06.645134 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/13-10:26:06.645134 **********             Options.num_levels: 7
2025/07/13-10:26:06.645135 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/13-10:26:06.645135 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/13-10:26:06.645136 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/13-10:26:06.645136 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/13-10:26:06.645136 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/13-10:26:06.645137 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/13-10:26:06.645137 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.645137 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.645138 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/13-10:26:06.645138 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/13-10:26:06.645139 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.645139 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.645139 **********            Options.compression_opts.window_bits: -14
2025/07/13-10:26:06.645140 **********                  Options.compression_opts.level: 32767
2025/07/13-10:26:06.645140 **********               Options.compression_opts.strategy: 0
2025/07/13-10:26:06.645140 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/13-10:26:06.645141 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/13-10:26:06.645141 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/13-10:26:06.645142 **********         Options.compression_opts.parallel_threads: 1
2025/07/13-10:26:06.645142 **********                  Options.compression_opts.enabled: false
2025/07/13-10:26:06.645142 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/13-10:26:06.645143 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.645143 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.645143 **********              Options.level0_stop_writes_trigger: 36
2025/07/13-10:26:06.645144 **********                   Options.target_file_size_base: 107374182
2025/07/13-10:26:06.645144 **********             Options.target_file_size_multiplier: 1
2025/07/13-10:26:06.645145 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.645145 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/13-10:26:06.645145 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.645146 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/13-10:26:06.645146 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/13-10:26:06.645147 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/13-10:26:06.645147 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/13-10:26:06.645147 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/13-10:26:06.645148 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/13-10:26:06.645148 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/13-10:26:06.645148 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.645149 **********                    Options.max_compaction_bytes: 2684354550
2025/07/13-10:26:06.645149 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.645149 **********                        Options.arena_block_size: 1048576
2025/07/13-10:26:06.645150 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.645150 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.645151 **********                Options.disable_auto_compactions: 0
2025/07/13-10:26:06.645151 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/13-10:26:06.645151 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/13-10:26:06.645152 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/13-10:26:06.645152 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/13-10:26:06.645153 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/13-10:26:06.645153 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/13-10:26:06.645153 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/13-10:26:06.645154 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/13-10:26:06.645154 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/13-10:26:06.645155 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/13-10:26:06.645155 **********                   Options.table_properties_collectors: 
2025/07/13-10:26:06.645155 **********                   Options.inplace_update_support: 0
2025/07/13-10:26:06.645156 **********                 Options.inplace_update_num_locks: 10000
2025/07/13-10:26:06.645156 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/13-10:26:06.645157 **********               Options.memtable_whole_key_filtering: 0
2025/07/13-10:26:06.645157 **********   Options.memtable_huge_page_size: 0
2025/07/13-10:26:06.645157 **********                           Options.bloom_locality: 0
2025/07/13-10:26:06.645158 **********                    Options.max_successive_merges: 0
2025/07/13-10:26:06.645158 **********                Options.optimize_filters_for_hits: 0
2025/07/13-10:26:06.645158 **********                Options.paranoid_file_checks: 0
2025/07/13-10:26:06.645159 **********                Options.force_consistency_checks: 1
2025/07/13-10:26:06.645159 **********                Options.report_bg_io_stats: 0
2025/07/13-10:26:06.645159 **********                               Options.ttl: 2592000
2025/07/13-10:26:06.645160 **********          Options.periodic_compaction_seconds: 0
2025/07/13-10:26:06.645160 **********  Options.preclude_last_level_data_seconds: 0
2025/07/13-10:26:06.645160 **********    Options.preserve_internal_time_seconds: 0
2025/07/13-10:26:06.645161 **********                       Options.enable_blob_files: false
2025/07/13-10:26:06.645161 **********                           Options.min_blob_size: 0
2025/07/13-10:26:06.645162 **********                          Options.blob_file_size: 268435456
2025/07/13-10:26:06.645162 **********                   Options.blob_compression_type: NoCompression
2025/07/13-10:26:06.645162 **********          Options.enable_blob_garbage_collection: false
2025/07/13-10:26:06.645163 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.645163 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.645163 **********          Options.blob_compaction_readahead_size: 0
2025/07/13-10:26:06.645164 **********                Options.blob_file_starting_level: 0
2025/07/13-10:26:06.645164 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.645176 ********** [db/db_impl/db_impl.cc:3200] Created column family [data_shred] (ID 9)
2025/07/13-10:26:06.647098 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.647116 ********** [db/db_impl/db_impl.cc:3200] Created column family [code_shred] (ID 10)
2025/07/13-10:26:06.649011 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.649023 ********** [db/db_impl/db_impl.cc:3200] Created column family [transaction_status] (ID 11)
2025/07/13-10:26:06.651178 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.651189 ********** [db/db_impl/db_impl.cc:3200] Created column family [address_signatures] (ID 12)
2025/07/13-10:26:06.653240 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.653251 ********** [db/db_impl/db_impl.cc:3200] Created column family [transaction_memos] (ID 13)
2025/07/13-10:26:06.655743 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.655753 ********** [db/db_impl/db_impl.cc:3200] Created column family [transaction_status_index] (ID 14)
2025/07/13-10:26:06.658413 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.658431 ********** [db/db_impl/db_impl.cc:3200] Created column family [rewards] (ID 15)
2025/07/13-10:26:06.661225 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.661237 ********** [db/db_impl/db_impl.cc:3200] Created column family [blocktime] (ID 16)
2025/07/13-10:26:06.663941 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.663955 ********** [db/db_impl/db_impl.cc:3200] Created column family [perf_samples] (ID 17)
2025/07/13-10:26:06.666859 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.666873 ********** [db/db_impl/db_impl.cc:3200] Created column family [block_height] (ID 18)
2025/07/13-10:26:06.669893 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.669905 ********** [db/db_impl/db_impl.cc:3200] Created column family [program_costs] (ID 19)
2025/07/13-10:26:06.673025 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.673034 ********** [db/db_impl/db_impl.cc:3200] Created column family [optimistic_slots] (ID 20)
2025/07/13-10:26:06.676342 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/13-10:26:06.676360 ********** [db/db_impl/db_impl.cc:3200] Created column family [merkle_root_meta] (ID 21)
2025/07/13-10:26:06.683744 ********** [db/db_impl/db_impl_open.cc:1977] SstFileManager instance 0x148f07710
2025/07/13-10:26:06.683766 ********** DB pointer 0x14a843e00
2025/07/13-10:26:06.684149 6174027776 [db/db_impl/db_impl.cc:1085] ------- DUMPING STATS -------
2025/07/13-10:26:06.684152 6174027776 [db/db_impl/db_impl.cc:1086] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0, 
** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x148e3a5e8#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 9e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a0093f8#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [meta] **

** Compaction Stats [dead_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [dead_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a00a2e8#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [dead_slots] **

** Compaction Stats [duplicate_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [duplicate_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a00b1e8#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [duplicate_slots] **

** Compaction Stats [erasure_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [erasure_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a00c0e8#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [erasure_meta] **

** Compaction Stats [orphans] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [orphans] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a00cfe8#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [orphans] **

** Compaction Stats [bank_hashes] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [bank_hashes] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a00dee8#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [bank_hashes] **

** Compaction Stats [root] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [root] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a00ede8#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [root] **

** Compaction Stats [index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a00fce8#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [index] **

** Compaction Stats [data_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [data_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a007358#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [data_shred] **

** Compaction Stats [code_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [code_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a008508#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [code_shred] **

** Compaction Stats [transaction_status] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a010be8#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status] **

** Compaction Stats [address_signatures] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [address_signatures] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a011bb8#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [address_signatures] **

** Compaction Stats [transaction_memos] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_memos] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a012b88#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_memos] **

** Compaction Stats [transaction_status_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a013b58#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status_index] **

** Compaction Stats [rewards] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [rewards] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a014a68#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [rewards] **

** Compaction Stats [blocktime] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [blocktime] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a015968#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [blocktime] **

** Compaction Stats [perf_samples] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [perf_samples] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a016868#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [perf_samples] **

** Compaction Stats [block_height] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [block_height] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a017768#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [block_height] **

** Compaction Stats [program_costs] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [program_costs] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a018668#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [program_costs] **

** Compaction Stats [optimistic_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [optimistic_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a019568#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [optimistic_slots] **

** Compaction Stats [merkle_root_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [merkle_root_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14a01a468#68947 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [merkle_root_meta] **
2025/07/13-10:26:06.687646 ********** [db/db_impl/db_impl.cc:1187] SetOptions() on column family [transaction_status], inputs:
2025/07/13-10:26:06.687647 ********** [db/db_impl/db_impl.cc:1190] periodic_compaction_seconds: 86400
2025/07/13-10:26:06.687647 ********** [db/db_impl/db_impl.cc:1194] [transaction_status] SetOptions() succeeded
2025/07/13-10:26:06.687648 ********** [options/cf_options.cc:1004]                         write_buffer_size: 268435456
2025/07/13-10:26:06.687648 ********** [options/cf_options.cc:1006]                   max_write_buffer_number: 8
2025/07/13-10:26:06.687649 ********** [options/cf_options.cc:1009]                          arena_block_size: 1048576
2025/07/13-10:26:06.687649 ********** [options/cf_options.cc:1011]               memtable_prefix_bloom_ratio: 0.000000
2025/07/13-10:26:06.687650 ********** [options/cf_options.cc:1013]               memtable_whole_key_filtering: 0
2025/07/13-10:26:06.687650 ********** [options/cf_options.cc:1016]                   memtable_huge_page_size: 0
2025/07/13-10:26:06.687650 ********** [options/cf_options.cc:1019]                     max_successive_merges: 0
2025/07/13-10:26:06.687651 ********** [options/cf_options.cc:1022]                  inplace_update_num_locks: 10000
2025/07/13-10:26:06.687651 ********** [options/cf_options.cc:1026]                          prefix_extractor: nullptr
2025/07/13-10:26:06.687652 ********** [options/cf_options.cc:1028]                  disable_auto_compactions: 0
2025/07/13-10:26:06.687652 ********** [options/cf_options.cc:1030]       soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.687652 ********** [options/cf_options.cc:1032]       hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.687653 ********** [options/cf_options.cc:1034]        level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.687653 ********** [options/cf_options.cc:1036]            level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.687653 ********** [options/cf_options.cc:1038]                level0_stop_writes_trigger: 36
2025/07/13-10:26:06.687654 ********** [options/cf_options.cc:1040]                      max_compaction_bytes: 2684354550
2025/07/13-10:26:06.687654 ********** [options/cf_options.cc:1042]     ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.687655 ********** [options/cf_options.cc:1044]                     target_file_size_base: 107374182
2025/07/13-10:26:06.687655 ********** [options/cf_options.cc:1046]               target_file_size_multiplier: 1
2025/07/13-10:26:06.687655 ********** [options/cf_options.cc:1048]                  max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.687656 ********** [options/cf_options.cc:1050]            max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.687656 ********** [options/cf_options.cc:1052]                                       ttl: 2592000
2025/07/13-10:26:06.687657 ********** [options/cf_options.cc:1054]               periodic_compaction_seconds: 86400
2025/07/13-10:26:06.687657 ********** [options/cf_options.cc:1068] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/07/13-10:26:06.687658 ********** [options/cf_options.cc:1070]         max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.687658 ********** [options/cf_options.cc:1072]          check_flush_compaction_key_order: 1
2025/07/13-10:26:06.687658 ********** [options/cf_options.cc:1074]                      paranoid_file_checks: 0
2025/07/13-10:26:06.687659 ********** [options/cf_options.cc:1076]                        report_bg_io_stats: 0
2025/07/13-10:26:06.687659 ********** [options/cf_options.cc:1078]                               compression: 0
2025/07/13-10:26:06.687660 ********** [options/cf_options.cc:1081]                        experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.687660 ********** [options/cf_options.cc:1085] compaction_options_universal.size_ratio : 1
2025/07/13-10:26:06.687660 ********** [options/cf_options.cc:1087] compaction_options_universal.min_merge_width : 2
2025/07/13-10:26:06.687661 ********** [options/cf_options.cc:1089] compaction_options_universal.max_merge_width : -1
2025/07/13-10:26:06.687661 ********** [options/cf_options.cc:1092] compaction_options_universal.max_size_amplification_percent : 200
2025/07/13-10:26:06.687662 ********** [options/cf_options.cc:1095] compaction_options_universal.compression_size_percent : -1
2025/07/13-10:26:06.687662 ********** [options/cf_options.cc:1097] compaction_options_universal.stop_style : 1
2025/07/13-10:26:06.687662 ********** [options/cf_options.cc:1100] compaction_options_universal.allow_trivial_move : 0
2025/07/13-10:26:06.687663 ********** [options/cf_options.cc:1102] compaction_options_universal.incremental        : 0
2025/07/13-10:26:06.687663 ********** [options/cf_options.cc:1106] compaction_options_fifo.max_table_files_size : 1073741824
2025/07/13-10:26:06.687663 ********** [options/cf_options.cc:1108] compaction_options_fifo.allow_compaction : 0
2025/07/13-10:26:06.687664 ********** [options/cf_options.cc:1112]                         enable_blob_files: false
2025/07/13-10:26:06.687664 ********** [options/cf_options.cc:1114]                             min_blob_size: 0
2025/07/13-10:26:06.687665 ********** [options/cf_options.cc:1116]                            blob_file_size: 268435456
2025/07/13-10:26:06.687665 ********** [options/cf_options.cc:1118]                     blob_compression_type: NoCompression
2025/07/13-10:26:06.687665 ********** [options/cf_options.cc:1120]            enable_blob_garbage_collection: false
2025/07/13-10:26:06.687666 ********** [options/cf_options.cc:1122]        blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.687666 ********** [options/cf_options.cc:1124]   blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.687667 ********** [options/cf_options.cc:1126]            blob_compaction_readahead_size: 0
2025/07/13-10:26:06.687667 ********** [options/cf_options.cc:1128]                  blob_file_starting_level: 0
2025/07/13-10:26:06.687667 ********** [options/cf_options.cc:1132]                    prepopulate_blob_cache: disable
2025/07/13-10:26:06.687668 ********** [options/cf_options.cc:1134]                    last_level_temperature: 0
2025/07/13-10:26:06.691327 ********** [db/db_impl/db_impl.cc:1187] SetOptions() on column family [address_signatures], inputs:
2025/07/13-10:26:06.691328 ********** [db/db_impl/db_impl.cc:1190] periodic_compaction_seconds: 86400
2025/07/13-10:26:06.691329 ********** [db/db_impl/db_impl.cc:1194] [address_signatures] SetOptions() succeeded
2025/07/13-10:26:06.691329 ********** [options/cf_options.cc:1004]                         write_buffer_size: 268435456
2025/07/13-10:26:06.691330 ********** [options/cf_options.cc:1006]                   max_write_buffer_number: 8
2025/07/13-10:26:06.691330 ********** [options/cf_options.cc:1009]                          arena_block_size: 1048576
2025/07/13-10:26:06.691330 ********** [options/cf_options.cc:1011]               memtable_prefix_bloom_ratio: 0.000000
2025/07/13-10:26:06.691331 ********** [options/cf_options.cc:1013]               memtable_whole_key_filtering: 0
2025/07/13-10:26:06.691331 ********** [options/cf_options.cc:1016]                   memtable_huge_page_size: 0
2025/07/13-10:26:06.691332 ********** [options/cf_options.cc:1019]                     max_successive_merges: 0
2025/07/13-10:26:06.691332 ********** [options/cf_options.cc:1022]                  inplace_update_num_locks: 10000
2025/07/13-10:26:06.691332 ********** [options/cf_options.cc:1026]                          prefix_extractor: nullptr
2025/07/13-10:26:06.691333 ********** [options/cf_options.cc:1028]                  disable_auto_compactions: 0
2025/07/13-10:26:06.691333 ********** [options/cf_options.cc:1030]       soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.691334 ********** [options/cf_options.cc:1032]       hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.691334 ********** [options/cf_options.cc:1034]        level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.691334 ********** [options/cf_options.cc:1036]            level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.691335 ********** [options/cf_options.cc:1038]                level0_stop_writes_trigger: 36
2025/07/13-10:26:06.691335 ********** [options/cf_options.cc:1040]                      max_compaction_bytes: 2684354550
2025/07/13-10:26:06.691336 ********** [options/cf_options.cc:1042]     ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.691336 ********** [options/cf_options.cc:1044]                     target_file_size_base: 107374182
2025/07/13-10:26:06.691336 ********** [options/cf_options.cc:1046]               target_file_size_multiplier: 1
2025/07/13-10:26:06.691337 ********** [options/cf_options.cc:1048]                  max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.691337 ********** [options/cf_options.cc:1050]            max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.691338 ********** [options/cf_options.cc:1052]                                       ttl: 2592000
2025/07/13-10:26:06.691338 ********** [options/cf_options.cc:1054]               periodic_compaction_seconds: 86400
2025/07/13-10:26:06.691339 ********** [options/cf_options.cc:1068] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/07/13-10:26:06.691339 ********** [options/cf_options.cc:1070]         max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.691339 ********** [options/cf_options.cc:1072]          check_flush_compaction_key_order: 1
2025/07/13-10:26:06.691340 ********** [options/cf_options.cc:1074]                      paranoid_file_checks: 0
2025/07/13-10:26:06.691340 ********** [options/cf_options.cc:1076]                        report_bg_io_stats: 0
2025/07/13-10:26:06.691341 ********** [options/cf_options.cc:1078]                               compression: 0
2025/07/13-10:26:06.691341 ********** [options/cf_options.cc:1081]                        experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.691341 ********** [options/cf_options.cc:1085] compaction_options_universal.size_ratio : 1
2025/07/13-10:26:06.691342 ********** [options/cf_options.cc:1087] compaction_options_universal.min_merge_width : 2
2025/07/13-10:26:06.691342 ********** [options/cf_options.cc:1089] compaction_options_universal.max_merge_width : -1
2025/07/13-10:26:06.691343 ********** [options/cf_options.cc:1092] compaction_options_universal.max_size_amplification_percent : 200
2025/07/13-10:26:06.691343 ********** [options/cf_options.cc:1095] compaction_options_universal.compression_size_percent : -1
2025/07/13-10:26:06.691343 ********** [options/cf_options.cc:1097] compaction_options_universal.stop_style : 1
2025/07/13-10:26:06.691344 ********** [options/cf_options.cc:1100] compaction_options_universal.allow_trivial_move : 0
2025/07/13-10:26:06.691344 ********** [options/cf_options.cc:1102] compaction_options_universal.incremental        : 0
2025/07/13-10:26:06.691344 ********** [options/cf_options.cc:1106] compaction_options_fifo.max_table_files_size : 1073741824
2025/07/13-10:26:06.691345 ********** [options/cf_options.cc:1108] compaction_options_fifo.allow_compaction : 0
2025/07/13-10:26:06.691345 ********** [options/cf_options.cc:1112]                         enable_blob_files: false
2025/07/13-10:26:06.691346 ********** [options/cf_options.cc:1114]                             min_blob_size: 0
2025/07/13-10:26:06.691346 ********** [options/cf_options.cc:1116]                            blob_file_size: 268435456
2025/07/13-10:26:06.691346 ********** [options/cf_options.cc:1118]                     blob_compression_type: NoCompression
2025/07/13-10:26:06.691347 ********** [options/cf_options.cc:1120]            enable_blob_garbage_collection: false
2025/07/13-10:26:06.691347 ********** [options/cf_options.cc:1122]        blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.691348 ********** [options/cf_options.cc:1124]   blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.691348 ********** [options/cf_options.cc:1126]            blob_compaction_readahead_size: 0
2025/07/13-10:26:06.691348 ********** [options/cf_options.cc:1128]                  blob_file_starting_level: 0
2025/07/13-10:26:06.691349 ********** [options/cf_options.cc:1132]                    prepopulate_blob_cache: disable
2025/07/13-10:26:06.691349 ********** [options/cf_options.cc:1134]                    last_level_temperature: 0
2025/07/13-10:26:06.695036 ********** [db/db_impl/db_impl.cc:1187] SetOptions() on column family [transaction_memos], inputs:
2025/07/13-10:26:06.695037 ********** [db/db_impl/db_impl.cc:1190] periodic_compaction_seconds: 86400
2025/07/13-10:26:06.695037 ********** [db/db_impl/db_impl.cc:1194] [transaction_memos] SetOptions() succeeded
2025/07/13-10:26:06.695038 ********** [options/cf_options.cc:1004]                         write_buffer_size: 268435456
2025/07/13-10:26:06.695038 ********** [options/cf_options.cc:1006]                   max_write_buffer_number: 8
2025/07/13-10:26:06.695039 ********** [options/cf_options.cc:1009]                          arena_block_size: 1048576
2025/07/13-10:26:06.695039 ********** [options/cf_options.cc:1011]               memtable_prefix_bloom_ratio: 0.000000
2025/07/13-10:26:06.695039 ********** [options/cf_options.cc:1013]               memtable_whole_key_filtering: 0
2025/07/13-10:26:06.695040 ********** [options/cf_options.cc:1016]                   memtable_huge_page_size: 0
2025/07/13-10:26:06.695040 ********** [options/cf_options.cc:1019]                     max_successive_merges: 0
2025/07/13-10:26:06.695041 ********** [options/cf_options.cc:1022]                  inplace_update_num_locks: 10000
2025/07/13-10:26:06.695041 ********** [options/cf_options.cc:1026]                          prefix_extractor: nullptr
2025/07/13-10:26:06.695041 ********** [options/cf_options.cc:1028]                  disable_auto_compactions: 0
2025/07/13-10:26:06.695042 ********** [options/cf_options.cc:1030]       soft_pending_compaction_bytes_limit: 68719476736
2025/07/13-10:26:06.695042 ********** [options/cf_options.cc:1032]       hard_pending_compaction_bytes_limit: 274877906944
2025/07/13-10:26:06.695042 ********** [options/cf_options.cc:1034]        level0_file_num_compaction_trigger: 4
2025/07/13-10:26:06.695043 ********** [options/cf_options.cc:1036]            level0_slowdown_writes_trigger: 20
2025/07/13-10:26:06.695043 ********** [options/cf_options.cc:1038]                level0_stop_writes_trigger: 36
2025/07/13-10:26:06.695044 ********** [options/cf_options.cc:1040]                      max_compaction_bytes: 2684354550
2025/07/13-10:26:06.695044 ********** [options/cf_options.cc:1042]     ignore_max_compaction_bytes_for_input: true
2025/07/13-10:26:06.695044 ********** [options/cf_options.cc:1044]                     target_file_size_base: 107374182
2025/07/13-10:26:06.695045 ********** [options/cf_options.cc:1046]               target_file_size_multiplier: 1
2025/07/13-10:26:06.695045 ********** [options/cf_options.cc:1048]                  max_bytes_for_level_base: 1073741824
2025/07/13-10:26:06.695045 ********** [options/cf_options.cc:1050]            max_bytes_for_level_multiplier: 10.000000
2025/07/13-10:26:06.695046 ********** [options/cf_options.cc:1052]                                       ttl: 2592000
2025/07/13-10:26:06.695046 ********** [options/cf_options.cc:1054]               periodic_compaction_seconds: 86400
2025/07/13-10:26:06.695047 ********** [options/cf_options.cc:1068] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/07/13-10:26:06.695047 ********** [options/cf_options.cc:1070]         max_sequential_skip_in_iterations: 8
2025/07/13-10:26:06.695048 ********** [options/cf_options.cc:1072]          check_flush_compaction_key_order: 1
2025/07/13-10:26:06.695048 ********** [options/cf_options.cc:1074]                      paranoid_file_checks: 0
2025/07/13-10:26:06.695049 ********** [options/cf_options.cc:1076]                        report_bg_io_stats: 0
2025/07/13-10:26:06.695049 ********** [options/cf_options.cc:1078]                               compression: 0
2025/07/13-10:26:06.695049 ********** [options/cf_options.cc:1081]                        experimental_mempurge_threshold: 0.000000
2025/07/13-10:26:06.695050 ********** [options/cf_options.cc:1085] compaction_options_universal.size_ratio : 1
2025/07/13-10:26:06.695050 ********** [options/cf_options.cc:1087] compaction_options_universal.min_merge_width : 2
2025/07/13-10:26:06.695050 ********** [options/cf_options.cc:1089] compaction_options_universal.max_merge_width : -1
2025/07/13-10:26:06.695051 ********** [options/cf_options.cc:1092] compaction_options_universal.max_size_amplification_percent : 200
2025/07/13-10:26:06.695051 ********** [options/cf_options.cc:1095] compaction_options_universal.compression_size_percent : -1
2025/07/13-10:26:06.695052 ********** [options/cf_options.cc:1097] compaction_options_universal.stop_style : 1
2025/07/13-10:26:06.695052 ********** [options/cf_options.cc:1100] compaction_options_universal.allow_trivial_move : 0
2025/07/13-10:26:06.695052 ********** [options/cf_options.cc:1102] compaction_options_universal.incremental        : 0
2025/07/13-10:26:06.695053 ********** [options/cf_options.cc:1106] compaction_options_fifo.max_table_files_size : 1073741824
2025/07/13-10:26:06.695053 ********** [options/cf_options.cc:1108] compaction_options_fifo.allow_compaction : 0
2025/07/13-10:26:06.695053 ********** [options/cf_options.cc:1112]                         enable_blob_files: false
2025/07/13-10:26:06.695054 ********** [options/cf_options.cc:1114]                             min_blob_size: 0
2025/07/13-10:26:06.695054 ********** [options/cf_options.cc:1116]                            blob_file_size: 268435456
2025/07/13-10:26:06.695055 ********** [options/cf_options.cc:1118]                     blob_compression_type: NoCompression
2025/07/13-10:26:06.695055 ********** [options/cf_options.cc:1120]            enable_blob_garbage_collection: false
2025/07/13-10:26:06.695055 ********** [options/cf_options.cc:1122]        blob_garbage_collection_age_cutoff: 0.250000
2025/07/13-10:26:06.695056 ********** [options/cf_options.cc:1124]   blob_garbage_collection_force_threshold: 1.000000
2025/07/13-10:26:06.695056 ********** [options/cf_options.cc:1126]            blob_compaction_readahead_size: 0
2025/07/13-10:26:06.695057 ********** [options/cf_options.cc:1128]                  blob_file_starting_level: 0
2025/07/13-10:26:06.695057 ********** [options/cf_options.cc:1132]                    prepopulate_blob_cache: disable
2025/07/13-10:26:06.695057 ********** [options/cf_options.cc:1134]                    last_level_temperature: 0
2025/07/13-10:26:06.700880 ********** [db/db_impl/db_impl.cc:490] Shutdown: canceling all background work
2025/07/13-10:26:06.701127 ********** [db/db_impl/db_impl.cc:692] Shutdown complete
