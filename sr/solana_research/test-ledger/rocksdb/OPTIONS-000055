# This is a RocksDB option file.
#
# For detailed file format spec, please refer to the example file
# in examples/rocksdb_option_file_example.ini
#

[Version]
  rocksdb_version=8.1.1
  options_file_version=1.1

[DBOptions]
  max_background_flushes=-1
  compaction_readahead_size=0
  strict_bytes_per_sync=false
  wal_bytes_per_sync=0
  max_open_files=-1
  stats_history_buffer_size=1048576
  max_total_wal_size=4294967296
  stats_persist_period_sec=600
  stats_dump_period_sec=600
  avoid_flush_during_shutdown=false
  max_subcompactions=1
  bytes_per_sync=0
  delayed_write_rate=16777216
  max_background_compactions=-1
  max_background_jobs=10
  delete_obsolete_files_period_micros=21600000000
  writable_file_max_buffer_size=1048576
  file_checksum_gen_factory=nullptr
  allow_data_in_errors=false
  max_bgerror_resume_count=2147483647
  best_efforts_recovery=false
  write_dbid_to_manifest=false
  atomic_flush=false
  wal_compression=kNoCompression
  manual_wal_flush=false
  two_write_queues=false
  avoid_flush_during_recovery=false
  dump_malloc_stats=false
  info_log_level=INFO_LEVEL
  write_thread_slow_yield_usec=3
  allow_ingest_behind=false
  fail_if_options_file_error=false
  persist_stats_to_disk=false
  WAL_ttl_seconds=0
  bgerror_resume_retry_interval=1000000
  allow_concurrent_memtable_write=true
  paranoid_checks=true
  WAL_size_limit_MB=0
  lowest_used_cache_tier=kNonVolatileBlockTier
  keep_log_file_num=1000
  table_cache_numshardbits=6
  max_file_opening_threads=16
  use_fsync=false
  unordered_write=false
  random_access_max_buffer_size=1048576
  log_readahead_size=0
  enable_pipelined_write=false
  wal_recovery_mode=kPointInTimeRecovery
  db_write_buffer_size=0
  allow_2pc=false
  skip_checking_sst_file_sizes_on_db_open=false
  skip_stats_update_on_db_open=false
  recycle_log_file_num=0
  db_host_id=__hostname__
  access_hint_on_compaction_start=NORMAL
  verify_sst_unique_id_in_manifest=true
  track_and_verify_wals_in_manifest=false
  error_if_exists=false
  manifest_preallocation_size=4194304
  is_fd_close_on_exec=true
  enable_write_thread_adaptive_yield=true
  enable_thread_tracking=false
  avoid_unnecessary_blocking_io=false
  allow_fallocate=true
  max_log_file_size=0
  advise_random_on_open=true
  create_missing_column_families=true
  max_write_batch_group_size_bytes=1048576
  use_adaptive_mutex=false
  wal_filter=nullptr
  create_if_missing=true
  enforce_single_del_contracts=true
  allow_mmap_writes=false
  log_file_time_to_roll=0
  use_direct_io_for_flush_and_compaction=false
  flush_verify_memtable_count=true
  max_manifest_file_size=**********
  write_thread_max_yield_usec=100
  use_direct_reads=false
  allow_mmap_reads=false
  

[CFOptions "default"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=67108864
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=268435456
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "default"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "meta"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "meta"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "dead_slots"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "dead_slots"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "duplicate_slots"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "duplicate_slots"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "erasure_meta"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "erasure_meta"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "orphans"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "orphans"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "bank_hashes"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "bank_hashes"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "root"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "root"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "index"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "index"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "data_shred"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "data_shred"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "code_shred"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "code_shred"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "transaction_status"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=86400
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=purged_slot_filter_factory(transaction_status)
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "transaction_status"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "address_signatures"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=86400
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=purged_slot_filter_factory(address_signatures)
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "address_signatures"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "transaction_memos"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=86400
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=purged_slot_filter_factory(transaction_memos)
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "transaction_memos"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "transaction_status_index"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "transaction_status_index"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "rewards"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "rewards"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "blocktime"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "blocktime"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "perf_samples"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "perf_samples"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "block_height"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "block_height"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "program_costs"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "program_costs"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "optimistic_slots"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "optimistic_slots"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "merkle_root_meta"]
  memtable_protection_bytes_per_key=0
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  blob_compression_type=kNoCompression
  prepopulate_blob_cache=kDisable
  blob_compaction_readahead_size=0
  level0_stop_writes_trigger=36
  min_blob_size=0
  last_level_temperature=kUnknown
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;incremental=false;max_merge_width=**********;size_ratio=1;}
  target_file_size_base=*********
  ignore_max_compaction_bytes_for_input=true
  memtable_whole_key_filtering=false
  blob_file_starting_level=0
  soft_pending_compaction_bytes_limit=***********
  max_write_buffer_number=8
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=**********;}
  check_flush_compaction_key_order=true
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  enable_blob_garbage_collection=false
  arena_block_size=1048576
  bottommost_compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  blob_garbage_collection_force_threshold=1.000000
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kNoCompression
  level0_file_num_compaction_trigger=4
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=268435456
  disable_auto_compactions=false
  max_compaction_bytes=2684354550
  compression_opts={use_zstd_dict_trainer=true;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  blob_file_size=268435456
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  experimental_mempurge_threshold=0.000000
  memtable_prefix_bloom_size_ratio=0.000000
  max_bytes_for_level_base=**********
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  sst_partitioner_factory=nullptr
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  compaction_filter_factory=nullptr
  compaction_filter=nullptr
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  min_write_buffer_number_to_merge=1
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  preserve_internal_time_seconds=0
  force_consistency_checks=true
  optimize_filters_for_hits=false
  merge_operator=nullptr
  num_levels=7
  level_compaction_dynamic_file_size=true
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  preclude_last_level_data_seconds=0
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "merkle_root_meta"]
  num_file_reads_for_auto_readahead=2
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  detect_filter_construct_corruption=false
  initial_auto_readahead_size=8192
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kXXH3
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  index_shortening=kShortenSeparators
  whole_key_filtering=true
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=false
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  
