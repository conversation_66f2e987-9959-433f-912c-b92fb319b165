[0m[38;5;8m[[0m2025-07-12T10:08:19.841134000Z [0m[32mINFO [0m solana_test_validator[0m[38;5;8m][0m solana-validator 1.18.20 (src:00000000; feat:4215500110, client:SolanaLabs)
[0m[38;5;8m[[0m2025-07-12T10:08:19.841166000Z [0m[32mINFO [0m solana_test_validator[0m[38;5;8m][0m Starting validator with: ArgsOs {
        inner: [
            "solana-test-validator",
        ],
    }
[0m[38;5;8m[[0m2025-07-12T10:08:19.841193000Z [0m[33mWARN [0m solana_perf[0m[38;5;8m][0m CUDA is disabled
[0m[38;5;8m[[0m2025-07-12T10:08:19.843684000Z [0m[32mINFO [0m solana_faucet::faucet[0m[38;5;8m][0m Faucet started. Listening on: 0.0.0.0:9900
[0m[38;5;8m[[0m2025-07-12T10:08:19.843690000Z [0m[32mINFO [0m solana_faucet::faucet[0m[38;5;8m][0m Faucet account address: 2nNmu3P47JpptHveQNyc8orrNgcjUp4zbpdYtF7koZJm
[0m[38;5;8m[[0m2025-07-12T10:08:19.844018000Z [0m[32mINFO [0m solana_validator::admin_rpc_service[0m[38;5;8m][0m started admin rpc service!
[0m[38;5;8m[[0m2025-07-12T10:08:19.844422000Z [0m[32mINFO [0m solana_test_validator[0m[38;5;8m][0m Feature for 41tVp5qR1XwWRt5WifvtSQyuxtqQWJgEK8w91AtBqSwP deactivated
[0m[38;5;8m[[0m2025-07-12T10:08:19.845522000Z [0m[32mINFO [0m solana_ledger::blockstore[0m[38;5;8m][0m Maximum open file descriptors: 1048575
[0m[38;5;8m[[0m2025-07-12T10:08:19.845530000Z [0m[32mINFO [0m solana_ledger::blockstore[0m[38;5;8m][0m Opening blockstore at "test-ledger/rocksdb"
[0m[38;5;8m[[0m2025-07-12T10:08:19.845700000Z [0m[33mWARN [0m solana_ledger::blockstore_db[0m[38;5;8m][0m Unable to detect Rocks columns: Error { message: "IO error: No such file or directory: While opening a file for sequentially reading: test-ledger/rocksdb/CURRENT: No such file or directory" }
[0m[38;5;8m[[0m2025-07-12T10:08:19.908169000Z [0m[32mINFO [0m solana_ledger::blockstore[0m[38;5;8m][0m Opening blockstore done; blockstore open took 62ms
[0m[38;5;8m[[0m2025-07-12T10:08:19.911040000Z [0m[32mINFO [0m solana_metrics::metrics[0m[38;5;8m][0m metrics disabled: environment variable not found
[0m[38;5;8m[[0m2025-07-12T10:08:19.911071000Z [0m[32mINFO [0m solana_metrics::metrics[0m[38;5;8m][0m datapoint: shred_insert_is_full total_time_ms=1i slot=0i last_index=31i num_repaired=0i num_recovered=0i
[0m[38;5;8m[[0m2025-07-12T10:08:19.965253000Z [0m[32mINFO [0m solana_accounts_db::hardened_unpack[0m[38;5;8m][0m Extracting "test-ledger/genesis.tar.bz2"...
