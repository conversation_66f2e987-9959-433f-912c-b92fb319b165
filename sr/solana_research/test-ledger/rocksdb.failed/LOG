2025/07/12-18:08:19.846054 ********** RocksDB version: 8.1.1
2025/07/12-18:08:19.846458 ********** Compile date 2023-04-06 16:38:52
2025/07/12-18:08:19.846460 ********** DB SUMMARY
2025/07/12-18:08:19.846461 ********** DB Session ID:  M73QQQJ1IPFIEGFIQIA9
2025/07/12-18:08:19.846477 ********** SST files in test-ledger/rocksdb dir, Total Num: 0, files: 
2025/07/12-18:08:19.846478 ********** Write Ahead Log file in test-ledger/rocksdb: 
2025/07/12-18:08:19.846478 **********                         Options.error_if_exists: 0
2025/07/12-18:08:19.846479 **********                       Options.create_if_missing: 1
2025/07/12-18:08:19.846480 **********                         Options.paranoid_checks: 1
2025/07/12-18:08:19.846480 **********             Options.flush_verify_memtable_count: 1
2025/07/12-18:08:19.846480 **********                               Options.track_and_verify_wals_in_manifest: 0
2025/07/12-18:08:19.846481 **********        Options.verify_sst_unique_id_in_manifest: 1
2025/07/12-18:08:19.846481 **********                                     Options.env: 0x1068d15b8
2025/07/12-18:08:19.846482 **********                                      Options.fs: PosixFileSystem
2025/07/12-18:08:19.846482 **********                                Options.info_log: 0x145651da8
2025/07/12-18:08:19.846482 **********                Options.max_file_opening_threads: 16
2025/07/12-18:08:19.846497 **********                              Options.statistics: 0x0
2025/07/12-18:08:19.846499 **********                               Options.use_fsync: 0
2025/07/12-18:08:19.846500 **********                       Options.max_log_file_size: 0
2025/07/12-18:08:19.846501 **********                  Options.max_manifest_file_size: 1073741824
2025/07/12-18:08:19.846502 **********                   Options.log_file_time_to_roll: 0
2025/07/12-18:08:19.846502 **********                       Options.keep_log_file_num: 1000
2025/07/12-18:08:19.846503 **********                    Options.recycle_log_file_num: 0
2025/07/12-18:08:19.846504 **********                         Options.allow_fallocate: 1
2025/07/12-18:08:19.846504 **********                        Options.allow_mmap_reads: 0
2025/07/12-18:08:19.846505 **********                       Options.allow_mmap_writes: 0
2025/07/12-18:08:19.846506 **********                        Options.use_direct_reads: 0
2025/07/12-18:08:19.846506 **********                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/12-18:08:19.846507 **********          Options.create_missing_column_families: 1
2025/07/12-18:08:19.846508 **********                              Options.db_log_dir: 
2025/07/12-18:08:19.846509 **********                                 Options.wal_dir: 
2025/07/12-18:08:19.846509 **********                Options.table_cache_numshardbits: 6
2025/07/12-18:08:19.846510 **********                         Options.WAL_ttl_seconds: 0
2025/07/12-18:08:19.846511 **********                       Options.WAL_size_limit_MB: 0
2025/07/12-18:08:19.846511 **********                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/12-18:08:19.846512 **********             Options.manifest_preallocation_size: 4194304
2025/07/12-18:08:19.846513 **********                     Options.is_fd_close_on_exec: 1
2025/07/12-18:08:19.846513 **********                   Options.advise_random_on_open: 1
2025/07/12-18:08:19.846514 **********                    Options.db_write_buffer_size: 0
2025/07/12-18:08:19.846515 **********                    Options.write_buffer_manager: 0x145651f70
2025/07/12-18:08:19.846515 **********         Options.access_hint_on_compaction_start: 1
2025/07/12-18:08:19.846516 **********           Options.random_access_max_buffer_size: 1048576
2025/07/12-18:08:19.846517 **********                      Options.use_adaptive_mutex: 0
2025/07/12-18:08:19.846517 **********                            Options.rate_limiter: 0x0
2025/07/12-18:08:19.846518 **********     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/12-18:08:19.846519 **********                       Options.wal_recovery_mode: 2
2025/07/12-18:08:19.846520 **********                  Options.enable_thread_tracking: 0
2025/07/12-18:08:19.846520 **********                  Options.enable_pipelined_write: 0
2025/07/12-18:08:19.846521 **********                  Options.unordered_write: 0
2025/07/12-18:08:19.846522 **********         Options.allow_concurrent_memtable_write: 1
2025/07/12-18:08:19.846522 **********      Options.enable_write_thread_adaptive_yield: 1
2025/07/12-18:08:19.846523 **********             Options.write_thread_max_yield_usec: 100
2025/07/12-18:08:19.846524 **********            Options.write_thread_slow_yield_usec: 3
2025/07/12-18:08:19.846524 **********                               Options.row_cache: None
2025/07/12-18:08:19.846525 **********                              Options.wal_filter: None
2025/07/12-18:08:19.846526 **********             Options.avoid_flush_during_recovery: 0
2025/07/12-18:08:19.846526 **********             Options.allow_ingest_behind: 0
2025/07/12-18:08:19.846527 **********             Options.two_write_queues: 0
2025/07/12-18:08:19.846528 **********             Options.manual_wal_flush: 0
2025/07/12-18:08:19.846529 **********             Options.wal_compression: 0
2025/07/12-18:08:19.846529 **********             Options.atomic_flush: 0
2025/07/12-18:08:19.846530 **********             Options.avoid_unnecessary_blocking_io: 0
2025/07/12-18:08:19.846531 **********                 Options.persist_stats_to_disk: 0
2025/07/12-18:08:19.846531 **********                 Options.write_dbid_to_manifest: 0
2025/07/12-18:08:19.846532 **********                 Options.log_readahead_size: 0
2025/07/12-18:08:19.846533 **********                 Options.file_checksum_gen_factory: Unknown
2025/07/12-18:08:19.846534 **********                 Options.best_efforts_recovery: 0
2025/07/12-18:08:19.846534 **********                Options.max_bgerror_resume_count: 2147483647
2025/07/12-18:08:19.846535 **********            Options.bgerror_resume_retry_interval: 1000000
2025/07/12-18:08:19.846536 **********             Options.allow_data_in_errors: 0
2025/07/12-18:08:19.846536 **********             Options.db_host_id: __hostname__
2025/07/12-18:08:19.846537 **********             Options.enforce_single_del_contracts: true
2025/07/12-18:08:19.846538 **********             Options.max_background_jobs: 10
2025/07/12-18:08:19.846539 **********             Options.max_background_compactions: -1
2025/07/12-18:08:19.846539 **********             Options.max_subcompactions: 1
2025/07/12-18:08:19.846540 **********             Options.avoid_flush_during_shutdown: 0
2025/07/12-18:08:19.846541 **********           Options.writable_file_max_buffer_size: 1048576
2025/07/12-18:08:19.846542 **********             Options.delayed_write_rate : 16777216
2025/07/12-18:08:19.846542 **********             Options.max_total_wal_size: 4294967296
2025/07/12-18:08:19.846543 **********             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/12-18:08:19.846544 **********                   Options.stats_dump_period_sec: 600
2025/07/12-18:08:19.846545 **********                 Options.stats_persist_period_sec: 600
2025/07/12-18:08:19.846545 **********                 Options.stats_history_buffer_size: 1048576
2025/07/12-18:08:19.846546 **********                          Options.max_open_files: -1
2025/07/12-18:08:19.846547 **********                          Options.bytes_per_sync: 0
2025/07/12-18:08:19.846547 **********                      Options.wal_bytes_per_sync: 0
2025/07/12-18:08:19.846548 **********                   Options.strict_bytes_per_sync: 0
2025/07/12-18:08:19.846549 **********       Options.compaction_readahead_size: 0
2025/07/12-18:08:19.846549 **********                  Options.max_background_flushes: -1
2025/07/12-18:08:19.846550 ********** Compression algorithms supported:
2025/07/12-18:08:19.846551 ********** 	kZSTD supported: 0
2025/07/12-18:08:19.846552 ********** 	kZlibCompression supported: 0
2025/07/12-18:08:19.846553 ********** 	kXpressCompression supported: 0
2025/07/12-18:08:19.846554 ********** 	kSnappyCompression supported: 0
2025/07/12-18:08:19.846554 ********** 	kZSTDNotFinalCompression supported: 0
2025/07/12-18:08:19.846555 ********** 	kLZ4HCCompression supported: 1
2025/07/12-18:08:19.846556 ********** 	kLZ4Compression supported: 1
2025/07/12-18:08:19.846556 ********** 	kBZip2Compression supported: 0
2025/07/12-18:08:19.846566 ********** Fast CRC32 supported: Supported on Arm64
2025/07/12-18:08:19.846575 ********** DMutex implementation: pthread_mutex_t
2025/07/12-18:08:19.846814 ********** [db/db_impl/db_impl_open.cc:315] Creating manifest 1 
2025/07/12-18:08:19.847049 ********** [db/version_set.cc:5662] Recovering from manifest file: test-ledger/rocksdb/MANIFEST-000001
2025/07/12-18:08:19.847084 ********** [db/column_family.cc:621] --------------- Options for column family [default]:
2025/07/12-18:08:19.847085 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/12-18:08:19.847085 **********           Options.merge_operator: None
2025/07/12-18:08:19.847086 **********        Options.compaction_filter: None
2025/07/12-18:08:19.847086 **********        Options.compaction_filter_factory: None
2025/07/12-18:08:19.847087 **********  Options.sst_partitioner_factory: None
2025/07/12-18:08:19.847087 **********         Options.memtable_factory: SkipListFactory
2025/07/12-18:08:19.847087 **********            Options.table_factory: BlockBasedTable
2025/07/12-18:08:19.847096 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x145631980)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x145650618
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/12-18:08:19.847098 **********        Options.write_buffer_size: 67108864
2025/07/12-18:08:19.847098 **********  Options.max_write_buffer_number: 2
2025/07/12-18:08:19.847099 **********          Options.compression: NoCompression
2025/07/12-18:08:19.847099 **********                  Options.bottommost_compression: Disabled
2025/07/12-18:08:19.847100 **********       Options.prefix_extractor: nullptr
2025/07/12-18:08:19.847100 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/12-18:08:19.847101 **********             Options.num_levels: 7
2025/07/12-18:08:19.847101 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/12-18:08:19.847101 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/12-18:08:19.847102 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/12-18:08:19.847102 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/12-18:08:19.847103 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/12-18:08:19.847103 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/12-18:08:19.847103 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.847104 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.847104 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/12-18:08:19.847105 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/12-18:08:19.847105 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.847106 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.847106 **********            Options.compression_opts.window_bits: -14
2025/07/12-18:08:19.847106 **********                  Options.compression_opts.level: 32767
2025/07/12-18:08:19.847107 **********               Options.compression_opts.strategy: 0
2025/07/12-18:08:19.847107 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.847108 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.847108 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.847108 **********         Options.compression_opts.parallel_threads: 1
2025/07/12-18:08:19.847109 **********                  Options.compression_opts.enabled: false
2025/07/12-18:08:19.847109 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.847109 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.847110 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.847110 **********              Options.level0_stop_writes_trigger: 36
2025/07/12-18:08:19.847111 **********                   Options.target_file_size_base: 67108864
2025/07/12-18:08:19.847111 **********             Options.target_file_size_multiplier: 1
2025/07/12-18:08:19.847111 **********                Options.max_bytes_for_level_base: 268435456
2025/07/12-18:08:19.847112 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/12-18:08:19.847112 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.847113 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/12-18:08:19.847113 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/12-18:08:19.847114 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/12-18:08:19.847114 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/12-18:08:19.847114 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/12-18:08:19.847115 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/12-18:08:19.847115 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/12-18:08:19.847115 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.847116 **********                    Options.max_compaction_bytes: 1677721600
2025/07/12-18:08:19.847116 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.847117 **********                        Options.arena_block_size: 1048576
2025/07/12-18:08:19.847117 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.847117 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.847118 **********                Options.disable_auto_compactions: 0
2025/07/12-18:08:19.847118 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/12-18:08:19.847119 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/12-18:08:19.847119 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/12-18:08:19.847120 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/12-18:08:19.847120 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/12-18:08:19.847120 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/12-18:08:19.847121 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/12-18:08:19.847121 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/12-18:08:19.847122 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/12-18:08:19.847122 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/12-18:08:19.847123 **********                   Options.table_properties_collectors: 
2025/07/12-18:08:19.847123 **********                   Options.inplace_update_support: 0
2025/07/12-18:08:19.847124 **********                 Options.inplace_update_num_locks: 10000
2025/07/12-18:08:19.847124 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/12-18:08:19.847124 **********               Options.memtable_whole_key_filtering: 0
2025/07/12-18:08:19.847125 **********   Options.memtable_huge_page_size: 0
2025/07/12-18:08:19.847125 **********                           Options.bloom_locality: 0
2025/07/12-18:08:19.847126 **********                    Options.max_successive_merges: 0
2025/07/12-18:08:19.847126 **********                Options.optimize_filters_for_hits: 0
2025/07/12-18:08:19.847126 **********                Options.paranoid_file_checks: 0
2025/07/12-18:08:19.847127 **********                Options.force_consistency_checks: 1
2025/07/12-18:08:19.847127 **********                Options.report_bg_io_stats: 0
2025/07/12-18:08:19.847127 **********                               Options.ttl: 2592000
2025/07/12-18:08:19.847128 **********          Options.periodic_compaction_seconds: 0
2025/07/12-18:08:19.847128 **********  Options.preclude_last_level_data_seconds: 0
2025/07/12-18:08:19.847128 **********    Options.preserve_internal_time_seconds: 0
2025/07/12-18:08:19.847129 **********                       Options.enable_blob_files: false
2025/07/12-18:08:19.847129 **********                           Options.min_blob_size: 0
2025/07/12-18:08:19.847130 **********                          Options.blob_file_size: 268435456
2025/07/12-18:08:19.847130 **********                   Options.blob_compression_type: NoCompression
2025/07/12-18:08:19.847130 **********          Options.enable_blob_garbage_collection: false
2025/07/12-18:08:19.847131 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.847131 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.847132 **********          Options.blob_compaction_readahead_size: 0
2025/07/12-18:08:19.847132 **********                Options.blob_file_starting_level: 0
2025/07/12-18:08:19.847132 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.847363 ********** [db/version_set.cc:5713] Recovered from manifest file:test-ledger/rocksdb/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/12-18:08:19.847365 ********** [db/version_set.cc:5722] Column family [default] (ID 0), log number is 0
2025/07/12-18:08:19.847386 ********** [db/db_impl/db_impl_open.cc:537] DB ID: d0853d36-f49c-402f-bc6c-847e0b6d5d39
2025/07/12-18:08:19.847464 ********** [db/version_set.cc:5180] Creating manifest 5
2025/07/12-18:08:19.847754 ********** [db/column_family.cc:621] --------------- Options for column family [meta]:
2025/07/12-18:08:19.847755 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/12-18:08:19.847756 **********           Options.merge_operator: None
2025/07/12-18:08:19.847756 **********        Options.compaction_filter: None
2025/07/12-18:08:19.847757 **********        Options.compaction_filter_factory: None
2025/07/12-18:08:19.847757 **********  Options.sst_partitioner_factory: None
2025/07/12-18:08:19.847757 **********         Options.memtable_factory: SkipListFactory
2025/07/12-18:08:19.847758 **********            Options.table_factory: BlockBasedTable
2025/07/12-18:08:19.847764 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14563e470)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14563e4c8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/12-18:08:19.847765 **********        Options.write_buffer_size: 268435456
2025/07/12-18:08:19.847765 **********  Options.max_write_buffer_number: 8
2025/07/12-18:08:19.847765 **********          Options.compression: NoCompression
2025/07/12-18:08:19.847766 **********                  Options.bottommost_compression: Disabled
2025/07/12-18:08:19.847766 **********       Options.prefix_extractor: nullptr
2025/07/12-18:08:19.847767 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/12-18:08:19.847767 **********             Options.num_levels: 7
2025/07/12-18:08:19.847767 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/12-18:08:19.847768 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/12-18:08:19.847768 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/12-18:08:19.847769 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/12-18:08:19.847769 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/12-18:08:19.847769 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/12-18:08:19.847770 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.847770 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.847771 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/12-18:08:19.847771 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/12-18:08:19.847771 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.847772 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.847772 **********            Options.compression_opts.window_bits: -14
2025/07/12-18:08:19.847772 **********                  Options.compression_opts.level: 32767
2025/07/12-18:08:19.847773 **********               Options.compression_opts.strategy: 0
2025/07/12-18:08:19.847773 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.847774 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.847774 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.847774 **********         Options.compression_opts.parallel_threads: 1
2025/07/12-18:08:19.847775 **********                  Options.compression_opts.enabled: false
2025/07/12-18:08:19.847775 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.847776 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.847776 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.847776 **********              Options.level0_stop_writes_trigger: 36
2025/07/12-18:08:19.847777 **********                   Options.target_file_size_base: 107374182
2025/07/12-18:08:19.847777 **********             Options.target_file_size_multiplier: 1
2025/07/12-18:08:19.847777 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.847778 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/12-18:08:19.847778 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.847779 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/12-18:08:19.847779 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/12-18:08:19.847780 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/12-18:08:19.847780 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/12-18:08:19.847780 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/12-18:08:19.847781 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/12-18:08:19.847781 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/12-18:08:19.847781 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.847782 **********                    Options.max_compaction_bytes: 2684354550
2025/07/12-18:08:19.847782 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.847783 **********                        Options.arena_block_size: 1048576
2025/07/12-18:08:19.847783 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.847783 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.847784 **********                Options.disable_auto_compactions: 0
2025/07/12-18:08:19.847784 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/12-18:08:19.847785 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/12-18:08:19.847785 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/12-18:08:19.847785 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/12-18:08:19.847786 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/12-18:08:19.847786 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/12-18:08:19.847787 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/12-18:08:19.847787 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/12-18:08:19.847788 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/12-18:08:19.847788 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/12-18:08:19.847788 **********                   Options.table_properties_collectors: 
2025/07/12-18:08:19.847789 **********                   Options.inplace_update_support: 0
2025/07/12-18:08:19.847789 **********                 Options.inplace_update_num_locks: 10000
2025/07/12-18:08:19.847790 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/12-18:08:19.847790 **********               Options.memtable_whole_key_filtering: 0
2025/07/12-18:08:19.847790 **********   Options.memtable_huge_page_size: 0
2025/07/12-18:08:19.847791 **********                           Options.bloom_locality: 0
2025/07/12-18:08:19.847791 **********                    Options.max_successive_merges: 0
2025/07/12-18:08:19.847791 **********                Options.optimize_filters_for_hits: 0
2025/07/12-18:08:19.847792 **********                Options.paranoid_file_checks: 0
2025/07/12-18:08:19.847792 **********                Options.force_consistency_checks: 1
2025/07/12-18:08:19.847793 **********                Options.report_bg_io_stats: 0
2025/07/12-18:08:19.847793 **********                               Options.ttl: 2592000
2025/07/12-18:08:19.847793 **********          Options.periodic_compaction_seconds: 0
2025/07/12-18:08:19.847794 **********  Options.preclude_last_level_data_seconds: 0
2025/07/12-18:08:19.847794 **********    Options.preserve_internal_time_seconds: 0
2025/07/12-18:08:19.847794 **********                       Options.enable_blob_files: false
2025/07/12-18:08:19.847795 **********                           Options.min_blob_size: 0
2025/07/12-18:08:19.847795 **********                          Options.blob_file_size: 268435456
2025/07/12-18:08:19.847796 **********                   Options.blob_compression_type: NoCompression
2025/07/12-18:08:19.847796 **********          Options.enable_blob_garbage_collection: false
2025/07/12-18:08:19.847796 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.847797 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.847797 **********          Options.blob_compaction_readahead_size: 0
2025/07/12-18:08:19.847798 **********                Options.blob_file_starting_level: 0
2025/07/12-18:08:19.847798 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.847814 ********** [db/db_impl/db_impl.cc:3200] Created column family [meta] (ID 1)
2025/07/12-18:08:19.848609 ********** [db/column_family.cc:621] --------------- Options for column family [dead_slots]:
2025/07/12-18:08:19.848610 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/12-18:08:19.848611 **********           Options.merge_operator: None
2025/07/12-18:08:19.848611 **********        Options.compaction_filter: None
2025/07/12-18:08:19.848611 **********        Options.compaction_filter_factory: None
2025/07/12-18:08:19.848612 **********  Options.sst_partitioner_factory: None
2025/07/12-18:08:19.848612 **********         Options.memtable_factory: SkipListFactory
2025/07/12-18:08:19.848612 **********            Options.table_factory: BlockBasedTable
2025/07/12-18:08:19.848618 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14563f360)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14563f3b8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/12-18:08:19.848619 **********        Options.write_buffer_size: 268435456
2025/07/12-18:08:19.848619 **********  Options.max_write_buffer_number: 8
2025/07/12-18:08:19.848620 **********          Options.compression: NoCompression
2025/07/12-18:08:19.848620 **********                  Options.bottommost_compression: Disabled
2025/07/12-18:08:19.848621 **********       Options.prefix_extractor: nullptr
2025/07/12-18:08:19.848621 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/12-18:08:19.848621 **********             Options.num_levels: 7
2025/07/12-18:08:19.848622 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/12-18:08:19.848622 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/12-18:08:19.848623 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/12-18:08:19.848623 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/12-18:08:19.848623 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/12-18:08:19.848624 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/12-18:08:19.848624 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.848625 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.848625 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/12-18:08:19.848625 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/12-18:08:19.848626 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.848626 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.848627 **********            Options.compression_opts.window_bits: -14
2025/07/12-18:08:19.848627 **********                  Options.compression_opts.level: 32767
2025/07/12-18:08:19.848627 **********               Options.compression_opts.strategy: 0
2025/07/12-18:08:19.848628 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.848628 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.848628 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.848629 **********         Options.compression_opts.parallel_threads: 1
2025/07/12-18:08:19.848629 **********                  Options.compression_opts.enabled: false
2025/07/12-18:08:19.848630 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.848630 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.848630 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.848631 **********              Options.level0_stop_writes_trigger: 36
2025/07/12-18:08:19.848631 **********                   Options.target_file_size_base: 107374182
2025/07/12-18:08:19.848632 **********             Options.target_file_size_multiplier: 1
2025/07/12-18:08:19.848632 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.848632 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/12-18:08:19.848633 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.848633 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/12-18:08:19.848634 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/12-18:08:19.848634 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/12-18:08:19.848634 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/12-18:08:19.848635 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/12-18:08:19.848635 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/12-18:08:19.848635 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/12-18:08:19.848636 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.848636 **********                    Options.max_compaction_bytes: 2684354550
2025/07/12-18:08:19.848637 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.848637 **********                        Options.arena_block_size: 1048576
2025/07/12-18:08:19.848637 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.848638 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.848638 **********                Options.disable_auto_compactions: 0
2025/07/12-18:08:19.848639 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/12-18:08:19.848639 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/12-18:08:19.848639 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/12-18:08:19.848640 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/12-18:08:19.848640 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/12-18:08:19.848640 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/12-18:08:19.848641 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/12-18:08:19.848641 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/12-18:08:19.848642 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/12-18:08:19.848642 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/12-18:08:19.848643 **********                   Options.table_properties_collectors: 
2025/07/12-18:08:19.848643 **********                   Options.inplace_update_support: 0
2025/07/12-18:08:19.848643 **********                 Options.inplace_update_num_locks: 10000
2025/07/12-18:08:19.848644 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/12-18:08:19.848644 **********               Options.memtable_whole_key_filtering: 0
2025/07/12-18:08:19.848644 **********   Options.memtable_huge_page_size: 0
2025/07/12-18:08:19.848645 **********                           Options.bloom_locality: 0
2025/07/12-18:08:19.848645 **********                    Options.max_successive_merges: 0
2025/07/12-18:08:19.848646 **********                Options.optimize_filters_for_hits: 0
2025/07/12-18:08:19.848646 **********                Options.paranoid_file_checks: 0
2025/07/12-18:08:19.848646 **********                Options.force_consistency_checks: 1
2025/07/12-18:08:19.848647 **********                Options.report_bg_io_stats: 0
2025/07/12-18:08:19.848647 **********                               Options.ttl: 2592000
2025/07/12-18:08:19.848647 **********          Options.periodic_compaction_seconds: 0
2025/07/12-18:08:19.848648 **********  Options.preclude_last_level_data_seconds: 0
2025/07/12-18:08:19.848648 **********    Options.preserve_internal_time_seconds: 0
2025/07/12-18:08:19.848649 **********                       Options.enable_blob_files: false
2025/07/12-18:08:19.848649 **********                           Options.min_blob_size: 0
2025/07/12-18:08:19.848649 **********                          Options.blob_file_size: 268435456
2025/07/12-18:08:19.848650 **********                   Options.blob_compression_type: NoCompression
2025/07/12-18:08:19.848650 **********          Options.enable_blob_garbage_collection: false
2025/07/12-18:08:19.848650 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.848651 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.848651 **********          Options.blob_compaction_readahead_size: 0
2025/07/12-18:08:19.848652 **********                Options.blob_file_starting_level: 0
2025/07/12-18:08:19.848652 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.848665 ********** [db/db_impl/db_impl.cc:3200] Created column family [dead_slots] (ID 2)
2025/07/12-18:08:19.849752 ********** [db/column_family.cc:621] --------------- Options for column family [duplicate_slots]:
2025/07/12-18:08:19.849753 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/12-18:08:19.849753 **********           Options.merge_operator: None
2025/07/12-18:08:19.849754 **********        Options.compaction_filter: None
2025/07/12-18:08:19.849754 **********        Options.compaction_filter_factory: None
2025/07/12-18:08:19.849755 **********  Options.sst_partitioner_factory: None
2025/07/12-18:08:19.849755 **********         Options.memtable_factory: SkipListFactory
2025/07/12-18:08:19.849755 **********            Options.table_factory: BlockBasedTable
2025/07/12-18:08:19.849762 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x145640260)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x1456402b8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/12-18:08:19.849763 **********        Options.write_buffer_size: 268435456
2025/07/12-18:08:19.849763 **********  Options.max_write_buffer_number: 8
2025/07/12-18:08:19.849763 **********          Options.compression: NoCompression
2025/07/12-18:08:19.849764 **********                  Options.bottommost_compression: Disabled
2025/07/12-18:08:19.849764 **********       Options.prefix_extractor: nullptr
2025/07/12-18:08:19.849765 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/12-18:08:19.849765 **********             Options.num_levels: 7
2025/07/12-18:08:19.849765 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/12-18:08:19.849766 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/12-18:08:19.849766 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/12-18:08:19.849766 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/12-18:08:19.849767 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/12-18:08:19.849767 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/12-18:08:19.849768 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.849768 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.849768 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/12-18:08:19.849769 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/12-18:08:19.849769 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.849769 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.849770 **********            Options.compression_opts.window_bits: -14
2025/07/12-18:08:19.849770 **********                  Options.compression_opts.level: 32767
2025/07/12-18:08:19.849771 **********               Options.compression_opts.strategy: 0
2025/07/12-18:08:19.849771 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.849771 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.849772 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.849772 **********         Options.compression_opts.parallel_threads: 1
2025/07/12-18:08:19.849772 **********                  Options.compression_opts.enabled: false
2025/07/12-18:08:19.849773 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.849773 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.849774 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.849774 **********              Options.level0_stop_writes_trigger: 36
2025/07/12-18:08:19.849774 **********                   Options.target_file_size_base: 107374182
2025/07/12-18:08:19.849775 **********             Options.target_file_size_multiplier: 1
2025/07/12-18:08:19.849775 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.849775 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/12-18:08:19.849776 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.849776 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/12-18:08:19.849777 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/12-18:08:19.849777 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/12-18:08:19.849777 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/12-18:08:19.849778 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/12-18:08:19.849778 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/12-18:08:19.849779 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/12-18:08:19.849779 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.849779 **********                    Options.max_compaction_bytes: 2684354550
2025/07/12-18:08:19.849780 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.849780 **********                        Options.arena_block_size: 1048576
2025/07/12-18:08:19.849780 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.849781 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.849781 **********                Options.disable_auto_compactions: 0
2025/07/12-18:08:19.849782 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/12-18:08:19.849782 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/12-18:08:19.849782 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/12-18:08:19.849783 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/12-18:08:19.849783 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/12-18:08:19.849783 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/12-18:08:19.849784 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/12-18:08:19.849784 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/12-18:08:19.849785 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/12-18:08:19.849785 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/12-18:08:19.849786 **********                   Options.table_properties_collectors: 
2025/07/12-18:08:19.849786 **********                   Options.inplace_update_support: 0
2025/07/12-18:08:19.849786 **********                 Options.inplace_update_num_locks: 10000
2025/07/12-18:08:19.849787 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/12-18:08:19.849787 **********               Options.memtable_whole_key_filtering: 0
2025/07/12-18:08:19.849788 **********   Options.memtable_huge_page_size: 0
2025/07/12-18:08:19.849788 **********                           Options.bloom_locality: 0
2025/07/12-18:08:19.849788 **********                    Options.max_successive_merges: 0
2025/07/12-18:08:19.849789 **********                Options.optimize_filters_for_hits: 0
2025/07/12-18:08:19.849789 **********                Options.paranoid_file_checks: 0
2025/07/12-18:08:19.849789 **********                Options.force_consistency_checks: 1
2025/07/12-18:08:19.849790 **********                Options.report_bg_io_stats: 0
2025/07/12-18:08:19.849790 **********                               Options.ttl: 2592000
2025/07/12-18:08:19.849790 **********          Options.periodic_compaction_seconds: 0
2025/07/12-18:08:19.849791 **********  Options.preclude_last_level_data_seconds: 0
2025/07/12-18:08:19.849791 **********    Options.preserve_internal_time_seconds: 0
2025/07/12-18:08:19.849791 **********                       Options.enable_blob_files: false
2025/07/12-18:08:19.849792 **********                           Options.min_blob_size: 0
2025/07/12-18:08:19.849792 **********                          Options.blob_file_size: 268435456
2025/07/12-18:08:19.849793 **********                   Options.blob_compression_type: NoCompression
2025/07/12-18:08:19.849793 **********          Options.enable_blob_garbage_collection: false
2025/07/12-18:08:19.849793 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.849794 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.849794 **********          Options.blob_compaction_readahead_size: 0
2025/07/12-18:08:19.849795 **********                Options.blob_file_starting_level: 0
2025/07/12-18:08:19.849795 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.849808 ********** [db/db_impl/db_impl.cc:3200] Created column family [duplicate_slots] (ID 3)
2025/07/12-18:08:19.850863 ********** [db/column_family.cc:621] --------------- Options for column family [erasure_meta]:
2025/07/12-18:08:19.850864 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/12-18:08:19.850865 **********           Options.merge_operator: None
2025/07/12-18:08:19.850865 **********        Options.compaction_filter: None
2025/07/12-18:08:19.850866 **********        Options.compaction_filter_factory: None
2025/07/12-18:08:19.850866 **********  Options.sst_partitioner_factory: None
2025/07/12-18:08:19.850866 **********         Options.memtable_factory: SkipListFactory
2025/07/12-18:08:19.850867 **********            Options.table_factory: BlockBasedTable
2025/07/12-18:08:19.850873 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x145641160)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x1456411b8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/12-18:08:19.850873 **********        Options.write_buffer_size: 268435456
2025/07/12-18:08:19.850874 **********  Options.max_write_buffer_number: 8
2025/07/12-18:08:19.850874 **********          Options.compression: NoCompression
2025/07/12-18:08:19.850874 **********                  Options.bottommost_compression: Disabled
2025/07/12-18:08:19.850875 **********       Options.prefix_extractor: nullptr
2025/07/12-18:08:19.850875 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/12-18:08:19.850875 **********             Options.num_levels: 7
2025/07/12-18:08:19.850876 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/12-18:08:19.850876 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/12-18:08:19.850877 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/12-18:08:19.850877 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/12-18:08:19.850877 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/12-18:08:19.850878 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/12-18:08:19.850878 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.850878 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.850879 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/12-18:08:19.850879 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/12-18:08:19.850880 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.850880 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.850880 **********            Options.compression_opts.window_bits: -14
2025/07/12-18:08:19.850881 **********                  Options.compression_opts.level: 32767
2025/07/12-18:08:19.850881 **********               Options.compression_opts.strategy: 0
2025/07/12-18:08:19.850882 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.850882 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.850882 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.850883 **********         Options.compression_opts.parallel_threads: 1
2025/07/12-18:08:19.850883 **********                  Options.compression_opts.enabled: false
2025/07/12-18:08:19.850883 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.850884 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.850884 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.850884 **********              Options.level0_stop_writes_trigger: 36
2025/07/12-18:08:19.850885 **********                   Options.target_file_size_base: 107374182
2025/07/12-18:08:19.850885 **********             Options.target_file_size_multiplier: 1
2025/07/12-18:08:19.850886 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.850886 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/12-18:08:19.850886 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.850887 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/12-18:08:19.850887 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/12-18:08:19.850887 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/12-18:08:19.850888 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/12-18:08:19.850888 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/12-18:08:19.850889 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/12-18:08:19.850889 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/12-18:08:19.850889 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.850890 **********                    Options.max_compaction_bytes: 2684354550
2025/07/12-18:08:19.850890 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.850890 **********                        Options.arena_block_size: 1048576
2025/07/12-18:08:19.850891 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.850891 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.850891 **********                Options.disable_auto_compactions: 0
2025/07/12-18:08:19.850892 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/12-18:08:19.850892 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/12-18:08:19.850893 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/12-18:08:19.850893 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/12-18:08:19.850893 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/12-18:08:19.850894 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/12-18:08:19.850894 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/12-18:08:19.850895 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/12-18:08:19.850895 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/12-18:08:19.850895 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/12-18:08:19.850896 **********                   Options.table_properties_collectors: 
2025/07/12-18:08:19.850896 **********                   Options.inplace_update_support: 0
2025/07/12-18:08:19.850897 **********                 Options.inplace_update_num_locks: 10000
2025/07/12-18:08:19.850897 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/12-18:08:19.850897 **********               Options.memtable_whole_key_filtering: 0
2025/07/12-18:08:19.850898 **********   Options.memtable_huge_page_size: 0
2025/07/12-18:08:19.850898 **********                           Options.bloom_locality: 0
2025/07/12-18:08:19.850898 **********                    Options.max_successive_merges: 0
2025/07/12-18:08:19.850899 **********                Options.optimize_filters_for_hits: 0
2025/07/12-18:08:19.850899 **********                Options.paranoid_file_checks: 0
2025/07/12-18:08:19.850899 **********                Options.force_consistency_checks: 1
2025/07/12-18:08:19.850900 **********                Options.report_bg_io_stats: 0
2025/07/12-18:08:19.850900 **********                               Options.ttl: 2592000
2025/07/12-18:08:19.850901 **********          Options.periodic_compaction_seconds: 0
2025/07/12-18:08:19.850901 **********  Options.preclude_last_level_data_seconds: 0
2025/07/12-18:08:19.850901 **********    Options.preserve_internal_time_seconds: 0
2025/07/12-18:08:19.850902 **********                       Options.enable_blob_files: false
2025/07/12-18:08:19.850902 **********                           Options.min_blob_size: 0
2025/07/12-18:08:19.850902 **********                          Options.blob_file_size: 268435456
2025/07/12-18:08:19.850903 **********                   Options.blob_compression_type: NoCompression
2025/07/12-18:08:19.850903 **********          Options.enable_blob_garbage_collection: false
2025/07/12-18:08:19.850903 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.850904 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.850904 **********          Options.blob_compaction_readahead_size: 0
2025/07/12-18:08:19.850905 **********                Options.blob_file_starting_level: 0
2025/07/12-18:08:19.850905 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.850920 ********** [db/db_impl/db_impl.cc:3200] Created column family [erasure_meta] (ID 4)
2025/07/12-18:08:19.852020 ********** [db/column_family.cc:621] --------------- Options for column family [orphans]:
2025/07/12-18:08:19.852022 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/12-18:08:19.852023 **********           Options.merge_operator: None
2025/07/12-18:08:19.852023 **********        Options.compaction_filter: None
2025/07/12-18:08:19.852024 **********        Options.compaction_filter_factory: None
2025/07/12-18:08:19.852024 **********  Options.sst_partitioner_factory: None
2025/07/12-18:08:19.852024 **********         Options.memtable_factory: SkipListFactory
2025/07/12-18:08:19.852025 **********            Options.table_factory: BlockBasedTable
2025/07/12-18:08:19.852032 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x145642060)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x1456420b8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/12-18:08:19.852033 **********        Options.write_buffer_size: 268435456
2025/07/12-18:08:19.852033 **********  Options.max_write_buffer_number: 8
2025/07/12-18:08:19.852033 **********          Options.compression: NoCompression
2025/07/12-18:08:19.852034 **********                  Options.bottommost_compression: Disabled
2025/07/12-18:08:19.852034 **********       Options.prefix_extractor: nullptr
2025/07/12-18:08:19.852035 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/12-18:08:19.852035 **********             Options.num_levels: 7
2025/07/12-18:08:19.852035 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/12-18:08:19.852036 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/12-18:08:19.852036 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/12-18:08:19.852037 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/12-18:08:19.852037 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/12-18:08:19.852037 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/12-18:08:19.852038 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.852038 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.852039 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/12-18:08:19.852039 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/12-18:08:19.852039 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.852040 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.852040 **********            Options.compression_opts.window_bits: -14
2025/07/12-18:08:19.852041 **********                  Options.compression_opts.level: 32767
2025/07/12-18:08:19.852041 **********               Options.compression_opts.strategy: 0
2025/07/12-18:08:19.852041 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.852042 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.852042 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.852042 **********         Options.compression_opts.parallel_threads: 1
2025/07/12-18:08:19.852043 **********                  Options.compression_opts.enabled: false
2025/07/12-18:08:19.852043 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.852044 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.852044 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.852044 **********              Options.level0_stop_writes_trigger: 36
2025/07/12-18:08:19.852045 **********                   Options.target_file_size_base: 107374182
2025/07/12-18:08:19.852045 **********             Options.target_file_size_multiplier: 1
2025/07/12-18:08:19.852045 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.852046 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/12-18:08:19.852046 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.852047 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/12-18:08:19.852047 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/12-18:08:19.852048 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/12-18:08:19.852048 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/12-18:08:19.852048 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/12-18:08:19.852049 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/12-18:08:19.852049 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/12-18:08:19.852049 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.852050 **********                    Options.max_compaction_bytes: 2684354550
2025/07/12-18:08:19.852050 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.852051 **********                        Options.arena_block_size: 1048576
2025/07/12-18:08:19.852051 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.852051 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.852052 **********                Options.disable_auto_compactions: 0
2025/07/12-18:08:19.852052 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/12-18:08:19.852053 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/12-18:08:19.852053 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/12-18:08:19.852053 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/12-18:08:19.852054 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/12-18:08:19.852054 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/12-18:08:19.852055 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/12-18:08:19.852055 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/12-18:08:19.852055 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/12-18:08:19.852056 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/12-18:08:19.852056 **********                   Options.table_properties_collectors: 
2025/07/12-18:08:19.852057 **********                   Options.inplace_update_support: 0
2025/07/12-18:08:19.852057 **********                 Options.inplace_update_num_locks: 10000
2025/07/12-18:08:19.852057 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/12-18:08:19.852058 **********               Options.memtable_whole_key_filtering: 0
2025/07/12-18:08:19.852058 **********   Options.memtable_huge_page_size: 0
2025/07/12-18:08:19.852059 **********                           Options.bloom_locality: 0
2025/07/12-18:08:19.852059 **********                    Options.max_successive_merges: 0
2025/07/12-18:08:19.852059 **********                Options.optimize_filters_for_hits: 0
2025/07/12-18:08:19.852060 **********                Options.paranoid_file_checks: 0
2025/07/12-18:08:19.852060 **********                Options.force_consistency_checks: 1
2025/07/12-18:08:19.852060 **********                Options.report_bg_io_stats: 0
2025/07/12-18:08:19.852061 **********                               Options.ttl: 2592000
2025/07/12-18:08:19.852061 **********          Options.periodic_compaction_seconds: 0
2025/07/12-18:08:19.852061 **********  Options.preclude_last_level_data_seconds: 0
2025/07/12-18:08:19.852062 **********    Options.preserve_internal_time_seconds: 0
2025/07/12-18:08:19.852062 **********                       Options.enable_blob_files: false
2025/07/12-18:08:19.852063 **********                           Options.min_blob_size: 0
2025/07/12-18:08:19.852063 **********                          Options.blob_file_size: 268435456
2025/07/12-18:08:19.852063 **********                   Options.blob_compression_type: NoCompression
2025/07/12-18:08:19.852064 **********          Options.enable_blob_garbage_collection: false
2025/07/12-18:08:19.852064 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.852065 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.852065 **********          Options.blob_compaction_readahead_size: 0
2025/07/12-18:08:19.852065 **********                Options.blob_file_starting_level: 0
2025/07/12-18:08:19.852066 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.852084 ********** [db/db_impl/db_impl.cc:3200] Created column family [orphans] (ID 5)
2025/07/12-18:08:19.853493 ********** [db/column_family.cc:621] --------------- Options for column family [bank_hashes]:
2025/07/12-18:08:19.853494 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/12-18:08:19.853495 **********           Options.merge_operator: None
2025/07/12-18:08:19.853495 **********        Options.compaction_filter: None
2025/07/12-18:08:19.853496 **********        Options.compaction_filter_factory: None
2025/07/12-18:08:19.853496 **********  Options.sst_partitioner_factory: None
2025/07/12-18:08:19.853496 **********         Options.memtable_factory: SkipListFactory
2025/07/12-18:08:19.853497 **********            Options.table_factory: BlockBasedTable
2025/07/12-18:08:19.853504 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x145642f60)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x145642fb8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/12-18:08:19.853505 **********        Options.write_buffer_size: 268435456
2025/07/12-18:08:19.853505 **********  Options.max_write_buffer_number: 8
2025/07/12-18:08:19.853506 **********          Options.compression: NoCompression
2025/07/12-18:08:19.853506 **********                  Options.bottommost_compression: Disabled
2025/07/12-18:08:19.853506 **********       Options.prefix_extractor: nullptr
2025/07/12-18:08:19.853507 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/12-18:08:19.853507 **********             Options.num_levels: 7
2025/07/12-18:08:19.853507 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/12-18:08:19.853508 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/12-18:08:19.853508 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/12-18:08:19.853509 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/12-18:08:19.853509 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/12-18:08:19.853509 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/12-18:08:19.853510 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.853510 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.853511 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/12-18:08:19.853511 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/12-18:08:19.853511 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.853512 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.853512 **********            Options.compression_opts.window_bits: -14
2025/07/12-18:08:19.853513 **********                  Options.compression_opts.level: 32767
2025/07/12-18:08:19.853513 **********               Options.compression_opts.strategy: 0
2025/07/12-18:08:19.853513 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.853514 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.853514 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.853514 **********         Options.compression_opts.parallel_threads: 1
2025/07/12-18:08:19.853515 **********                  Options.compression_opts.enabled: false
2025/07/12-18:08:19.853515 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.853516 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.853516 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.853516 **********              Options.level0_stop_writes_trigger: 36
2025/07/12-18:08:19.853517 **********                   Options.target_file_size_base: 107374182
2025/07/12-18:08:19.853517 **********             Options.target_file_size_multiplier: 1
2025/07/12-18:08:19.853517 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.853518 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/12-18:08:19.853518 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.853519 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/12-18:08:19.853519 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/12-18:08:19.853520 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/12-18:08:19.853520 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/12-18:08:19.853520 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/12-18:08:19.853521 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/12-18:08:19.853521 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/12-18:08:19.853521 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.853522 **********                    Options.max_compaction_bytes: 2684354550
2025/07/12-18:08:19.853522 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.853522 **********                        Options.arena_block_size: 1048576
2025/07/12-18:08:19.853523 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.853523 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.853524 **********                Options.disable_auto_compactions: 0
2025/07/12-18:08:19.853524 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/12-18:08:19.853524 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/12-18:08:19.853525 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/12-18:08:19.853525 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/12-18:08:19.853526 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/12-18:08:19.853526 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/12-18:08:19.853526 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/12-18:08:19.853527 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/12-18:08:19.853527 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/12-18:08:19.853528 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/12-18:08:19.853528 **********                   Options.table_properties_collectors: 
2025/07/12-18:08:19.853529 **********                   Options.inplace_update_support: 0
2025/07/12-18:08:19.853529 **********                 Options.inplace_update_num_locks: 10000
2025/07/12-18:08:19.853529 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/12-18:08:19.853530 **********               Options.memtable_whole_key_filtering: 0
2025/07/12-18:08:19.853530 **********   Options.memtable_huge_page_size: 0
2025/07/12-18:08:19.853530 **********                           Options.bloom_locality: 0
2025/07/12-18:08:19.853531 **********                    Options.max_successive_merges: 0
2025/07/12-18:08:19.853531 **********                Options.optimize_filters_for_hits: 0
2025/07/12-18:08:19.853532 **********                Options.paranoid_file_checks: 0
2025/07/12-18:08:19.853532 **********                Options.force_consistency_checks: 1
2025/07/12-18:08:19.853532 **********                Options.report_bg_io_stats: 0
2025/07/12-18:08:19.853533 **********                               Options.ttl: 2592000
2025/07/12-18:08:19.853533 **********          Options.periodic_compaction_seconds: 0
2025/07/12-18:08:19.853533 **********  Options.preclude_last_level_data_seconds: 0
2025/07/12-18:08:19.853534 **********    Options.preserve_internal_time_seconds: 0
2025/07/12-18:08:19.853534 **********                       Options.enable_blob_files: false
2025/07/12-18:08:19.853535 **********                           Options.min_blob_size: 0
2025/07/12-18:08:19.853535 **********                          Options.blob_file_size: 268435456
2025/07/12-18:08:19.853535 **********                   Options.blob_compression_type: NoCompression
2025/07/12-18:08:19.853536 **********          Options.enable_blob_garbage_collection: false
2025/07/12-18:08:19.853536 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.853536 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.853537 **********          Options.blob_compaction_readahead_size: 0
2025/07/12-18:08:19.853537 **********                Options.blob_file_starting_level: 0
2025/07/12-18:08:19.853538 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.853551 ********** [db/db_impl/db_impl.cc:3200] Created column family [bank_hashes] (ID 6)
2025/07/12-18:08:19.855617 ********** [db/column_family.cc:621] --------------- Options for column family [root]:
2025/07/12-18:08:19.855619 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/12-18:08:19.855619 **********           Options.merge_operator: None
2025/07/12-18:08:19.855620 **********        Options.compaction_filter: None
2025/07/12-18:08:19.855620 **********        Options.compaction_filter_factory: None
2025/07/12-18:08:19.855620 **********  Options.sst_partitioner_factory: None
2025/07/12-18:08:19.855621 **********         Options.memtable_factory: SkipListFactory
2025/07/12-18:08:19.855621 **********            Options.table_factory: BlockBasedTable
2025/07/12-18:08:19.855628 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x145643e60)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x145643eb8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/12-18:08:19.855628 **********        Options.write_buffer_size: 268435456
2025/07/12-18:08:19.855629 **********  Options.max_write_buffer_number: 8
2025/07/12-18:08:19.855629 **********          Options.compression: NoCompression
2025/07/12-18:08:19.855630 **********                  Options.bottommost_compression: Disabled
2025/07/12-18:08:19.855630 **********       Options.prefix_extractor: nullptr
2025/07/12-18:08:19.855630 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/12-18:08:19.855631 **********             Options.num_levels: 7
2025/07/12-18:08:19.855631 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/12-18:08:19.855631 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/12-18:08:19.855632 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/12-18:08:19.855632 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/12-18:08:19.855633 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/12-18:08:19.855633 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/12-18:08:19.855633 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.855634 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.855634 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/12-18:08:19.855634 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/12-18:08:19.855635 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.855635 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.855636 **********            Options.compression_opts.window_bits: -14
2025/07/12-18:08:19.855636 **********                  Options.compression_opts.level: 32767
2025/07/12-18:08:19.855636 **********               Options.compression_opts.strategy: 0
2025/07/12-18:08:19.855637 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.855637 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.855637 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.855638 **********         Options.compression_opts.parallel_threads: 1
2025/07/12-18:08:19.855638 **********                  Options.compression_opts.enabled: false
2025/07/12-18:08:19.855638 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.855639 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.855639 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.855640 **********              Options.level0_stop_writes_trigger: 36
2025/07/12-18:08:19.855640 **********                   Options.target_file_size_base: 107374182
2025/07/12-18:08:19.855640 **********             Options.target_file_size_multiplier: 1
2025/07/12-18:08:19.855641 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.855641 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/12-18:08:19.855641 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.855642 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/12-18:08:19.855642 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/12-18:08:19.855643 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/12-18:08:19.855643 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/12-18:08:19.855643 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/12-18:08:19.855644 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/12-18:08:19.855644 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/12-18:08:19.855644 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.855645 **********                    Options.max_compaction_bytes: 2684354550
2025/07/12-18:08:19.855645 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.855646 **********                        Options.arena_block_size: 1048576
2025/07/12-18:08:19.855646 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.855646 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.855647 **********                Options.disable_auto_compactions: 0
2025/07/12-18:08:19.855647 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/12-18:08:19.855647 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/12-18:08:19.855648 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/12-18:08:19.855648 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/12-18:08:19.855649 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/12-18:08:19.855649 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/12-18:08:19.855649 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/12-18:08:19.855650 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/12-18:08:19.855650 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/12-18:08:19.855651 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/12-18:08:19.855651 **********                   Options.table_properties_collectors: 
2025/07/12-18:08:19.855651 **********                   Options.inplace_update_support: 0
2025/07/12-18:08:19.855652 **********                 Options.inplace_update_num_locks: 10000
2025/07/12-18:08:19.855652 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/12-18:08:19.855653 **********               Options.memtable_whole_key_filtering: 0
2025/07/12-18:08:19.855653 **********   Options.memtable_huge_page_size: 0
2025/07/12-18:08:19.855653 **********                           Options.bloom_locality: 0
2025/07/12-18:08:19.855654 **********                    Options.max_successive_merges: 0
2025/07/12-18:08:19.855654 **********                Options.optimize_filters_for_hits: 0
2025/07/12-18:08:19.855654 **********                Options.paranoid_file_checks: 0
2025/07/12-18:08:19.855655 **********                Options.force_consistency_checks: 1
2025/07/12-18:08:19.855655 **********                Options.report_bg_io_stats: 0
2025/07/12-18:08:19.855655 **********                               Options.ttl: 2592000
2025/07/12-18:08:19.855656 **********          Options.periodic_compaction_seconds: 0
2025/07/12-18:08:19.855656 **********  Options.preclude_last_level_data_seconds: 0
2025/07/12-18:08:19.855657 **********    Options.preserve_internal_time_seconds: 0
2025/07/12-18:08:19.855657 **********                       Options.enable_blob_files: false
2025/07/12-18:08:19.855657 **********                           Options.min_blob_size: 0
2025/07/12-18:08:19.855658 **********                          Options.blob_file_size: 268435456
2025/07/12-18:08:19.855658 **********                   Options.blob_compression_type: NoCompression
2025/07/12-18:08:19.855658 **********          Options.enable_blob_garbage_collection: false
2025/07/12-18:08:19.855659 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.855659 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.855660 **********          Options.blob_compaction_readahead_size: 0
2025/07/12-18:08:19.855660 **********                Options.blob_file_starting_level: 0
2025/07/12-18:08:19.855660 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.855674 ********** [db/db_impl/db_impl.cc:3200] Created column family [root] (ID 7)
2025/07/12-18:08:19.857699 ********** [db/column_family.cc:621] --------------- Options for column family [index]:
2025/07/12-18:08:19.857701 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/12-18:08:19.857702 **********           Options.merge_operator: None
2025/07/12-18:08:19.857702 **********        Options.compaction_filter: None
2025/07/12-18:08:19.857702 **********        Options.compaction_filter_factory: None
2025/07/12-18:08:19.857703 **********  Options.sst_partitioner_factory: None
2025/07/12-18:08:19.857703 **********         Options.memtable_factory: SkipListFactory
2025/07/12-18:08:19.857704 **********            Options.table_factory: BlockBasedTable
2025/07/12-18:08:19.857712 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x145644d60)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x145644db8
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/12-18:08:19.857713 **********        Options.write_buffer_size: 268435456
2025/07/12-18:08:19.857714 **********  Options.max_write_buffer_number: 8
2025/07/12-18:08:19.857714 **********          Options.compression: NoCompression
2025/07/12-18:08:19.857714 **********                  Options.bottommost_compression: Disabled
2025/07/12-18:08:19.857715 **********       Options.prefix_extractor: nullptr
2025/07/12-18:08:19.857715 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/12-18:08:19.857716 **********             Options.num_levels: 7
2025/07/12-18:08:19.857716 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/12-18:08:19.857716 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/12-18:08:19.857717 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/12-18:08:19.857717 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/12-18:08:19.857718 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/12-18:08:19.857718 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/12-18:08:19.857718 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.857719 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.857719 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/12-18:08:19.857720 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/12-18:08:19.857720 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.857720 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.857721 **********            Options.compression_opts.window_bits: -14
2025/07/12-18:08:19.857721 **********                  Options.compression_opts.level: 32767
2025/07/12-18:08:19.857721 **********               Options.compression_opts.strategy: 0
2025/07/12-18:08:19.857722 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.857722 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.857722 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.857723 **********         Options.compression_opts.parallel_threads: 1
2025/07/12-18:08:19.857723 **********                  Options.compression_opts.enabled: false
2025/07/12-18:08:19.857724 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.857724 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.857724 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.857725 **********              Options.level0_stop_writes_trigger: 36
2025/07/12-18:08:19.857725 **********                   Options.target_file_size_base: 107374182
2025/07/12-18:08:19.857725 **********             Options.target_file_size_multiplier: 1
2025/07/12-18:08:19.857726 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.857726 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/12-18:08:19.857727 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.857727 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/12-18:08:19.857727 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/12-18:08:19.857728 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/12-18:08:19.857728 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/12-18:08:19.857729 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/12-18:08:19.857729 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/12-18:08:19.857729 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/12-18:08:19.857730 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.857730 **********                    Options.max_compaction_bytes: 2684354550
2025/07/12-18:08:19.857730 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.857731 **********                        Options.arena_block_size: 1048576
2025/07/12-18:08:19.857731 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.857732 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.857732 **********                Options.disable_auto_compactions: 0
2025/07/12-18:08:19.857732 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/12-18:08:19.857733 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/12-18:08:19.857733 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/12-18:08:19.857733 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/12-18:08:19.857734 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/12-18:08:19.857734 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/12-18:08:19.857735 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/12-18:08:19.857735 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/12-18:08:19.857735 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/12-18:08:19.857736 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/12-18:08:19.857736 **********                   Options.table_properties_collectors: 
2025/07/12-18:08:19.857737 **********                   Options.inplace_update_support: 0
2025/07/12-18:08:19.857737 **********                 Options.inplace_update_num_locks: 10000
2025/07/12-18:08:19.857737 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/12-18:08:19.857738 **********               Options.memtable_whole_key_filtering: 0
2025/07/12-18:08:19.857738 **********   Options.memtable_huge_page_size: 0
2025/07/12-18:08:19.857739 **********                           Options.bloom_locality: 0
2025/07/12-18:08:19.857739 **********                    Options.max_successive_merges: 0
2025/07/12-18:08:19.857739 **********                Options.optimize_filters_for_hits: 0
2025/07/12-18:08:19.857740 **********                Options.paranoid_file_checks: 0
2025/07/12-18:08:19.857740 **********                Options.force_consistency_checks: 1
2025/07/12-18:08:19.857740 **********                Options.report_bg_io_stats: 0
2025/07/12-18:08:19.857741 **********                               Options.ttl: 2592000
2025/07/12-18:08:19.857741 **********          Options.periodic_compaction_seconds: 0
2025/07/12-18:08:19.857741 **********  Options.preclude_last_level_data_seconds: 0
2025/07/12-18:08:19.857742 **********    Options.preserve_internal_time_seconds: 0
2025/07/12-18:08:19.857742 **********                       Options.enable_blob_files: false
2025/07/12-18:08:19.857743 **********                           Options.min_blob_size: 0
2025/07/12-18:08:19.857743 **********                          Options.blob_file_size: 268435456
2025/07/12-18:08:19.857743 **********                   Options.blob_compression_type: NoCompression
2025/07/12-18:08:19.857744 **********          Options.enable_blob_garbage_collection: false
2025/07/12-18:08:19.857744 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.857744 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.857745 **********          Options.blob_compaction_readahead_size: 0
2025/07/12-18:08:19.857745 **********                Options.blob_file_starting_level: 0
2025/07/12-18:08:19.857745 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.857761 ********** [db/db_impl/db_impl.cc:3200] Created column family [index] (ID 8)
2025/07/12-18:08:19.859389 ********** [db/column_family.cc:621] --------------- Options for column family [data_shred]:
2025/07/12-18:08:19.859389 **********               Options.comparator: leveldb.BytewiseComparator
2025/07/12-18:08:19.859390 **********           Options.merge_operator: None
2025/07/12-18:08:19.859390 **********        Options.compaction_filter: None
2025/07/12-18:08:19.859391 **********        Options.compaction_filter_factory: None
2025/07/12-18:08:19.859391 **********  Options.sst_partitioner_factory: None
2025/07/12-18:08:19.859391 **********         Options.memtable_factory: SkipListFactory
2025/07/12-18:08:19.859392 **********            Options.table_factory: BlockBasedTable
2025/07/12-18:08:19.859449 **********            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x14563c640)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x14563c698
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 8388608
    num_shard_bits : 4
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.000
    low_pri_pool_ratio: 0.000
  persistent_cache: 0x0
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/07/12-18:08:19.859450 **********        Options.write_buffer_size: 268435456
2025/07/12-18:08:19.859451 **********  Options.max_write_buffer_number: 8
2025/07/12-18:08:19.859451 **********          Options.compression: NoCompression
2025/07/12-18:08:19.859451 **********                  Options.bottommost_compression: Disabled
2025/07/12-18:08:19.859452 **********       Options.prefix_extractor: nullptr
2025/07/12-18:08:19.859452 **********   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/12-18:08:19.859452 **********             Options.num_levels: 7
2025/07/12-18:08:19.859453 **********        Options.min_write_buffer_number_to_merge: 1
2025/07/12-18:08:19.859453 **********     Options.max_write_buffer_number_to_maintain: 0
2025/07/12-18:08:19.859453 **********     Options.max_write_buffer_size_to_maintain: 0
2025/07/12-18:08:19.859454 **********            Options.bottommost_compression_opts.window_bits: -14
2025/07/12-18:08:19.859454 **********                  Options.bottommost_compression_opts.level: 32767
2025/07/12-18:08:19.859455 **********               Options.bottommost_compression_opts.strategy: 0
2025/07/12-18:08:19.859455 **********         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.859455 **********         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.859456 **********         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/12-18:08:19.859456 **********                  Options.bottommost_compression_opts.enabled: false
2025/07/12-18:08:19.859456 **********         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.859457 **********         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.859457 **********            Options.compression_opts.window_bits: -14
2025/07/12-18:08:19.859458 **********                  Options.compression_opts.level: 32767
2025/07/12-18:08:19.859458 **********               Options.compression_opts.strategy: 0
2025/07/12-18:08:19.859458 **********         Options.compression_opts.max_dict_bytes: 0
2025/07/12-18:08:19.859459 **********         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/12-18:08:19.859459 **********         Options.compression_opts.use_zstd_dict_trainer: true
2025/07/12-18:08:19.859459 **********         Options.compression_opts.parallel_threads: 1
2025/07/12-18:08:19.859460 **********                  Options.compression_opts.enabled: false
2025/07/12-18:08:19.859460 **********         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/12-18:08:19.859461 **********      Options.level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.859461 **********          Options.level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.859461 **********              Options.level0_stop_writes_trigger: 36
2025/07/12-18:08:19.859462 **********                   Options.target_file_size_base: 107374182
2025/07/12-18:08:19.859462 **********             Options.target_file_size_multiplier: 1
2025/07/12-18:08:19.859462 **********                Options.max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.859463 ********** Options.level_compaction_dynamic_level_bytes: 0
2025/07/12-18:08:19.859463 **********          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.859464 ********** Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/12-18:08:19.859464 ********** Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/12-18:08:19.859464 ********** Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/12-18:08:19.859465 ********** Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/12-18:08:19.859465 ********** Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/12-18:08:19.859465 ********** Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/12-18:08:19.859466 ********** Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/12-18:08:19.859466 **********       Options.max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.859466 **********                    Options.max_compaction_bytes: 2684354550
2025/07/12-18:08:19.859467 **********   Options.ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.859467 **********                        Options.arena_block_size: 1048576
2025/07/12-18:08:19.859468 **********   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.859468 **********   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.859468 **********                Options.disable_auto_compactions: 0
2025/07/12-18:08:19.859469 **********                        Options.compaction_style: kCompactionStyleLevel
2025/07/12-18:08:19.859469 **********                          Options.compaction_pri: kMinOverlappingRatio
2025/07/12-18:08:19.859470 ********** Options.compaction_options_universal.size_ratio: 1
2025/07/12-18:08:19.859470 ********** Options.compaction_options_universal.min_merge_width: 2
2025/07/12-18:08:19.859470 ********** Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/12-18:08:19.859471 ********** Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/12-18:08:19.859471 ********** Options.compaction_options_universal.compression_size_percent: -1
2025/07/12-18:08:19.859472 ********** Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/12-18:08:19.859472 ********** Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/12-18:08:19.859472 ********** Options.compaction_options_fifo.allow_compaction: 0
2025/07/12-18:08:19.859473 **********                   Options.table_properties_collectors: 
2025/07/12-18:08:19.859473 **********                   Options.inplace_update_support: 0
2025/07/12-18:08:19.859474 **********                 Options.inplace_update_num_locks: 10000
2025/07/12-18:08:19.859474 **********               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/12-18:08:19.859474 **********               Options.memtable_whole_key_filtering: 0
2025/07/12-18:08:19.859475 **********   Options.memtable_huge_page_size: 0
2025/07/12-18:08:19.859475 **********                           Options.bloom_locality: 0
2025/07/12-18:08:19.859476 **********                    Options.max_successive_merges: 0
2025/07/12-18:08:19.859477 **********                Options.optimize_filters_for_hits: 0
2025/07/12-18:08:19.859477 **********                Options.paranoid_file_checks: 0
2025/07/12-18:08:19.859477 **********                Options.force_consistency_checks: 1
2025/07/12-18:08:19.859478 **********                Options.report_bg_io_stats: 0
2025/07/12-18:08:19.859478 **********                               Options.ttl: 2592000
2025/07/12-18:08:19.859478 **********          Options.periodic_compaction_seconds: 0
2025/07/12-18:08:19.859479 **********  Options.preclude_last_level_data_seconds: 0
2025/07/12-18:08:19.859479 **********    Options.preserve_internal_time_seconds: 0
2025/07/12-18:08:19.859479 **********                       Options.enable_blob_files: false
2025/07/12-18:08:19.859480 **********                           Options.min_blob_size: 0
2025/07/12-18:08:19.859480 **********                          Options.blob_file_size: 268435456
2025/07/12-18:08:19.859481 **********                   Options.blob_compression_type: NoCompression
2025/07/12-18:08:19.859481 **********          Options.enable_blob_garbage_collection: false
2025/07/12-18:08:19.859481 **********      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.859482 ********** Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.859482 **********          Options.blob_compaction_readahead_size: 0
2025/07/12-18:08:19.859483 **********                Options.blob_file_starting_level: 0
2025/07/12-18:08:19.859483 ********** Options.experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.859496 ********** [db/db_impl/db_impl.cc:3200] Created column family [data_shred] (ID 9)
2025/07/12-18:08:19.861427 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.861438 ********** [db/db_impl/db_impl.cc:3200] Created column family [code_shred] (ID 10)
2025/07/12-18:08:19.863264 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.863276 ********** [db/db_impl/db_impl.cc:3200] Created column family [transaction_status] (ID 11)
2025/07/12-18:08:19.865486 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.865498 ********** [db/db_impl/db_impl.cc:3200] Created column family [address_signatures] (ID 12)
2025/07/12-18:08:19.867539 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.867549 ********** [db/db_impl/db_impl.cc:3200] Created column family [transaction_memos] (ID 13)
2025/07/12-18:08:19.869732 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.869743 ********** [db/db_impl/db_impl.cc:3200] Created column family [transaction_status_index] (ID 14)
2025/07/12-18:08:19.872712 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.872734 ********** [db/db_impl/db_impl.cc:3200] Created column family [rewards] (ID 15)
2025/07/12-18:08:19.875407 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.875419 ********** [db/db_impl/db_impl.cc:3200] Created column family [blocktime] (ID 16)
2025/07/12-18:08:19.878256 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.878267 ********** [db/db_impl/db_impl.cc:3200] Created column family [perf_samples] (ID 17)
2025/07/12-18:08:19.881383 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.881393 ********** [db/db_impl/db_impl.cc:3200] Created column family [block_height] (ID 18)
2025/07/12-18:08:19.884319 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.884329 ********** [db/db_impl/db_impl.cc:3200] Created column family [program_costs] (ID 19)
2025/07/12-18:08:19.887758 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.887769 ********** [db/db_impl/db_impl.cc:3200] Created column family [optimistic_slots] (ID 20)
2025/07/12-18:08:19.890909 ********** [db/column_family.cc:624] 	(skipping printing options)
2025/07/12-18:08:19.890919 ********** [db/db_impl/db_impl.cc:3200] Created column family [merkle_root_meta] (ID 21)
2025/07/12-18:08:19.897393 ********** [db/db_impl/db_impl_open.cc:1977] SstFileManager instance 0x145652120
2025/07/12-18:08:19.897414 ********** DB pointer 0x145872800
2025/07/12-18:08:19.897859 6147715072 [db/db_impl/db_impl.cc:1085] ------- DUMPING STATS -------
2025/07/12-18:08:19.897861 6147715072 [db/db_impl/db_impl.cc:1086] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0, 
** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x145650618#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 8e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14563e4c8#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 5e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [meta] **

** Compaction Stats [dead_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [dead_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14563f3b8#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 5e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [dead_slots] **

** Compaction Stats [duplicate_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [duplicate_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x1456402b8#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 4e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [duplicate_slots] **

** Compaction Stats [erasure_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [erasure_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x1456411b8#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [erasure_meta] **

** Compaction Stats [orphans] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [orphans] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x1456420b8#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [orphans] **

** Compaction Stats [bank_hashes] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [bank_hashes] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x145642fb8#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [bank_hashes] **

** Compaction Stats [root] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [root] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x145643eb8#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [root] **

** Compaction Stats [index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x145644db8#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [index] **

** Compaction Stats [data_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [data_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14563c698#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [data_shred] **

** Compaction Stats [code_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [code_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14563d588#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [code_shred] **

** Compaction Stats [transaction_status] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x145645cb8#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status] **

** Compaction Stats [address_signatures] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [address_signatures] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x145646c88#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [address_signatures] **

** Compaction Stats [transaction_memos] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_memos] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x145647c58#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_memos] **

** Compaction Stats [transaction_status_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x145648c28#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status_index] **

** Compaction Stats [rewards] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [rewards] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x145649b38#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [rewards] **

** Compaction Stats [blocktime] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [blocktime] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14564aa38#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 2e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [blocktime] **

** Compaction Stats [perf_samples] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [perf_samples] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14564b938#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 2e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [perf_samples] **

** Compaction Stats [block_height] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [block_height] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14564c838#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [block_height] **

** Compaction Stats [program_costs] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [program_costs] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14564d738#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [program_costs] **

** Compaction Stats [optimistic_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [optimistic_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14564e638#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [optimistic_slots] **

** Compaction Stats [merkle_root_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [merkle_root_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0, Block cache LRUCache@0x14564f538#31299 capacity: 8.00 MB usage: 0.08 KB table_size: 256 occupancy: 87 collections: 1 last_copies: 0 last_secs: 3e-06 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [merkle_root_meta] **
2025/07/12-18:08:19.901065 ********** [db/db_impl/db_impl.cc:1187] SetOptions() on column family [transaction_status], inputs:
2025/07/12-18:08:19.901067 ********** [db/db_impl/db_impl.cc:1190] periodic_compaction_seconds: 86400
2025/07/12-18:08:19.901068 ********** [db/db_impl/db_impl.cc:1194] [transaction_status] SetOptions() succeeded
2025/07/12-18:08:19.901068 ********** [options/cf_options.cc:1004]                         write_buffer_size: 268435456
2025/07/12-18:08:19.901069 ********** [options/cf_options.cc:1006]                   max_write_buffer_number: 8
2025/07/12-18:08:19.901069 ********** [options/cf_options.cc:1009]                          arena_block_size: 1048576
2025/07/12-18:08:19.901070 ********** [options/cf_options.cc:1011]               memtable_prefix_bloom_ratio: 0.000000
2025/07/12-18:08:19.901070 ********** [options/cf_options.cc:1013]               memtable_whole_key_filtering: 0
2025/07/12-18:08:19.901071 ********** [options/cf_options.cc:1016]                   memtable_huge_page_size: 0
2025/07/12-18:08:19.901071 ********** [options/cf_options.cc:1019]                     max_successive_merges: 0
2025/07/12-18:08:19.901071 ********** [options/cf_options.cc:1022]                  inplace_update_num_locks: 10000
2025/07/12-18:08:19.901072 ********** [options/cf_options.cc:1026]                          prefix_extractor: nullptr
2025/07/12-18:08:19.901072 ********** [options/cf_options.cc:1028]                  disable_auto_compactions: 0
2025/07/12-18:08:19.901073 ********** [options/cf_options.cc:1030]       soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.901073 ********** [options/cf_options.cc:1032]       hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.901073 ********** [options/cf_options.cc:1034]        level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.901074 ********** [options/cf_options.cc:1036]            level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.901074 ********** [options/cf_options.cc:1038]                level0_stop_writes_trigger: 36
2025/07/12-18:08:19.901075 ********** [options/cf_options.cc:1040]                      max_compaction_bytes: 2684354550
2025/07/12-18:08:19.901075 ********** [options/cf_options.cc:1042]     ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.901075 ********** [options/cf_options.cc:1044]                     target_file_size_base: 107374182
2025/07/12-18:08:19.901076 ********** [options/cf_options.cc:1046]               target_file_size_multiplier: 1
2025/07/12-18:08:19.901076 ********** [options/cf_options.cc:1048]                  max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.901077 ********** [options/cf_options.cc:1050]            max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.901077 ********** [options/cf_options.cc:1052]                                       ttl: 2592000
2025/07/12-18:08:19.901077 ********** [options/cf_options.cc:1054]               periodic_compaction_seconds: 86400
2025/07/12-18:08:19.901078 ********** [options/cf_options.cc:1068] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/07/12-18:08:19.901079 ********** [options/cf_options.cc:1070]         max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.901079 ********** [options/cf_options.cc:1072]          check_flush_compaction_key_order: 1
2025/07/12-18:08:19.901079 ********** [options/cf_options.cc:1074]                      paranoid_file_checks: 0
2025/07/12-18:08:19.901080 ********** [options/cf_options.cc:1076]                        report_bg_io_stats: 0
2025/07/12-18:08:19.901080 ********** [options/cf_options.cc:1078]                               compression: 0
2025/07/12-18:08:19.901080 ********** [options/cf_options.cc:1081]                        experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.901081 ********** [options/cf_options.cc:1085] compaction_options_universal.size_ratio : 1
2025/07/12-18:08:19.901081 ********** [options/cf_options.cc:1087] compaction_options_universal.min_merge_width : 2
2025/07/12-18:08:19.901082 ********** [options/cf_options.cc:1089] compaction_options_universal.max_merge_width : -1
2025/07/12-18:08:19.901082 ********** [options/cf_options.cc:1092] compaction_options_universal.max_size_amplification_percent : 200
2025/07/12-18:08:19.901082 ********** [options/cf_options.cc:1095] compaction_options_universal.compression_size_percent : -1
2025/07/12-18:08:19.901083 ********** [options/cf_options.cc:1097] compaction_options_universal.stop_style : 1
2025/07/12-18:08:19.901083 ********** [options/cf_options.cc:1100] compaction_options_universal.allow_trivial_move : 0
2025/07/12-18:08:19.901084 ********** [options/cf_options.cc:1102] compaction_options_universal.incremental        : 0
2025/07/12-18:08:19.901084 ********** [options/cf_options.cc:1106] compaction_options_fifo.max_table_files_size : 1073741824
2025/07/12-18:08:19.901084 ********** [options/cf_options.cc:1108] compaction_options_fifo.allow_compaction : 0
2025/07/12-18:08:19.901085 ********** [options/cf_options.cc:1112]                         enable_blob_files: false
2025/07/12-18:08:19.901085 ********** [options/cf_options.cc:1114]                             min_blob_size: 0
2025/07/12-18:08:19.901086 ********** [options/cf_options.cc:1116]                            blob_file_size: 268435456
2025/07/12-18:08:19.901086 ********** [options/cf_options.cc:1118]                     blob_compression_type: NoCompression
2025/07/12-18:08:19.901086 ********** [options/cf_options.cc:1120]            enable_blob_garbage_collection: false
2025/07/12-18:08:19.901087 ********** [options/cf_options.cc:1122]        blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.901087 ********** [options/cf_options.cc:1124]   blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.901088 ********** [options/cf_options.cc:1126]            blob_compaction_readahead_size: 0
2025/07/12-18:08:19.901088 ********** [options/cf_options.cc:1128]                  blob_file_starting_level: 0
2025/07/12-18:08:19.901088 ********** [options/cf_options.cc:1132]                    prepopulate_blob_cache: disable
2025/07/12-18:08:19.901089 ********** [options/cf_options.cc:1134]                    last_level_temperature: 0
2025/07/12-18:08:19.904727 ********** [db/db_impl/db_impl.cc:1187] SetOptions() on column family [address_signatures], inputs:
2025/07/12-18:08:19.904727 ********** [db/db_impl/db_impl.cc:1190] periodic_compaction_seconds: 86400
2025/07/12-18:08:19.904728 ********** [db/db_impl/db_impl.cc:1194] [address_signatures] SetOptions() succeeded
2025/07/12-18:08:19.904728 ********** [options/cf_options.cc:1004]                         write_buffer_size: 268435456
2025/07/12-18:08:19.904729 ********** [options/cf_options.cc:1006]                   max_write_buffer_number: 8
2025/07/12-18:08:19.904729 ********** [options/cf_options.cc:1009]                          arena_block_size: 1048576
2025/07/12-18:08:19.904730 ********** [options/cf_options.cc:1011]               memtable_prefix_bloom_ratio: 0.000000
2025/07/12-18:08:19.904730 ********** [options/cf_options.cc:1013]               memtable_whole_key_filtering: 0
2025/07/12-18:08:19.904731 ********** [options/cf_options.cc:1016]                   memtable_huge_page_size: 0
2025/07/12-18:08:19.904731 ********** [options/cf_options.cc:1019]                     max_successive_merges: 0
2025/07/12-18:08:19.904731 ********** [options/cf_options.cc:1022]                  inplace_update_num_locks: 10000
2025/07/12-18:08:19.904732 ********** [options/cf_options.cc:1026]                          prefix_extractor: nullptr
2025/07/12-18:08:19.904732 ********** [options/cf_options.cc:1028]                  disable_auto_compactions: 0
2025/07/12-18:08:19.904732 ********** [options/cf_options.cc:1030]       soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.904733 ********** [options/cf_options.cc:1032]       hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.904733 ********** [options/cf_options.cc:1034]        level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.904734 ********** [options/cf_options.cc:1036]            level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.904734 ********** [options/cf_options.cc:1038]                level0_stop_writes_trigger: 36
2025/07/12-18:08:19.904734 ********** [options/cf_options.cc:1040]                      max_compaction_bytes: 2684354550
2025/07/12-18:08:19.904735 ********** [options/cf_options.cc:1042]     ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.904735 ********** [options/cf_options.cc:1044]                     target_file_size_base: 107374182
2025/07/12-18:08:19.904735 ********** [options/cf_options.cc:1046]               target_file_size_multiplier: 1
2025/07/12-18:08:19.904736 ********** [options/cf_options.cc:1048]                  max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.904736 ********** [options/cf_options.cc:1050]            max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.904737 ********** [options/cf_options.cc:1052]                                       ttl: 2592000
2025/07/12-18:08:19.904737 ********** [options/cf_options.cc:1054]               periodic_compaction_seconds: 86400
2025/07/12-18:08:19.904738 ********** [options/cf_options.cc:1068] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/07/12-18:08:19.904738 ********** [options/cf_options.cc:1070]         max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.904738 ********** [options/cf_options.cc:1072]          check_flush_compaction_key_order: 1
2025/07/12-18:08:19.904739 ********** [options/cf_options.cc:1074]                      paranoid_file_checks: 0
2025/07/12-18:08:19.904739 ********** [options/cf_options.cc:1076]                        report_bg_io_stats: 0
2025/07/12-18:08:19.904740 ********** [options/cf_options.cc:1078]                               compression: 0
2025/07/12-18:08:19.904740 ********** [options/cf_options.cc:1081]                        experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.904740 ********** [options/cf_options.cc:1085] compaction_options_universal.size_ratio : 1
2025/07/12-18:08:19.904741 ********** [options/cf_options.cc:1087] compaction_options_universal.min_merge_width : 2
2025/07/12-18:08:19.904741 ********** [options/cf_options.cc:1089] compaction_options_universal.max_merge_width : -1
2025/07/12-18:08:19.904741 ********** [options/cf_options.cc:1092] compaction_options_universal.max_size_amplification_percent : 200
2025/07/12-18:08:19.904742 ********** [options/cf_options.cc:1095] compaction_options_universal.compression_size_percent : -1
2025/07/12-18:08:19.904742 ********** [options/cf_options.cc:1097] compaction_options_universal.stop_style : 1
2025/07/12-18:08:19.904743 ********** [options/cf_options.cc:1100] compaction_options_universal.allow_trivial_move : 0
2025/07/12-18:08:19.904743 ********** [options/cf_options.cc:1102] compaction_options_universal.incremental        : 0
2025/07/12-18:08:19.904743 ********** [options/cf_options.cc:1106] compaction_options_fifo.max_table_files_size : 1073741824
2025/07/12-18:08:19.904744 ********** [options/cf_options.cc:1108] compaction_options_fifo.allow_compaction : 0
2025/07/12-18:08:19.904744 ********** [options/cf_options.cc:1112]                         enable_blob_files: false
2025/07/12-18:08:19.904744 ********** [options/cf_options.cc:1114]                             min_blob_size: 0
2025/07/12-18:08:19.904745 ********** [options/cf_options.cc:1116]                            blob_file_size: 268435456
2025/07/12-18:08:19.904745 ********** [options/cf_options.cc:1118]                     blob_compression_type: NoCompression
2025/07/12-18:08:19.904746 ********** [options/cf_options.cc:1120]            enable_blob_garbage_collection: false
2025/07/12-18:08:19.904746 ********** [options/cf_options.cc:1122]        blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.904747 ********** [options/cf_options.cc:1124]   blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.904747 ********** [options/cf_options.cc:1126]            blob_compaction_readahead_size: 0
2025/07/12-18:08:19.904747 ********** [options/cf_options.cc:1128]                  blob_file_starting_level: 0
2025/07/12-18:08:19.904748 ********** [options/cf_options.cc:1132]                    prepopulate_blob_cache: disable
2025/07/12-18:08:19.904748 ********** [options/cf_options.cc:1134]                    last_level_temperature: 0
2025/07/12-18:08:19.908120 ********** [db/db_impl/db_impl.cc:1187] SetOptions() on column family [transaction_memos], inputs:
2025/07/12-18:08:19.908121 ********** [db/db_impl/db_impl.cc:1190] periodic_compaction_seconds: 86400
2025/07/12-18:08:19.908121 ********** [db/db_impl/db_impl.cc:1194] [transaction_memos] SetOptions() succeeded
2025/07/12-18:08:19.908122 ********** [options/cf_options.cc:1004]                         write_buffer_size: 268435456
2025/07/12-18:08:19.908122 ********** [options/cf_options.cc:1006]                   max_write_buffer_number: 8
2025/07/12-18:08:19.908123 ********** [options/cf_options.cc:1009]                          arena_block_size: 1048576
2025/07/12-18:08:19.908123 ********** [options/cf_options.cc:1011]               memtable_prefix_bloom_ratio: 0.000000
2025/07/12-18:08:19.908124 ********** [options/cf_options.cc:1013]               memtable_whole_key_filtering: 0
2025/07/12-18:08:19.908124 ********** [options/cf_options.cc:1016]                   memtable_huge_page_size: 0
2025/07/12-18:08:19.908124 ********** [options/cf_options.cc:1019]                     max_successive_merges: 0
2025/07/12-18:08:19.908125 ********** [options/cf_options.cc:1022]                  inplace_update_num_locks: 10000
2025/07/12-18:08:19.908125 ********** [options/cf_options.cc:1026]                          prefix_extractor: nullptr
2025/07/12-18:08:19.908125 ********** [options/cf_options.cc:1028]                  disable_auto_compactions: 0
2025/07/12-18:08:19.908126 ********** [options/cf_options.cc:1030]       soft_pending_compaction_bytes_limit: 68719476736
2025/07/12-18:08:19.908126 ********** [options/cf_options.cc:1032]       hard_pending_compaction_bytes_limit: 274877906944
2025/07/12-18:08:19.908127 ********** [options/cf_options.cc:1034]        level0_file_num_compaction_trigger: 4
2025/07/12-18:08:19.908127 ********** [options/cf_options.cc:1036]            level0_slowdown_writes_trigger: 20
2025/07/12-18:08:19.908127 ********** [options/cf_options.cc:1038]                level0_stop_writes_trigger: 36
2025/07/12-18:08:19.908128 ********** [options/cf_options.cc:1040]                      max_compaction_bytes: 2684354550
2025/07/12-18:08:19.908128 ********** [options/cf_options.cc:1042]     ignore_max_compaction_bytes_for_input: true
2025/07/12-18:08:19.908129 ********** [options/cf_options.cc:1044]                     target_file_size_base: 107374182
2025/07/12-18:08:19.908129 ********** [options/cf_options.cc:1046]               target_file_size_multiplier: 1
2025/07/12-18:08:19.908129 ********** [options/cf_options.cc:1048]                  max_bytes_for_level_base: 1073741824
2025/07/12-18:08:19.908130 ********** [options/cf_options.cc:1050]            max_bytes_for_level_multiplier: 10.000000
2025/07/12-18:08:19.908130 ********** [options/cf_options.cc:1052]                                       ttl: 2592000
2025/07/12-18:08:19.908130 ********** [options/cf_options.cc:1054]               periodic_compaction_seconds: 86400
2025/07/12-18:08:19.908131 ********** [options/cf_options.cc:1068] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/07/12-18:08:19.908132 ********** [options/cf_options.cc:1070]         max_sequential_skip_in_iterations: 8
2025/07/12-18:08:19.908132 ********** [options/cf_options.cc:1072]          check_flush_compaction_key_order: 1
2025/07/12-18:08:19.908132 ********** [options/cf_options.cc:1074]                      paranoid_file_checks: 0
2025/07/12-18:08:19.908133 ********** [options/cf_options.cc:1076]                        report_bg_io_stats: 0
2025/07/12-18:08:19.908133 ********** [options/cf_options.cc:1078]                               compression: 0
2025/07/12-18:08:19.908133 ********** [options/cf_options.cc:1081]                        experimental_mempurge_threshold: 0.000000
2025/07/12-18:08:19.908134 ********** [options/cf_options.cc:1085] compaction_options_universal.size_ratio : 1
2025/07/12-18:08:19.908134 ********** [options/cf_options.cc:1087] compaction_options_universal.min_merge_width : 2
2025/07/12-18:08:19.908135 ********** [options/cf_options.cc:1089] compaction_options_universal.max_merge_width : -1
2025/07/12-18:08:19.908135 ********** [options/cf_options.cc:1092] compaction_options_universal.max_size_amplification_percent : 200
2025/07/12-18:08:19.908135 ********** [options/cf_options.cc:1095] compaction_options_universal.compression_size_percent : -1
2025/07/12-18:08:19.908136 ********** [options/cf_options.cc:1097] compaction_options_universal.stop_style : 1
2025/07/12-18:08:19.908136 ********** [options/cf_options.cc:1100] compaction_options_universal.allow_trivial_move : 0
2025/07/12-18:08:19.908136 ********** [options/cf_options.cc:1102] compaction_options_universal.incremental        : 0
2025/07/12-18:08:19.908137 ********** [options/cf_options.cc:1106] compaction_options_fifo.max_table_files_size : 1073741824
2025/07/12-18:08:19.908137 ********** [options/cf_options.cc:1108] compaction_options_fifo.allow_compaction : 0
2025/07/12-18:08:19.908138 ********** [options/cf_options.cc:1112]                         enable_blob_files: false
2025/07/12-18:08:19.908138 ********** [options/cf_options.cc:1114]                             min_blob_size: 0
2025/07/12-18:08:19.908138 ********** [options/cf_options.cc:1116]                            blob_file_size: 268435456
2025/07/12-18:08:19.908139 ********** [options/cf_options.cc:1118]                     blob_compression_type: NoCompression
2025/07/12-18:08:19.908139 ********** [options/cf_options.cc:1120]            enable_blob_garbage_collection: false
2025/07/12-18:08:19.908139 ********** [options/cf_options.cc:1122]        blob_garbage_collection_age_cutoff: 0.250000
2025/07/12-18:08:19.908140 ********** [options/cf_options.cc:1124]   blob_garbage_collection_force_threshold: 1.000000
2025/07/12-18:08:19.908140 ********** [options/cf_options.cc:1126]            blob_compaction_readahead_size: 0
2025/07/12-18:08:19.908141 ********** [options/cf_options.cc:1128]                  blob_file_starting_level: 0
2025/07/12-18:08:19.908141 ********** [options/cf_options.cc:1132]                    prepopulate_blob_cache: disable
2025/07/12-18:08:19.908141 ********** [options/cf_options.cc:1134]                    last_level_temperature: 0
2025/07/12-18:08:19.911129 ********** [db/db_impl/db_impl.cc:490] Shutdown: canceling all background work
2025/07/12-18:08:19.911393 ********** [db/db_impl/db_impl.cc:692] Shutdown complete
