#!/bin/bash

# Solana 开发环境设置脚本
# 由于 macOS 兼容性问题，使用 devnet 作为开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 配置 Solana CLI
setup_solana_config() {
    print_message $BLUE "⚙️  配置 Solana CLI..."
    
    # 设置为 devnet
    solana config set --url devnet
    print_message $GREEN "✅ 已设置为 devnet"
    
    # 生成密钥对（如果不存在）
    if [ ! -f "$HOME/.config/solana/id.json" ]; then
        print_message $BLUE "🔑 生成新的密钥对..."
        solana-keygen new --outfile "$HOME/.config/solana/id.json" --no-passphrase
    else
        print_message $GREEN "✅ 密钥对已存在"
    fi
    
    # 显示配置
    print_message $BLUE "📋 当前配置:"
    solana config get
    
    # 显示钱包地址
    WALLET_ADDRESS=$(solana address)
    print_message $GREEN "💳 钱包地址: $WALLET_ADDRESS"
}

# 获取测试 SOL
get_test_sol() {
    print_message $BLUE "💰 获取测试 SOL..."
    
    WALLET_ADDRESS=$(solana address)
    
    # 请求空投
    print_message $BLUE "📡 请求空投 2 SOL..."
    if solana airdrop 2 "$WALLET_ADDRESS"; then
        print_message $GREEN "✅ 空投成功"
        
        # 检查余额
        print_message $BLUE "💳 检查余额..."
        solana balance
    else
        print_message $RED "❌ 空投失败"
        print_message $YELLOW "💡 可能是请求过于频繁，请稍后再试"
    fi
}

# 测试连接
test_connection() {
    print_message $BLUE "🧪 测试 Solana 连接..."
    
    # 检查网络连接
    print_message $BLUE "📡 检查网络连接..."
    if solana cluster-version; then
        print_message $GREEN "✅ 网络连接正常"
    else
        print_message $RED "❌ 网络连接失败"
        return 1
    fi
    
    # 检查钱包余额
    print_message $BLUE "💳 检查钱包余额..."
    BALANCE=$(solana balance 2>/dev/null | cut -d' ' -f1)
    if [ "$BALANCE" != "0" ]; then
        print_message $GREEN "✅ 钱包余额: $BALANCE SOL"
    else
        print_message $YELLOW "⚠️  钱包余额为 0，需要获取测试 SOL"
        get_test_sol
    fi
}

# 显示开发环境信息
show_dev_info() {
    print_message $BLUE "📋 Solana 开发环境信息"
    echo ""
    
    print_message $GREEN "🌐 网络配置:"
    solana config get
    echo ""
    
    print_message $GREEN "💳 钱包信息:"
    WALLET_ADDRESS=$(solana address)
    echo "地址: $WALLET_ADDRESS"
    echo "余额: $(solana balance)"
    echo ""
    
    print_message $GREEN "🔗 有用的链接:"
    echo "• Solana Explorer: https://explorer.solana.com/?cluster=devnet"
    echo "• 你的钱包: https://explorer.solana.com/address/$WALLET_ADDRESS?cluster=devnet"
    echo "• Devnet Faucet: https://faucet.solana.com/"
    echo ""
    
    print_message $GREEN "💡 常用命令:"
    echo "• 获取测试 SOL: solana airdrop 2"
    echo "• 查看余额: solana balance"
    echo "• 转账: solana transfer <地址> <金额>"
    echo "• 查看交易: solana confirm <签名>"
}

# 尝试启动本地验证者
try_local_validator() {
    print_message $BLUE "🚀 尝试启动本地验证者..."
    print_message $YELLOW "⚠️  注意：在 Apple Silicon Mac 上可能会失败"
    
    # 清理旧数据
    rm -rf test-ledger
    
    # 尝试启动（后台运行）
    print_message $BLUE "🔄 启动验证者..."
    if solana-test-validator --ledger test-ledger --reset --quiet &
    then
        VALIDATOR_PID=$!
        echo $VALIDATOR_PID > validator.pid
        print_message $GREEN "✅ 验证者已启动 (PID: $VALIDATOR_PID)"
        
        # 等待启动
        sleep 10
        
        # 检查是否成功
        if kill -0 $VALIDATOR_PID 2>/dev/null && curl -s http://localhost:8899/health >/dev/null; then
            print_message $GREEN "✅ 本地验证者运行正常"
            print_message $BLUE "📡 RPC 端点: http://localhost:8899"
            
            # 切换到本地网络
            solana config set --url localhost
            print_message $GREEN "✅ 已切换到本地网络"
            
            return 0
        else
            print_message $RED "❌ 验证者启动失败"
            kill $VALIDATOR_PID 2>/dev/null || true
            rm -f validator.pid
            solana config set --url devnet
            return 1
        fi
    else
        print_message $RED "❌ 无法启动验证者"
        solana config set --url devnet
        return 1
    fi
}

# 停止本地验证者
stop_local_validator() {
    print_message $BLUE "🛑 停止本地验证者..."
    
    if [ -f validator.pid ]; then
        PID=$(cat validator.pid)
        if kill -0 $PID 2>/dev/null; then
            kill $PID
            print_message $GREEN "✅ 验证者已停止"
        else
            print_message $YELLOW "⚠️  验证者进程不存在"
        fi
        rm -f validator.pid
    else
        print_message $YELLOW "⚠️  未找到验证者 PID 文件"
    fi
    
    # 强制清理
    pkill -f solana-test-validator 2>/dev/null || true
    
    # 切换回 devnet
    solana config set --url devnet
    print_message $GREEN "✅ 已切换回 devnet"
}

# 显示帮助
show_help() {
    echo "Solana 开发环境设置脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  setup         设置开发环境（推荐首次使用）"
    echo "  airdrop       获取测试 SOL"
    echo "  test          测试连接"
    echo "  info          显示环境信息"
    echo "  local         尝试启动本地验证者"
    echo "  stop          停止本地验证者"
    echo "  help          显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 setup      # 初始化开发环境"
    echo "  $0 airdrop    # 获取测试 SOL"
    echo "  $0 local      # 尝试启动本地验证者"
}

# 主函数
main() {
    case "${1:-help}" in
        setup)
            setup_solana_config
            get_test_sol
            show_dev_info
            ;;
        airdrop)
            get_test_sol
            ;;
        test)
            test_connection
            ;;
        info)
            show_dev_info
            ;;
        local)
            try_local_validator
            ;;
        stop)
            stop_local_validator
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
