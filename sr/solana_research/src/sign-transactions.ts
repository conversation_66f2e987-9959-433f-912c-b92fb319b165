import { getTransferSolInstruction } from "@solana-program/system";
import {
  appendTransactionMessageInstructions,
  createTransactionMessage,
  getSignatureFromTransaction,
  KeyPairSigner,
  lamports,
  pipe,
  setTransactionMessageFeePayerSigner,
  setTransactionMessageLifetimeUsingBlockhash,
  signTransactionMessageWithSigners,
} from "@solana/kit";
import { ENetworkTypes, getNetwork } from "./utils/get-netwrok";

interface ISignTransactionOptions {
  sender: KeyPairSigner;
  recipient: KeyPairSigner;
  amount: bigint;
  networkType: ENetworkTypes;
}
export async function signSolTransaction(options: ISignTransactionOptions) {
  const { sender, recipient, amount, networkType } = options;

  const { rpc } = getNetwork(networkType);

  const transferAmount = lamports(amount);

  const transferInstruction = getTransferSolInstruction({
    source: sender,
    destination: recipient.address,
    amount: transferAmount,
  });

  const { value: latestBlockhash } = await rpc.getLatestBlockhash().send();
  const transactionMessage = pipe(
    createTransactionMessage({ version: 0 }),
    (tx) => setTransactionMessageFeePayerSigner(sender, tx),
    (tx) => setTransactionMessageLifetimeUsingBlockhash(latestBlockhash, tx),
    (tx) => appendTransactionMessageInstructions([transferInstruction], tx)
  );

  const signedTransaction = await signTransactionMessageWithSigners(
    transactionMessage
  );
  const transactionSignature = getSignatureFromTransaction(signedTransaction);
  return {
    transactionSignature,
    signedTransaction,
  };
}
