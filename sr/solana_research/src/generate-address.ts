import { createKeyPairSignerFromPrivateKeyBytes } from "@solana/kit";
import * as bip39 from "bip39";
import { HDKey } from "micro-ed25519-hdkey";

export async function generateAddress(
  mnemonic: string,
  index: number
): Promise<string> {
  const seed = bip39.mnemonicToSeedSync(mnemonic);
  const hd = HDKey.fromMasterSeed(seed.toString("hex"));

  const path = `m/44'/501'/${index}'/0'`;
  const child = hd.derive(path);

  const signer = await createKeyPairSignerFromPrivateKeyBytes(
    new Uint8Array(child.privateKey)
  );
  return signer.address;
}
