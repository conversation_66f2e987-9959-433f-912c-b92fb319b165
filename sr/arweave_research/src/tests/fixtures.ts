import { JWKInterface, WarpFactory } from "warp-contracts";
import { DeployPlugin } from "warp-contracts-plugin-deploy";
import { client } from "../client";
import { generateAddress } from "../generate-address";

interface IDeployTokenOptions {
  ownerKey: JW<PERSON>Interface;
  initialSupply: number;
  /** default to 12 */
  decimals?: number;
}
export async function deployToken(options: IDeployTokenOptions) {
  const { ownerKey, initialSupply, decimals = 12 } = options;
  // 创建 Warp 实例（连接到我们的 arlocal）
  const warp = WarpFactory.forLocal(Number(process.env.ARWEAVE_PORT)).use(
    new DeployPlugin()
  );

  const ownerAddress = await warp.arweave.wallets.getAddress(ownerKey);

  console.log(`📝 Warp 钱包地址: ${ownerAddress}`);

  // 使用我们的 PST 合约源码
  const contractSrc = `
export async function handle(state, action) {
  const balances = state.balances;
  const input = action.input;
  const caller = action.caller;

  if (input.function === 'transfer') {
    const target = input.target;
    const qty = input.qty;

    if (!Number.isInteger(qty)) {
      throw new ContractError('Invalid value for "qty". Must be an integer');
    }

    if (!target) {
      throw new ContractError('No target specified');
    }

    if (qty <= 0 || caller === target) {
      throw new ContractError('Invalid token transfer');
    }

    if (!balances[caller] || balances[caller] < qty) {
      throw new ContractError(\`Caller balance not high enough to send \${qty} token(s)!\`);
    }

    // Lower the token balance of the caller
    balances[caller] -= qty;
    if (target in balances) {
      // Wallet already exists in state, add new tokens
      balances[target] += qty;
    } else {
      // Wallet is new, set starting balance
      balances[target] = qty;
    }

    return { state };
  }

  if (input.function === 'balance') {
    const target = input.target || caller;
    const ticker = state.ticker;
    const balance = balances[target] || 0;

    return { result: { target, ticker, balance } };
  }

  throw new ContractError(\`No function supplied or function not recognized: "\${input.function}"\`);
}
`;

  const contractInitState = {
    ticker: "WARP",
    name: "Warp Test Token",
    owner: ownerAddress,
    canEvolve: true,
    balances: {
      [ownerAddress]: initialSupply,
    },
    decimals,
  };

  console.log(`🚀 使用 Warp SDK 部署合约`);

  // 使用 Warp SDK 部署合约
  const { contractTxId } = await warp.deploy({
    wallet: ownerKey,
    initState: JSON.stringify(contractInitState),
    src: contractSrc,
  });

  console.log(`✅ Warp 合约已部署: ${contractTxId}`);

  // 挖矿确认部署
  await warp.testing.mineBlock();

  return {
    contractTxId,
  };
}

const mint = async (address: string, amountAr: string) => {
  console.log(`Minting ${amountAr} AR to ${address}`);
  const amountWinston = client.ar.arToWinston(amountAr);
  await client.api.get(`mint/${address}/${amountWinston}`);
};

export const createWallets = async (count = 10, amountAr = "1000") => {
  const wallets = new Array(count);
  for (let index = 0; index < count; index++) {
    wallets[index] = await generateAddress();

    await mint(wallets[index].address, amountAr);
  }

  return wallets;
};

export const mine = async () => {
  console.log("Mining block...");
  await client.api.get("mine");
  console.log("Block mined successfully");
};
