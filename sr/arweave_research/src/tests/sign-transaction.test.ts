import { Pst<PERSON>ontract, WarpFactory } from "warp-contracts";
import { DeployPlugin } from "warp-contracts-plugin-deploy";
import { client } from "../client";
import { generateAddress } from "../generate-address";
import {
  signTokenTransferTransaction,
  signTransaction,
} from "../sign-transaction";
import { createWallets, deployToken } from "./fixtures";

describe("signTransaction", () => {
  let fromWallet: Awaited<ReturnType<typeof generateAddress>>;
  let toWallet: Awaited<ReturnType<typeof generateAddress>>;

  const mint = async (address: string, amountAr: string) => {
    console.log(`Minting ${amountAr} AR to ${address}`);
    // The mint endpoint expects winston units, not AR units
    const amountWinston = client.ar.arToWinston(amountAr);
    await client.api.get(`mint/${address}/${amountWinston}`);
    const balance = await client.wallets.getBalance(address);
    expect(balance).toEqual(amountWinston);
    console.log(`Balance after minting: ${client.ar.winstonToAr(balance)} AR`);
  };

  const mine = async () => {
    console.log("Mining block...");
    await client.api.get("mine");
    console.log("Block mined successfully");
  };

  beforeEach(async () => {
    console.log("Setting up test wallets...");
    fromWallet = await generateAddress();
    toWallet = await generateAddress();

    console.log(`From wallet address: ${fromWallet.address}`);
    console.log(`To wallet address: ${toWallet.address}`);

    await mint(fromWallet.address, "2");
    await mint(toWallet.address, "1");
  });

  it("should sign Native Token transaction, and then broadcast, verify successfully", async () => {
    const transferAmount = "0.3";

    // 获取初始余额
    const initialFromBalance = await client.wallets.getBalance(
      fromWallet.address
    );
    const initialToBalance = await client.wallets.getBalance(toWallet.address);

    console.log(
      `📊 Initial balances - From: ${client.ar.winstonToAr(
        initialFromBalance
      )} AR, To: ${client.ar.winstonToAr(initialToBalance)} AR`
    );

    // 1. 创建并签名交易
    const transaction = await signTransaction({
      key: fromWallet.key,
      target: toWallet.address,
      quantity: client.ar.arToWinston(transferAmount),
    });

    // 验证交易已正确签名
    expect(transaction.id).toBeDefined();
    expect(transaction.signature).toBeDefined();
    const isValid = await client.transactions.verify(transaction);
    expect(isValid).toBe(true);
    console.log(`✅ Transaction signed successfully: ${transaction.id}`);

    // 2. 广播交易到网络
    const submitResult = await client.transactions.post(transaction);
    expect(submitResult.status).toBe(200);
    console.log(`📡 Transaction broadcasted successfully: ${transaction.id}`);

    // 3. 挖矿确认交易
    await mine();
    console.log(`⛏️ Block mined to confirm transaction`);

    // 5. 验证余额变化
    const finalFromBalance = await client.wallets.getBalance(
      fromWallet.address
    );
    const finalToBalance = await client.wallets.getBalance(toWallet.address);

    console.log(
      `📊 Final balances - From: ${client.ar.winstonToAr(
        finalFromBalance
      )} AR, To: ${client.ar.winstonToAr(finalToBalance)} AR`
    );

    // 验证接收方余额增加了转账金额
    const expectedToBalance =
      BigInt(initialToBalance) + BigInt(client.ar.arToWinston(transferAmount));
    expect(finalToBalance).toBe(expectedToBalance.toString());

    // 验证发送方余额减少了转账金额（在本地区块链中手续费为0）
    const expectedFromBalance =
      BigInt(initialFromBalance) -
      BigInt(client.ar.arToWinston(transferAmount));
    expect(finalFromBalance).toBe(expectedFromBalance.toString());

    console.log(`🎉 Complete transaction lifecycle verified successfully!`);
  });

  it(
    "should sign Token transfer transaction using raw Arweave SDK, broadcast and verify successfully",
    async () => {
      const [ownerWallet, userWallet] = await createWallets(2);
      const { contractTxId } = await deployToken({
        ownerKey: ownerWallet.key,
        initialSupply: 1000000,
      });

      // 使用原生Arweave SDK创建token转账交易

      // 查询余额的辅助函数
      const queryBalance = async (contractId: string, target: string) => {
        const warp = WarpFactory.forLocal(Number(process.env.ARWEAVE_PORT)).use(
          new DeployPlugin()
        );
        const contract = warp.pst(contractId) as PstContract;

        try {
          const result = await contract.currentBalance(target);
          return result.balance;
        } catch (error) {
          return 0; // 地址不存在余额时返回0
        }
      };

      // 1. 查询初始余额
      console.log(`📊 查询初始余额...`);
      const initialOwnerBalance = await queryBalance(
        contractTxId,
        ownerWallet.address
      );
      const initialUserBalance = await queryBalance(
        contractTxId,
        userWallet.address
      );

      console.log(`Owner初始余额: ${initialOwnerBalance}`);
      console.log(`User初始余额: ${initialUserBalance}`);

      // 2. 创建并签名转账交易
      const transferAmount = 75000;
      console.log(`💸 创建转账交易: ${transferAmount} tokens`);

      const transaction = await signTokenTransferTransaction({
        contractId: contractTxId,
        key: ownerWallet.key,
        target: userWallet.address,
        quantity: transferAmount.toString(),
      });

      // 验证交易已正确签名
      expect(transaction.id).toBeDefined();
      expect(transaction.signature).toBeDefined();

      // 验证标签（需要解码base64）
      const ArweaveUtils = require("arweave/node/lib/utils");

      const contractTag = transaction.tags.find(
        (tag) => ArweaveUtils.b64UrlToString(tag.name) === "Contract"
      );
      expect(contractTag).toBeDefined();
      if (contractTag) {
        expect(ArweaveUtils.b64UrlToString(contractTag.value)).toBe(
          contractTxId
        );
      }

      const appNameTag = transaction.tags.find(
        (tag) => ArweaveUtils.b64UrlToString(tag.name) === "App-Name"
      );
      expect(appNameTag).toBeDefined();
      if (appNameTag) {
        expect(ArweaveUtils.b64UrlToString(appNameTag.value)).toBe(
          "SmartWeaveAction"
        );
      }

      const isValid = await client.transactions.verify(transaction);
      expect(isValid).toBe(true);
      console.log(`✅ 交易签名验证成功: ${transaction.id}`);

      // 3. 广播交易到网络
      const submitResult = await client.transactions.post(transaction);
      expect(submitResult.status).toBe(200);
      console.log(`📡 交易广播成功: ${transaction.id}`);

      // 4. 挖矿确认交易
      await mine();
      console.log(`⛏️ 区块挖矿完成`);

      // 5. 验证余额变化
      console.log(`📊 查询转账后余额...`);
      const finalOwnerBalance = await queryBalance(
        contractTxId,
        ownerWallet.address
      );
      const finalUserBalance = await queryBalance(
        contractTxId,
        userWallet.address
      );

      console.log(`Owner最终余额: ${finalOwnerBalance}`);
      console.log(`User最终余额: ${finalUserBalance}`);

      // 6. 验证余额变化正确
      expect(finalOwnerBalance).toBe(initialOwnerBalance - transferAmount);
      expect(finalUserBalance).toBe(initialUserBalance + transferAmount);

      console.log(`🎉 原生 SDK Token转账测试完成!`);
    },
    1000 * 60 * 60
  );
});
