import { JWKInterface } from "warp-contracts";
import { client } from "./client";

interface ISignTransactionOptions {
  /**
   * 发起人密钥
   */
  key: JWKInterface;
  /**
   * 目标账户地址
   */
  target: string;
  /**
   * Winston
   */
  quantity?: string;
}

export async function signTransaction(options: ISignTransactionOptions) {
  const { key, quantity, target } = options;
  const transaction = await client.createTransaction({
    target,
    quantity,
  });

  await client.transactions.sign(transaction, key);

  return transaction;
}

interface ISignTokenTransactionOptions {
  quantity: string;
  contractId: string;
  target: string;
  key: JWKInterface;
}

export async function signTokenTransferTransaction(
  options: ISignTokenTransactionOptions
) {
  const { quantity, contractId, target, key } = options;

  const input = {
    function: "transfer",
    target: target,
    qty: Number(quantity),
  };

  const transaction = await client.createTransaction(
    {
      data: JSON.stringify(input),
    },
    key
  );

  transaction.addTag("App-Name", "SmartWeaveAction");
  transaction.addTag("App-Version", "0.3.0");
  transaction.addTag("Contract", contractId);
  transaction.addTag("Input", JSON.stringify(input));

  await client.transactions.sign(transaction, key);
  return transaction;
}
