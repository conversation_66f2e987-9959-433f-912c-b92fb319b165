# Arweave 链调研

## 账户模型还是 UTXO？

Arweave 的核心是账户模型，因为它为每个地址维护一个明确的余额。它并非 UTXO 模型。其独特之处在于使用 TX_anchor 来连接一个账户的连续交易，以此实现安全性和顺序性，这与以太坊使用 nonce 的方式功能类似但实现不同。

## 共识机制

它通过三个关键设计实现这一点：

- 随机历史访问：
  挖矿必须引用一个不可预测的随机历史区块。这迫使矿工必须拥有并能随时访问历史数据，否则就失去了参与竞争的资格。
- 高成本的诚实门槛：
  数据在存储前需经过高昂的计算“打包”。这一成本使得临时下载数据来作弊的代价，远高于长期诚实存储的成本，从而确保了存储的真实性。
- 轻量化的验证：
  最终用于证明的加密证据（SPoA）本身非常小且验证极快，这保证了网络的验证效率不会因数据总量的增长而降低。同时，可验证延迟函数（VDF）控制着整体节奏，确保出块时间稳定。

## 是否为多链结构？

单链

## 代币精度

AR 代币精度：12 位小数
最小单位：Winston (1 AR = 10^12 Winston)
精度处理：使用 BigNumber 库处理高精度计算

## 密码学算法

签名算法：RSA-PSS (RSA Probabilistic Signature Scheme)
哈希算法：SHA-256
密钥长度：4096 位 RSA 密钥对
地址生成：基于 RSA 公钥的 SHA-256 哈希

## Tag/Memo 支持

支持 Tags。

结构：一个交易可以包含任意数量的 Tag。每个 Tag 都是一个键值对（Key-Value Pair）。
键（Name）：标签的名称，例如 Content-Type。
值（Value）：标签的具体内容，例如 image/jpeg。
格式：这些键值对是为任意元数据（metadata）设计的，允许开发者和用户为每一笔交易附加丰富的、结构化的信息。
限制：一笔交易中所有 Tag 序列化后的总大小上限为 2048 字节。
作用：数据发现和查询。

```ts
const transaction = await arweave.createTransaction({
  data: "Hello Arweave!",
});

// 添加标签
transaction.addTag("Content-Type", "text/plain");
transaction.addTag("App-Name", "MyApp");
transaction.addTag("Version", "1.0.0");
```

## 交易体系构建

Arweave 的交易分为两种交易类型，他们的应用场景如下：

- 数据交易：数据交易用于在 Arweave 永久网络上存储数据。合约是基于此加上 SmartWeave 协议来实现的。
- 钱包交易：实现原生代币的转账。

一笔交易的数据结构：

```ts
// 这是一个Arweave交易对象的结构示例
interface ArweaveTransaction {
  // 交易的唯一ID，通过对签名进行SHA-256哈希生成
  id: string;

  // 发送者的RSA公钥 (注意：是完整的公钥，不是地址)
  // 用于验证签名，代表交易的所有权
  owner: string;

  // 交易的接收方地址 (对于纯数据上传，此字段可以为空)
  target: string;

  // 发送的原生代币AR数量，单位是 Winston (1 AR = 10^12 Winston)
  // 对于数据上传或合约交互，此值可以为 "0"
  quantity: string;

  // 交易的核心载荷（Payload），可以是任何数据，上限约为10MB
  // 对于数据上传，这里是文件内容。
  // 对于合约交互，这里通常是描述操作的JSON字符串。
  // 对于原生AR转账，此字段为空。
  data: string; // Base64URL编码

  // 用户为本次交易支付的费用，单位是 Winston
  // 这笔费用大部分会进入存储捐赠基金，而不是直接给矿工。
  reward: string;

  // 交易锚点：发送者账户的上一笔交易的ID。
  // 这是防止重放攻击的核心机制。
  last_tx: string;

  // 标签：一个由键值对组成的数组，用于添加元数据。
  // 这是实现数据查询和构建dApp的基石。
  tags: { name: string; value: string }[];

  // 使用发送者私钥对以上关键字段生成的数字签名
  // 证明了交易的真实性和完整性
  signature: string;
}
```

### 原生代币

这是 Arweave 网络的基础货币，其机制被“写死”在区块链的核心协议里。

- 模型：账户模型 (Account Model)
  AR 代币不使用 UTXO 模型。相反，Arweave 网络维护一个全局的**“钱包列表”（Wallet List）**，这个列表就像一个总账本，记录了每个地址的确切 AR 余额。
- 交易逻辑：直接修改余额
  当一笔 AR 转账发生时，它是一个原生的 Arweave 交易。矿工在验证区块时，会直接执行以下操作：
  减少发送方地址在“钱包列表”中的余额。
  增加接收方地址在“钱包列表”中的余额。
  这个状态变更是由全网共识保证的，并且直接体现在链上。
- 安全与顺序：交易锚点 (TX_anchor)
  为了防止重放攻击并为每个账户的交易排序，每笔 AR 交易都必须引用该发送方账户的上一笔交易的哈希值。这为每个账户创建了一条自己的、由哈希连接的内部交易链。
  一句话总结：AR 代币是协议的一部分，其状态由链上共识直接管理，简单、直接且安全。

使用 SDK 创建原生代币交易：

```ts
import Arweave from "arweave";

const client = Arweave.init({
  host: "localhost",
  port: 1984,
  protocol: "http",
});

interface ISignTransactionOptions {
  /**
   * 发起人密钥
   */
  key: JWKInterface;
  /**
   * 目标账户地址
   */
  target: string;
  /**
   * Winston
   */
  quantity?: string;
}

export async function signTransaction(options: ISignTransactionOptions) {
  const { key, quantity, target } = options;
  const transaction = await client.createTransaction({
    target,
    quantity,
  });

  await client.transactions.sign(transaction, key);

  return transaction;
}
```

### 同质化代币

自定义代币不是协议的原生功能，而是通过**链下计算（Off-chain Computation）**的智能合约模型实现的。
这个模型的核心理念是：Arweave 只作为不可篡改的“数据日志”，真正的计算发生在需要知道结果的客户端。
它的实现依赖于三个核心要素：

- 合约源代码 (The Rules | 规则书)：一个代币的逻辑（如何转账、谁是所有者等）被写在一个 JavaScript 文件中。这个 JS 文件本身作为一笔交易，被永久上传到 Arweave。这份代码是公开的、不可篡改的“规则书”，任何人都可以下载它来了解这个代币的行为准则。
- 初始状态 (The Genesis | 创世文件)：在部署合约时，会定义一个 JSON 对象作为代币的初始状态。这是代币的“创世文件”，定义了它的起点。它包含了代币的所有创世信息，例如

  ```js
  {
      ticker: "MYTOKEN",
      name: "My Custom Token",
      owner: "xxxx",
      initialSupply: 1000000,
      decimals: 8,
      description: "My custom ERC20 token",
  };
  ```

- 交互历史 (The Ledger | 交易日志)：之后发生的所有操作，比如 A 转给 B 10 个代币，不是一笔原生交易。它是一笔数据交易，这笔交易的数据部分是一个描述操作的 JSON 对象，例如：`{ "function": "transfer", "target": "address_of_B", "qty": 10 }`，这笔数据交易会通过 Tag 与合约源代码的 ID 关联起来。所有这些交互交易共同构成了这个代币的、不可篡改的**“交易日志”**。

- 最终状态的计算：当你想查询你拥有多少“MAT”代币时，Arweave 矿工什么都不做。你的客户端（或 Warp 网关）会执行以下步骤：
  - 下载规则书（合约的 JS 源代码）。
  - 下载创世文件（初始状态）。
  - 下载交易日志（所有与该合约相关的交互历史）。
  - 在本地“重放”：你的客户端会像播放录像带一样，从初始状态开始，按照时间顺序，用 JS 代码逐一执行所有的交互。当所有交互都执行完毕后，得出的最终状态就是当前的状态。

```ts
import Arweave from "arweave";

const client = Arweave.init({
  host: "localhost",
  port: 1984,
  protocol: "http",
});

interface ISignTokenTransactionOptions {
  quantity: string;
  contractId: string;
  target: string;
  key: JWKInterface;
}

export async function signTokenTransferTransaction(
  options: ISignTokenTransactionOptions
) {
  const { quantity, contractId, target, key } = options;

  const input = {
    function: "transfer",
    target: target,
    qty: Number(quantity),
  };

  const transaction = await client.createTransaction(
    {
      data: JSON.stringify(input),
    },
    key
  );

  transaction.addTag("App-Name", "SmartWeaveAction");
  transaction.addTag("App-Version", "0.3.0");
  transaction.addTag("Contract", contractId);
  transaction.addTag("Input", JSON.stringify(input));

  await client.transactions.sign(transaction, key);
  return transaction;
}
```

## 交易解析

### Native Token

```ts
const isNativeTransfer = (transaction: any): boolean => {
  return (
    transaction.target &&
    transaction.quantity &&
    transaction.quantity !== "0" &&
    !transaction.tags.find((tag: any) => tag.name === "Contract")
  );
};
```

### 同质化代币

```ts
const parseTokenTransaction = (transaction: any) => {
  const contractTag = transaction.tags.find(
    (tag: any) => tag.name === "Contract"
  );
  const inputTag = transaction.tags.find((tag: any) => tag.name === "Input");

  if (contractTag && inputTag) {
    const input = JSON.parse(inputTag.value);
    return {
      type: "token",
      contract: contractTag.value,
      function: input.function,
      target: input.target,
      quantity: input.qty,
    };
  }
  return null;
};
```

## 地址生成

```ts
import Arweave from "arweave";
import { JWKInterface } from "arweave/node/lib/wallet";

const generateAddress = async (): Promise<{
  address: string;
  wallet: JWKInterface;
}> => {
  // 1. 生成 RSA 密钥对
  const wallet = await arweave.wallets.generate();

  // 2. 从公钥生成地址
  const address = await arweave.wallets.getAddress(wallet);

  return { address, wallet };
};
```

## 手续费

### 手续费计算

```ts
// 手续费计算公式
const calculateFee = async (dataSize: number, target?: string) => {
  // 基础费用 = 数据大小 * 每字节价格
  const price = await arweave.transactions.getPrice(dataSize, target);

  // 转换为 AR
  const feeInAR = arweave.ar.winstonToAr(price);

  return {
    winston: price,
    ar: feeInAR,
  };
};
```

### 手续费预测

```ts
const predictFee = async (data: string | Uint8Array) => {
  const dataSize =
    typeof data === "string"
      ? new TextEncoder().encode(data).length
      : data.length;

  const currentPrice = await arweave.transactions.getPrice(dataSize);

  // 添加 10% 缓冲
  const bufferedPrice = Math.ceil(parseInt(currentPrice) * 1.1).toString();

  return {
    current: arweave.ar.winstonToAr(currentPrice),
    recommended: arweave.ar.winstonToAr(bufferedPrice),
  };
};
```

## API 接口

| 接口名称                  | 说明         | 入参           | 返回值           |
| ------------------------- | ------------ | -------------- | ---------------- |
| /info                     | 获取网络信息 | 无             | 网络状态、高度等 |
| /wallet/{address}/balance | 获取余额     | address        | Winston 余额     |
| /tx/{id}                  | 获取交易详情 | transaction_id | 交易数据         |
| /tx/{id}/status           | 获取交易状态 | transaction_id | 确认状态         |
| /price/{bytes}            | 获取存储价格 | 数据大小       | Winston 价格     |
| /price/{bytes}/{target}   | 获取转账价格 | 大小+目标地址  | Winston 价格     |
| /tx                       | 提交交易     | 交易数据       | 提交结果         |
| /block/height/{height}    | 获取区块     | 区块高度       | 区块数据         |
| /peers                    | 获取节点列表 | 无             | 节点信息         |
| /tx_anchor                | 获取交易锚点 | 无             | 最新锚点         |

- 主网：https://arweave.net
- 本地启动一条 Arweave 链：`docker run --name arlocal -p 1984:1984 textury/arlocal`

### 获取最新区块

```bash
curl --location 'https://arweave.net/info'
```

```json
{
  "version": 5,
  "release": 84,
  "queue_length": 0,
  "peers": 368,
  "node_state_latency": 0,
  "network": "arweave.N.1",
  "height": 1710585,
  "current": "R3n1FZJwPGbltt1ny7SMaqY_v0PP6Ajl1jMemuqKlN9s5H8jlFX4WA4cqt2yM7F8",
  "blocks": 1710586
}
```

### 获取区块中的交易列表

```bash
curl --location 'https://arweave.net/block/hash/R3n1FZJwPGbltt1ny7SMaqY_v0PP6Ajl1jMemuqKlN9s5H8jlFX4WA4cqt2yM7F8'
```

```json
{
  "txs": [
    // 交易列表
    "eVWqUcrwtKZ8auhtkjpm4wtZFWecMwXk_r_c_akeE7E",
    "08QGVi4XBjMbYTnwNF0--PVGqKmC00yiscljP8ytQ1Q",
    "CnyUXQD1niZgnTwvT4mF8oIwW2-kBKKlfha-hR9uxKo",
    "17RJQedmOjYmX_xlhC6dj9QiPjlIx52YwWcaVnqx1-U",
    "37X8p8Z9nxwbPyaoHZhOs5SuON3wNhME_wfuSsVDmVU",
    "EBrudmq_WCft9sYvY4R5-Idi8Rs9jt36wgNivX0GdPE",
    "ztzT7aVY1O_KJlUu-Exb8UXwz5Ewfc_m_XXNAHXI2Vk"
  ],
  "replica_format": 1,
  "packing_difficulty": 10,
  "unpacked_chunk_hash": "VmEKklaYtld3YLdf-HMns7Dub1IT54C4wySBybU7oX0",
  "unpacked_chunk2_hash": "Qh8mwg5owg2VubKRsVa649rx9YKnkrpYcGJ0U3Wzchs",
  "chunk2_hash": "Ry4pR_XTCVCqg01AA61PtrhzW-F7qSmv0GOobynxrzw",
  "merkle_rebase_support_threshold": "151066495197430",
  "chunk_hash": "JUKRw7Ku8xNr0pbSmk66TDzRLQ64BIJZPcOyYLCpHqc",
  "block_time_history_hash": "0Bj8sl1QEINB4JMXRqZlYqsl_WY99VA-ZJHMrrAy8rk",
  "recall_byte2": "91820321902568",
  "hash_preimage": "bCcAAY823UQKWAgXNm8AjsQ8juPj8FC9fQzncgrDhqs",
  "recall_byte": "325110089047056",
  "reward": "320483384866",
  "previous_solution_hash": "____-poQLA26dTGBcIHJUlRqkFUIfWcJbPa0b_HktnQ",
  "partition_number": 90,
  "nonce_limiter_info": {
    "output": "tZjb3vSDN7T9GviiJMqvfvH2OCfMrxJRnjvjKKsbb_c",
    "global_step_number": 77830628,
    "seed": "cjTE6jWl8PHGbG21jd-9RcT2pIp3pyHrDs9XuWC7zYBQqUUwcymDskgw68Uu3ZiE",
    "next_seed": "y1cuxRnMEeglluXWHM5jFSonkknBRU8k_QJSvrwFWP2ktqqtOzZUyKjSkdKYoRmo",
    "zone_upper_bound": 349305638002934,
    "next_zone_upper_bound": 349310582563062,
    "prev_output": "5MF4rN70orFqxqkVy8Kh4gqVVDfgIv9h5oRe3W7WJVU",
    "last_step_checkpoints": [
      "tZjb3vSDN7T9GviiJMqvfvH2OCfMrxJRnjvjKKsbb_c",
      "MbFI5lObrJEL0wEeqLaYQH22BiQYpK4SThu8P6wt7jI",
      "9NBPxIRd-cZFklOIawbL_h5V8FAW5GHR2EU9M5lNKek",
      "pNElZbeU6jjKCqp7EsGfFrZZGFxSLQ72vGr8gwir5P0",
      "qYaz_y_fq4HIX-MthJotuIrOUzbCXCP3nsie3oGNb10",
      "bbmmngUGOBxrCMRQj6QJsRSSJMMCpDeu1LVlsyhO6vE",
      "lFqswGKRRXhVM6_98NWC0C9jon5QZv-Xi1jsi0SXq1E",
      "OilgMk1w1mYDIZ1zfQKIxZ4qmYZxK5ivzKRrBPkZVu0",
      "9AXVCw8yXL7Y1HJF1WgF3YMe2GextOfzHEUnGtlMo-o",
      "DVWI2IVJsYwNG9NH0BKjMmxR6CddqoNar-nl31FLtHs",
      "LzoaxBuiqNdRzkXA3s2Zlt_a_pENetpaIbn3e1ZRNBA",
      "-Vq_81fBFsP4X3PfE2DrYYvMuXPjtFQizYPDagxoHuI",
      "1XEumauc3GgGQ3gRyfBj9p2ifGCDDDtGC7s7LkXt55k",
      "A5rJvSKfk15_sArpdN5AFGbSWZS6jhJKO9DuNFcinaA",
      "M4cX_MpQLOOHyUghNubDBkrXft5T98caO_CSQjAtf24",
      "3u2D4wBH1gr27KAQPbvfd9jX1b07XRkLWtGFlPFs51k",
      "8repAEdF39IeBrvKOTSXGhNcdzpTXhyAGqHScACqB6U",
      "rcU5mNhdc1pCt06Dr_CuIrqcH0tXFBcB-w-YOlOnDY0",
      "jNyN2OaGGMfvaKY9KCKObeRGQ9OMH3KDIZIYw-UgEvI",
      "2icyb7qVDuJz6HswPFiet8ZAMcMkutMABRtYBD1ODa8",
      "5EEQ9-AodpJoP5duRr_0hr2i2dIpyPewLrXQudpeAlM",
      "1Ro6LREJjkU9ZF5t45HhsJ5403585EmUflc7r1NADG8",
      "WvtvSlFWaxll_Rw0MebLr1ZpyxOjm7nRwgiEHd0H8y0",
      "oQEvhmOUzycJ8NnCG_-mxUX0l7_PAT5O3VwuTMe-sjU",
      "vlkuMDbg9LwZtH_BgLW9qkYkRyidV0PmiWNP_jnV15M"
    ],
    "checkpoints": [
      "tZjb3vSDN7T9GviiJMqvfvH2OCfMrxJRnjvjKKsbb_c",
      "eIjacuNfx4xBsjBayL0m2-v0QL-Bimll2aVEvYZqixo",
      "xpYJvadDKsFqv4xSYmelKCFHCFUqsdTz24YvYjFzbeY",
      "PNj0m7VzGc5lr0M4UkuO9Th_MXLqnnEI3pEu34-f43s",
      "t5j63q6iH5lYcWyiZtZsPjL6AEjh69am6xkBSGQqbaw",
      "qglffEndo9m4CKezS8hKkVnLPcL9JjEWjYy7X-Fq1_Q",
      "J7TaI9KXYYuY7obIXOTfyvaKmy71IyDPM9UDREt21pE",
      "LVi5nJ6YV9w1o3Ir1pWnL3f_eILk8CWP2RUvPG4g2NI"
    ],
    "vdf_difficulty": "1067228",
    "next_vdf_difficulty": "1067228"
  },
  "poa2": {
    "option": "1",
    "tx_path": "6iFPQvKW37ghoJNYRZpX_58QFafgZhCGVJ7UUfO-3gD_aMAGzBexpp7cWFHOlMSUMHfnmDw7FpbPLg3QSiyA7AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAANAAAwwAJwgBGy96qwoKhPOtndK4aP7NxZNw7ILXPxViKnZX_A78KhJzNbH8HNCbiCliG48JG1je3dGTpuOqwIX3mxAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADkAAAJHTa8VirdpN_jGLFkj21Z0XFSjECo6rE1DIgbbFw8k9YJ2NlqptMRaTf6WTp8wfdCKJVecRGSZxqFKhJ0grUlgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADjAAAnRTf_2Z5gB9hh7unjiqN5czgBund64xuSaXMaF2F0ofPMlTQZLmuqOCPrqM10kKeyGCVNEpcC66kp-NELR-k2AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADiz9j0PSQrbvVEoAobzgaKS35Hqqb1P7bamLW1HjVs3_p7SsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4s_Yw",
    "data_path": "fujan04XUO0ql0f-obljuH...",
    "chunk": "2feAjNglDzyB9n...",
    "unpacked_chunk": "aGF0J3MgYSBr..."
  },
  "signature": "edM2Q_30zymaQQY1nGbM0pim8Cex7qJgoh4qXzOMOAUP2e0Vq2LYLsT66E3oMxdltNBhnvJ-TOYIkvLHqzZsdEGRiooP300EQvUHDEkUEgrXTz5EHp134PXxLzTI__a8kG8k2_7eyFFMhlP71u8H4NamEtcUZyuG_NKutHRKh8tVEUE1swouXOTuVKI0jFj_igAxZwcZElZ9sThxi-YAju_gc9qJcNIeoKMvfwgdfayiC63fNczQvF5n65xehDxjQgO8UCbk9ZSDTaHl2mLIb2QrWgJvBsnT5EhmSxflRQcAEel2dGoThp_KTlD79LRIS3SyzsnGauo_RBlhj6Qwmugm2vwx93bFYaru69W_94RPa3AdmQ_h4hTGkE7bBHDLhtcEDgEn8Llwyx6uc3RbXogafX3n169_7XGm8yaERAF6NMT2r_fxlUQOqdeir18qh4jK753QD3vrNz3Xgd0gcqtRSi-tt02Nie3pmd0cvtM-i07ckkTucYIgWVUh1PrMsyy_vSqOky2DeAshUiyBoimg7EmT67ghtqxnRn_HGPX9V84pXcknmjlv7XA4SufVLfJ0tPWo-J02FU4paSJrne8weGfRZOxLGhpnb8libk1Gew-kPL4VpkxjekHUlzJXH8KHxR7FYdR6bA4d_Y5l83goIdLNb_TVQAnNC-zNHlk",
  "reward_key": "vn8KhyHMCJOGj..",
  "price_per_gib_minute": "1023",
  "scheduled_price_per_gib_minute": "1022",
  "reward_history_hash": "OChMC1ZqIbHl_PhflMQ732i6MSMzVTwfQ-LSbeVXYw0",
  "debt_supply": "0",
  "kryder_plus_rate_multiplier": "1",
  "kryder_plus_rate_multiplier_latch": "0",
  "denomination": "1",
  "redenomination_height": 0,
  "double_signing_proof": {},
  "previous_cumulative_diff": "5252272532419240",
  "usd_to_ar_rate": ["1", "10"],
  "scheduled_usd_to_ar_rate": ["1", "10"],
  "packing_2_5_threshold": "0",
  "strict_data_split_threshold": "30607159107830",
  "nonce": "AA",
  "previous_block": "NDrzyvVwQxx9tpw3c_W0vGkb57Xd-CKfDwQuXK2sA9mMixBEaGkF9id8HTLwJpIY",
  "timestamp": 1752390777,
  "last_retarget": 1752390247,
  "diff": "115792089067412060833702478895352807316288768768996085390872824889320936052398",
  "height": 1710585,
  "hash": "____-Z3m5_Ea5UsLOo4vIc_9TUaT1ay3Hr2G82dy8SI",
  "indep_hash": "R3n1FZJwPGbltt1ny7SMaqY_v0PP6Ajl1jMemuqKlN9s5H8jlFX4WA4cqt2yM7F8",

  "tx_root": "fjEMqm9-OMWYtJ_vbyGwu4bRFO3u6tqQP2QIoOkf-S8",
  "wallet_list": "8zXCMH1TsxWGb01tMruDKuVhf1SIr2lf4enuUm4i19dTaR30A62wVRcz6qR185oq",
  "reward_addr": "IqYrh2ctBcrKAnnj6URGCBHdG0hftRAavOGs6WuJ7iA",
  "tags": [],
  "reward_pool": "245234195969997955",
  "weave_size": "349313319084278",
  "block_size": "5505024",
  "cumulative_diff": "5252273213933492",
  "hash_list_merkle": "SshskYdyXL7hk0jFtCXoXWFfa1Qtdnum4w58bqR6bxOLZuYar2bh10U-Ga7NS7Ql",
  "poa": {
    "option": "1",
    "tx_path": "UKKC02A0sfh7tA0nIMxro1gHpxNVYt5funghXJV-7Ff7FMyr1JqVLqEdbNj5Jlea3Cx5fOJz1ZOP_qtMk37OogAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABWFNEWCWqtoBzCyZlyAOSkBliRLAJj_VGhKofpQau9_KUsnGIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVhTRFg",
    "data_path": "NQ6r5Iu7vuUUEqe4b_nyR...",
    "chunk": "IIZYOI4D1lic...",
    "unpacked_chunk": "hmRC0SN..."
  }
}
```

### 获取交易信息

```bash
curl --location 'https://arweave.net/tx/eVWqUcrwtKZ8auhtkjpm4wtZFWecMwXk_r_c_akeE7E'
```

原生代币转账：

```json
{
  "format": 2,
  "id": "sRNuH316FAoCDIEvnnI7GbG7SZgYJ3XeVjiHhPwjYa0",
  "last_tx": "KSIg4MYKJPTEbkex-vQLtuCAkiYEuaF98mz5qF4Du-WYf17foofsYeyJJi5P1-vy",
  "owner": "mBf535DajOZ8i18gGqfsJjot2PXFo7qvq_Kvvw7BE1gOEACdPQlel3Zl8d3U5GKgEwymDhVyyQFep4shx1G7HLIYv51jJfvlGi-LInPqSHxdqvYoC2a-K-JtyTRl2fquHHceXgKUxVqeoSKF2NTFpdgkdcPNPS1n806YfiMUXbHq87h1B80YcFDwOqLi63JWD4hmIJMeJlZuXdnbghAEICe2NyraNmQx1rFJkKZ53WvDP6GMUjjFFePe9bfDDEO9xb27dm9EjSV0zwwALzi1QZ0ZIfzpqUAnvb80SiKmHNYurl4loPkIOPE_p2ONP5osTDg3MRpZh9J0U-s1AW5vPk88WmyGq3r7TTnwnNdAo_RxfzPaHAbT5Oq0cJ7C1GMtukEXoGH-xSrEQpT7FuziOMyI1ga_3LjVDUhU-qaykJvjvmtWXOoIYxTrWGnUd8tAoP28D5U9U3oD3w0XdHAiSNxGIcxa2ZA5IBZn9drXa_BSgqw8Yahus4TlGhVBEX_q5_XkzYEodYnTD3sSU82dYsyXI54VteyNk7H5ErT6QLNLHXhnR8_q_MbAgGEZx7tWNYBtnfInf_vrmCf0i6_fNErSrjk1zr1KKluXrRKUH8aX6L57B6DRBIKDh92GgFUvVkY2VMXBQRKn_re_dou_6vfb-8GMn6FCALVDg1u6M1U",
  "tags": [],
  "target": "5wPG4QWZsa0LcjB3-k5NQWbeLSktex-R9fEg14GTuTw",
  "quantity": "1000000000",
  "data": "",
  "data_size": "0",
  "data_tree": [],
  "data_root": "",
  "reward": "41842404941",
  "signature": "RQVFNN4UgLU-QeWEzrAj9VXCvJQeB6qCI1KNp-d6SHppTfmFySRg8olUApU3I1nXkpn_xBKjsnec-SW6oHgidcljL-FvzYSOWp_G10w8vY2BQ-EmkRsqFiotzDmyN3ljnb9KI-mMzEQfg5fPcC_AXbmlWRHd_DkrX_7NBTeu6kqH8wQtICU1yHYy5OIQoAuQtkbEX5CP_LqVoOUtBfvTWreC_vUlMT7M4JeflbxK9bjOEJlvOD4YFJX5TCQAKtpuEhuBWbjgucN4ZeGtg6a3ojIfNBW0WbunrNKa9lKCXD_0S3PGSiVh6uJXkDs3qBJQsm4hLrz7OxENFef9VW6nmGxc5SEe2cQr3fzywX5sJFqwhzpmg6P-KRLI3kdbKaYv6Hh3BCn2qoOg9lLeP46kqXff0s_4wBAX88fjMHhCajxRr7aFjfC3lyHFCPyrNaZ_64QkZX83QgcPA4cb0jNjKfa79aZ_nUkjZgTYmR23ikbq-f8WywFrLn4O-KFriV9m1kZWsfZRLqdDoqratu_JA4T1932V_d8iLRz60bAgiwmdB2mkZ0LzyjAeyFCejciaFychw9m2I5G7OnLFsk9vHE9bcP02vNRGSEbV3mBXvM6nQ4YdvqDBOiG9AodwYv1rRUCxTF0YFm-wQFkG1HdCXG6GP3dGBUXnhKmxXwXOU1o"
}
```

Token 转账：

```json
{
  "id": "LL9YZ8_rEX0v6SATMjKPldswKqQrOWRk18qMvLFDelY",
  "owner": "sYuEqnbVtW2mWE953q_NLqCg-hHT0TmU5GIwAkAzoa-G69y9rcaUtrB9yL0mBY5BCrwqDNL4-ZzgtRt-GS08QK-ndh_xdq9fy8_kqen58OV8gWPWoS-UD-2dkZk8qWpUUN8M7MsJGBiWAGwVlzMlDXdE4EQ7hs29XGDAzK6Mg0izYfqUxSg0ZffCiZC0QLICHitZxOv8I9z4Oil1KWApOcrNdExelZlOlqa9nTEbkGrxRMIedxLEzSF1PCrrh_HShfRPbbHeJ_4EDozbwgDcFsXt8MKODM87sZbPyJ5hrpjlCo2dVByc58eijE7kGgtTkvhKroY6wWNxwnCBNogJ0Je9NaPi1yxNd8-36BwBh_fW9z2Ivg0IJ6eoXip_F7_GFEdTE620kSA4mmRYGuwCd3cYHbFnU6MgXmCnNIE5DvYM2OYcCRpG-YikVQsgURe8xCRkeM0q2HLP8dJcc7drQHOIKhVx76Z1zt0imA0qHcvBIjQ5cpK0iB_8MmLhZqepTgSKCE8MgkDZgidAkBFeWGJQxkNYM9Ac0CES1oa91FWoA-cVuG6VhsNI0A17IcNPB4kQD02QXeqzyYCP0nQXLfdls9UntTFRjy2DF4FxMbucqien_-Z-egETrB9dRBWM6FtZIjoyjwF7arl4b1h3xyLSwpiQtJ_BWPjFeAY3hYc",
  // name 和 value 都是 base64 编码格式
  "tags": [
    {
      "name": "QXBwLU5hbWU", // App-Name
      "value": "U21hcnRXZWF2ZUFjdGlvbg"
    },
    {
      "name": "QXBwLVZlcnNpb24", // App-Version
      "value": "MC4zLjA"
    },
    {
      "name": "Q29udHJhY3Q", // Contract 一般存放合约Id，即创建该合约的交易 id
      "value": "RHlKOUZyQ0pLX3luYkdoMVE2bFIzdllOQ25MMGhrMmFBZy16ZV9mOVNGdw"
    },
    {
      "name": "SW5wdXQ", // Input 表示 Token 交易信息
      "value": "eyJmdW5jdGlvbiI6InRyYW5zZmVyIiwidGFyZ2V0IjoiRGZrRlZOcUhYbjVJSlJyS0xQVXF0bnBwNjFnYjIzajMtd0YwN2QzbjJiOCIsInF0eSI6NzUwMDB9" // {"function":"transfer","target":"DfkFVNqHXn5IJRrKLPUqtnpp61gb23j3-wF07d3n2b8","qty":75000}
    }
  ],
  "target": "",
  "quantity": "0",
  "reward": "5903596",
  "signature": "YkPjd45vY7z3R1lfKpMnxgHpbNpyorLOC2XQCq7qNKskpKaz2Dd5JlUOWrCddye4bHG-dHsu2X_q7Gs6_YJbKA352biGpBKoBAKH4YbgHaIsT_ed3fXXte1hLa6KunX5rCiBckjxSLRFmQ5cSXgBGCEuBtQd4PfJFkTxn2wV9HTHTt8Tq9jGKglIUim_W0ubIcyLZpi7B6Z3ZsFTTq3nxgTapNQclrAYMXmQeWQHwrFLslgW3h2LvISGhSdTKPMNTZHKIK7IiWfbeuRaER26Eob1o1Lx0hopoQtiqP7ATzMoGwkpgMQV5fOb7HqRhB3M8bn6uilgozUeLsYxqAZk4wapOusf05F4_1VStdI5eVnyrLAqKL3NzcAyr9Xk-EfE6hUllj56EjIvONi7rWvS_pesZdGJTDLFzpHecMXi5AJgXyx3u0tSEKlvVmhuo66TK5wnrR99Ng6FDxbemQAzTjsw9kQr04Nw8Mk_QluH8uAy11-B2607SWS_RFYBMwTKHJuN4PntCkz_uwKXDJkkg-9bee5mQ9GhBZC1xZn8gS3gc_SUGiXzPsz-mePqwAfzHySkZGu9LUSpoVdhLNZa5KjySNYDa2g_ALNuy_VXXr7GUMt5SfzxGvOtML7yz3SE1pinExVR_LrAvccXZ7hWIKYx0ivHG7cr66uIse2UHFM",
  "last_tx": "tcs60tg99cjdmae8oz5fhx6prhpy4xndfyuehi6cxhum3m73hr6cgh6og4zgxcif",
  "data_size": "90",
  "content_type": "",
  "format": 2,
  "height": 6,
  "owner_address": "Dt0DgaUbHOfJl4cyEUees2JEL1Wp6StA51lhQbe8lQ8",
  "data_root": "3XRWdLXrIcXAG7U9SrcI5cA8m-WV1moRYn7CJtlpa-E",
  "parent": null,
  "block": "u7bwzutlyaixvrsxvkxlenv62iqbe5sm3nt0lbxcdr59lnqolcltm3cbs31pzk7q",
  "bundledIn": "",
  "created_at": "2025-07-13T07:51:45.546Z",
  "data": ""
}
```

参考：https://docs.arweave.org/developers/arweave-node-server/http-api#transaction-format

### 获取交易手续费

- bytes: 交易数据字段的字节数。 如果向另一个钱包发送 AR 且未附加数据，则应使用 0。
- target: 目标钱包地址

```bash
# GET https://arweave.net/price/{bytes}/{target}
curl --location 'https://arweave.net/price/0/VFXltFxA6skwF3X3Z2m-TeNZHBF3r6x9jEUhlqLFqE0'
```

```text
6727794
```

### 发送交易

POST https://arweave.net/tx

请求体和交易信息一样

参考：https://docs.arweave.org/developers/arweave-node-server/http-api#submit-a-transaction

## Arweave 生态资源

### 核心项目

| 项目名称   | 描述                                               | 链接                                                                                |
| ---------- | -------------------------------------------------- | ----------------------------------------------------------------------------------- |
| Arweave    | 公链                                               | [官网](https://www.arweave.org/) / [GitHub](https://github.com/ArweaveTeam/arweave) |
| arlocal    | 启动本地 arlocal 节点，支持 node.js 和 docker 启动 | [GitHub](https://github.com/textury/arlocal)                                        |
| SmartWeave | 基于 Arweave 协议的简单、可扩展的智能合约          | [GitHub](https://github.com/ArweaveTeam/SmartWeave)                                 |
| Warp       | Arweave SmartWeave 智能合约协议的实现              | [官网](https://warp.cc/) / [GitHub](https://github.com/warp-contracts/warp)         |
| arweave-js | Arweave JavaScript/TypeScript SDK                  | [GitHub](https://github.com/ArweaveTeam/arweave-js)                                 |

### 文档资源

| 资源名称         | 描述                    | 链接                                                                     |
| ---------------- | ----------------------- | ------------------------------------------------------------------------ |
| 开发者文档       | Arweave 官方开发者文档  | [链接](https://docs.arweave.org/developers)                              |
| 白皮书           | Arweave 白皮书          | [PDF](https://www.arweave.org/files/arweave-lightpaper.pdf)              |
| 黄皮书           | Arweave 技术黄皮书      | [PDF](https://www.arweave.org/yellow-paper.pdf)                          |
| HTTP API 文档    | Arweave 节点服务器 API  | [链接](https://docs.arweave.org/developers/arweave-node-server/http-api) |
| SmartWeave 文档  | SmartWeave 合约开发文档 | [GitHub](https://github.com/ArweaveTeam/SmartWeave)                      |
| Arweave Cookbook | 开发者指南和示例集合    | [链接](https://cookbook.arweave.dev)                                     |

### 工具和应用

| 工具名称  | 描述                   | 链接                              |
| --------- | ---------------------- | --------------------------------- |
| ArConnect | Arweave 浏览器钱包扩展 | [官网](https://www.arconnect.io/) |
| ViewBlock | Arweave 区块链浏览器   | [链接](https://viewblock.io/)     |
