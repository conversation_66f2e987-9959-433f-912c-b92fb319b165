import { transactions } from 'near-api-js';

import * as nearAPI from 'near-api-js';
import { derivePath, getPublicKey } from 'ed25519-hd-key';

const { keyStores, utils } = nearAPI;

// 签名方法
export async function signTransactionsMzy (params) {
  const keyStore = new keyStores.InMemoryKeyStore();
  const keyPair = utils.KeyPair.fromString(params.privateKey);
  await keyStore.setKey(params.networkId, params.signerId, keyPair);
  const signer = new nearAPI.InMemorySigner(keyStore);

  const publicKey = await signer.getPublicKey(
    params.signerId,
    params.networkId
  );

  // @ts-ignore
  const actions = [transactions.transfer(utils.format.parseNearAmount(params.amount))];

  const transaction = nearAPI.transactions.createTransaction(
    params.signerId,
    nearAPI.utils.PublicKey.from(publicKey.toString()),
    params.receiverId,
    params.nonce + 1, // nonce + 1
    actions,
    nearAPI.utils.serialize.base_decode(params.blockHash)
  );

  const response = await nearAPI.transactions.signTransaction(
    transaction,
    signer,
    transaction.signerId,
    params.networkId
  );

  // 返回序列化的交易签名 (Base64 编码)
  return Buffer.from(response[1].encode()).toString('base64');
}

export async function createNearAddress (seedHex: string, addressIndex: number, network: string) {
  const { key } = derivePath("m/44'/501'/1'/" + addressIndex + "'", seedHex);
  const publicKey = getPublicKey(<Buffer> new Uint8Array(key), false).toString('hex');
  const hdWallet = {
    privateKey: key.toString('hex') + publicKey,
    publicKey,
    address: publicKey
  };
  return JSON.stringify(hdWallet);
}

/**
 /**
 * 验证 NEAR 地址是否有效
 * @param params 包含地址和网络ID的参数对象
 * @returns 验证结果对象
 */
export function verifyNearAddress (params: {
  address: string;
  networkId?: 'mainnet' | 'testnet';
}): { isValid: boolean; message?: string } {
  const { address, networkId = 'mainnet' } = params;

  // 基本格式验证：长度和字符限制
  if (typeof address !== 'string') {
    return { isValid: false, message: '地址必须是字符串' };
  }

  // 正则表达式验证：
  // - 主网地址：64位十六进制哈希 或 长度为2-64的小写字母数字+连字符组合，且不能以连字符开头或结尾
  // - 测试网地址：与主网相同，允许.testnet后缀
  const mainnetRegex = /^(?:[0-9a-f]{64}|(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)(?:\.near)?)$/;
  const testnetRegex = /^(?:[0-9a-f]{64}|(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)(?:\.testnet)?)$/;

  const regex = networkId === 'testnet' ? testnetRegex : mainnetRegex;
  if (!regex.test(address)) {
    return {
      isValid: false,
      message: `无效的NEAR ${networkId}地址格式`
    };
  }

  // 合约地址（64位哈希）无需进一步验证
  if (address.length === 64 && /^[0-9a-f]+$/.test(address)) {
    return { isValid: true };
  }

  // 子账户验证（包含点的地址）
  if (address.includes('.')) {
    const parts = address.split('.');

    // 主网地址不能包含.testnet后缀
    if (networkId === 'mainnet' && parts[parts.length - 1] === 'testnet') {
      return { isValid: false, message: '主网地址不能使用.testnet后缀' };
    }

    // 测试网地址必须以.testnet结尾
    if (networkId === 'testnet' && parts[parts.length - 1] !== 'testnet') {
      // 允许主网格式的地址在测试网使用（例如合约地址）
      if (!mainnetRegex.test(address)) {
        return { isValid: false, message: '测试网地址必须以.testnet结尾' };
      }
    }

    // 验证每个部分（不包括顶级域名）
    const partsToVerify = networkId === 'testnet'
      ? parts.slice(0, -1) // 测试网排除.testnet部分
      : parts; // 主网验证所有部分

    for (const part of partsToVerify) {
      if (!mainnetRegex.test(part)) {
        return { isValid: false, message: '子账户部分格式无效' };
      }
    }
  } else {
    // 单部分地址（非子账户）必须符合主网格式（不允许.testnet）
    if (networkId === 'mainnet' && address.endsWith('.near')) {
      const baseName = address.slice(0, -5); // 移除.near后缀
      if (!mainnetRegex.test(baseName)) {
        return { isValid: false, message: '主网账户名格式无效' };
      }
    }
  }

  return { isValid: true };
}
