import {
    mnemonicToSeed
} from "../wallet/bip/bip";

import {
    signTransactionsMzy,
    createNearAddress, verifyNearAddress,
} from "../wallet/nearMzy";


describe('near unit test case', () => {
    test('createAddress', () => {
        const mnemonic = "hold hybrid write stamp badge evolve ceiling glow wire siren fun test"; //modified
        const params_1 = {
            mnemonic: mnemonic,
            password: "1111"
        }
        const seed = mnemonicToSeed(params_1)
        const account = createNearAddress(seed.toString("hex"), 0, "mainnet")
        console.log(account)
    });

// test passed
    test('signTransaction', async () => {
        try {
            const signedTx = await signTransactionsMzy({
                privateKey: 'ed25519:5vefYCaxG...', //modified
                signerId: 'dylanma.near',
                receiverId: 'annie.near',
                amount: '1',
                nonce: ***************,
                blockHash: 'HHaDkupofjS3e9iBYreLGtzrY1SXoeM1owroKmMptMRd',
                networkId: 'mainnet'
                //NetworkId = "mainnet" | "testnet";
            });

            console.log('签名后的交易 (Base64):', signedTx);
            console.log('可将此字符串传输到联网设备进行广播');
        } catch (error) {
            console.error('签名过程出错:', error);
        }
    });
// test passed
    test('verifyAddress', async () => {
        // @ts-ignore
        console.log(verifyNearAddress({
            address: 'dylanma.near',
            networkId: 'mainnet'
        }))
    });
});