import {generateMnemonic, mnemonicToSeed} from "../wallet/bip/bip";
import {createAddress, numberToHex} from "../wallet";
import {ethers} from "ethers";
import Common from "@ethereumjs/common";
import {FeeMarketEIP1559Transaction, Transaction} from "@ethereumjs/tx";
import {Interface} from "@ethersproject/abi";
const BigNumber = require('bignumber.js');

const eth_provider = new ethers.providers.JsonRpcProvider("https://ethereum-sepolia-rpc.publicnode.com");
const optimism_provider = new ethers.providers.JsonRpcProvider("https://sepolia.optimism.io");


// 从 ETH Sepolia → Optimism Sepolia 跨链充值
// 需要的：ETH Sepolia ChainID：11155111
// 桥合约地址（部署在 ETH Sepolia 上的 Optimism Portal 合约）
// 注意！跨链充值时，交易是发给 ETH Sepolia 上的 OptimismPortal 合约，不是 L2 Optimism 合约。
// ETH Sepolia 上的 OptimismPortal 地址
const L1Bridge =  "******************************************"
// Optimism Sepolia上的L1StandardBridge
const L2Bridge ="******************************************"

const eth_sepolia_chainId = 11155111
const optimism_sepolia_chainId = 11155420

// 你别搞！！！
const privateKey = 'b3fbdd286b3f5ece5bcbf37e3ff98ca450b38a2c386718a03becda96471827b3'
const address = '******************************************'

describe("home works", () => {

    test("create address", () => {
        const mnemonic = generateMnemonic({number: 12, language: "english"});
        const params = {
            mnemonic: mnemonic,
            password: ""
        }
        const seed = mnemonicToSeed(params);
        const address = createAddress(seed.toString("hex"), "0");
        console.log("address", address);
        // address {"privateKey":"0xec4d954277112bba9e8fe5a7b46d6761d976c681565ef47d506efb501bd706e5",
        // "publicKey":"0x0373d3b44a1392e466ffe4174c27cd7dee165aba4b5a79b81c1f0b52b9f30ff1f9",
        // "address":"******************************************"}
    })

    test("ETH -> ETH", async () => {
        const from = "******************************************"
        const to = "******************************************"
        const nonce = await eth_provider.getTransactionCount(from)
        const rawHex = signTransaction({
            privateKey: privateKey,
            nonce: nonce,
            from: from,
            to: to,
            gasLimit: 91000,
            maxFeePerGas: **********,
            maxPriorityFeePerGas: 100000000,
            gasPrice: 0,
            amount: '0.01',
            decimal: 18,
            chainId: 11155111,
            tokenAddress: "******************************************",
            callData: ""
        })
        const tx = await eth_provider.sendTransaction(rawHex);
        console.log("tx.await()");
        await tx.wait();
        console.log("tx", tx);
    })

    test("ETH -> Optimism", async () => {
        // 根据私钥推算出钱包、公钥
        const wallet = new ethers.Wallet(privateKey)
        // 跨链调用合约，构建 callData
        // 使用depositETHTo可指定充值到的地址，使用depositETH方法默认充值到原地址的 op Sepolia 链上
        const bridgeInterface = new ethers.utils.Interface([
            "function depositETHTo(address _to, uint32 _minGasLimit, bytes calldata _extraData) external payable"
        ])
        const calldata = bridgeInterface.encodeFunctionData("depositETHTo", [address, "21000", "0x"])
        const amount = "0.01"
        const decimal = 18;
        const nonce = await getNonce(address);
        console.log("nonce: ", nonce);
        // 因为 eth 精度是 18，所以转为 18位
        const valueInWei = BigNumber(amount).times("1e18");
        // 获取当前网络最新的 gas 价格数据
        // 返回的 feeData 通常包括：
        // gasPrice（Legacy 旧版 gas price）
        // maxFeePerGas（EIP-1559 最大可接受 gas）
        // maxPriorityFeePerGas（EIP-1559 小费）
        // 👉 用来后续设置交易的 gas 相关参数。
        const feeData = await eth_provider.getFeeData();
        // 估算这笔交易需要的 Gas
        const estimate = await eth_provider.estimateGas({
            from: address,
            to: L1Bridge,
            value: valueInWei.toFixed(0),
            data: calldata
        });
        const signed = signTransaction({
            privateKey,
            nonce,
            from: address,
            to: L1Bridge,
            gasLimit: estimate.toNumber(),
            amount,
            gasPrice: 0,
            decimal,
            chainId: eth_sepolia_chainId,
            tokenAddress: "0x00",
            callData: calldata,
            maxPriorityFeePerGas: parseInt(feeData.maxPriorityFeePerGas!.toString()),
            maxFeePerGas: parseInt(feeData.maxFeePerGas!.toString())
        })

        console.log("📤 signed:", signed);
        console.log("📤 Broadcasting tx...");
        const tx = await eth_provider.sendTransaction(signed);
        console.log("📨 Sent! Tx Hash:", tx.hash);

        const receipt = await tx.wait();
        console.log("✅ Transaction confirmed");
        console.log(receipt.status === 1 ? "✅ 成功" : "❌ 执行失败");
        console.log(receipt);
    })

    test("Optimism -> ETH", async () => {
        // 跨链调用合约，构建 callData
        const bridgeInterface = new ethers.utils.Interface(["function withdraw(address _l2Token, uint256 _amount, uint32 _l1Gas, bytes _extraData) external payable"])
        const amount = ethers.utils.parseEther("0.01")
        const calldata = bridgeInterface.encodeFunctionData("withdraw", ["******************************************", amount, 100000 , "0x"])
        const decimal = 18;
        const nonce = await optimism_provider.getTransactionCount(address);
        console.log("nonce: ", nonce);
        const signed = signTransaction({
            privateKey,
            nonce: nonce,
            from: address,
            to: L2Bridge,
            gasLimit: 2000000,
            amount: "0.01",
            gasPrice: 0,
            decimal,
            chainId: optimism_sepolia_chainId,
            tokenAddress: "0x00",
            callData: calldata,
            maxFeePerGas: **********,
            maxPriorityFeePerGas: 100000000
        })

        console.log("📤 signed:", signed);
        console.log("📤 Broadcasting tx...");
        const tx = await optimism_provider.sendTransaction(signed);
        console.log("📨 Sent! Tx Hash:", tx.hash);

        const receipt = await tx.wait();
        console.log("✅ Transaction confirmed");
        console.log(receipt.status === 1 ? "✅ 成功" : "❌ 执行失败");
        console.log(receipt);
    })


    async function getNonce(address: string): Promise<number> {
        const data = await eth_provider.getTransactionCount(address, "latest")
        return data;
    }

    function signTransaction(params: {
        privateKey: string;
        nonce: number;
        from: string;
        to: string;
        gasLimit: number;
        amount: string;
        gasPrice: number;
        decimal: number;
        chainId: number;
        tokenAddress: string;
        callData: string;
        maxPriorityFeePerGas?: number;
        maxFeePerGas?: number;
    }) {
        let { privateKey, nonce, from, to, gasPrice, gasLimit, amount, tokenAddress, callData, decimal, maxPriorityFeePerGas, maxFeePerGas, chainId } = params;
        const transactionNonce = numberToHex(nonce);
        const gasLimits = numberToHex(gasLimit);
        const chainIdHex = numberToHex(chainId);
        let newAmount = BigNumber(amount).times(BigNumber(10).pow(decimal));
        const numBalanceHex = numberToHex(newAmount);
        let txData: any = {
            nonce: transactionNonce,
            gasLimit: gasLimits,
            to,
            from,
            chainId: chainIdHex,
            value: numBalanceHex,
            data: callData
        };

        // 以太坊自 London 升级（2021） 起，启用了 EIP-1559，改进了 gas 定价机制
        // 交易有两种 gas 定价方式：
        // EIP-1559 模式： maxFeePerGas maxPriorityFeePerGas
        // 旧版 Legacy 模式：gasPrice
        // 不同链、不同 RPC、不同兼容性，可能只支持其中一种。
        // 签名交易时必须保证 Gas 参数匹配，否则交易无法广播。
        if (maxFeePerGas && maxPriorityFeePerGas) {
            // 把最大可接受的 gas 总费用（maxFeePerGas）和给矿工的小费（maxPriorityFeePerGas）
            txData.maxFeePerGas = numberToHex(maxFeePerGas);
            txData.maxPriorityFeePerGas = numberToHex(maxPriorityFeePerGas);
        } else {
            txData.gasPrice = numberToHex(gasPrice);
        }

        let common: any, tx: any;
        if (txData.maxFeePerGas && txData.maxPriorityFeePerGas) {
            common = Common.custom({ chainId, defaultHardfork: "london" });
            tx = FeeMarketEIP1559Transaction.fromTxData(txData, { common });
        } else {
            common = Common.custom({ chainId });
            tx = Transaction.fromTxData(txData, { common });
        }
        const privateKeyBuffer = Buffer.from(privateKey, "hex");
        const signedTx = tx.sign(privateKeyBuffer);
        const serializedTx = signedTx.serialize();
        if (!serializedTx) throw new Error("serializedTx is empty");
        return `0x${serializedTx.toString("hex")}`;
    }

})