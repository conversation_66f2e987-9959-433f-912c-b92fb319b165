* Optimism -> ETH: 
  * 0x874fe13877d7c657c8f7fb61bee8a5332e6fe05a455139d2d3b4eeafa1b37bd4
  * https://sepolia-optimism.etherscan.io/tx/0x874fe13877d7c657c8f7fb61bee8a5332e6fe05a455139d2d3b4eeafa1b37bd4
* ETH -> Optimism: 
  * 0x76b5292a3ae97408c392542c4fb9fd406a43c5d28eb3a27989ae5ca34ac3b711
  * https://sepolia.etherscan.io/tx/0x76b5292a3ae97408c392542c4fb9fd406a43c5d28eb3a27989ae5ca34ac3b711
* Optimism 各合约地址 https://docs.optimism.io/superchain/addresses
* 接口文档 https://www.quicknode.com/docs/ethereum/eth_blockNumber
## 扫链RPC
* 获取区块最新块
  * request
    ``` 
    curl --location 'https://ethereum-sepolia-rpc.publicnode.com' \
    --header 'Content-Type: application/json' \
    --data '{
    "jsonrpc":"2.0",
    "method":"eth_blockNumber",
    "params":[],
    "id":1
    }'
  * response
    ```
    {"jsonrpc":"2.0","result":"0x85d250","id":1}

* 根据区块 number 获取详情
  * request
    ``` 
    curl --location 'https://ethereum-sepolia-rpc.publicnode.com' \
    --header 'Content-Type: application/json' \
    --data '{
    "jsonrpc":"2.0",
    "method":"eth_getBlockByNumber",
    "params":[
    "0x1b4",
    true
    ],
    "id":1
    }'
  * response
    ```
    {
    "jsonrpc": "2.0",
    "id": 1,
    "result": {
        "baseFeePerGas": "0x7",
        "difficulty": "0x277e1",
        "extraData": "0x",
        "gasLimit": "0x1c9c380",
        "gasUsed": "0x0",
        "hash": "0x4069699f4304ee58adc892661fe1e6667962da5326a57d6c0179d763a775a0d6",
        "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000",
        "miner": "******************************************",
        "mixHash": "0x2b1f8a2b947a83f6836a4f99ec4dbb1e4b22eda3a79cb40f85906c1334f64b45",
        "nonce": "0xc7faaf72b4588dfb",
        "number": "0x1b4",
        "parentHash": "0xf456e77f27fb99fadae70182138a3687d8482d64e9677b2c9f934d9379c018b1",
        "receiptsRoot": "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421",
        "sha3Uncles": "0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347",
        "size": "0x203",
        "stateRoot": "0x6fbabd9ad6fe39d48abca3dc592edcca0ca29bc85c91daecdad6db84048cec92",
        "timestamp": "0x61736246",
        "transactions": [],
        "transactionsRoot": "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421",
        "uncles": []
    }
}

* 根据 hash 获取详情
  * request
    ``` 
    curl --location 'https://ethereum-sepolia-rpc.publicnode.com' \
    --header 'Content-Type: application/json' \
    --data '{
    "jsonrpc":"2.0",
    "method":"eth_getBlockByHash",
    "params":[
    "0x4069699f4304ee58adc892661fe1e6667962da5326a57d6c0179d763a775a0d6",
    true
    ],
    "id":1
    }'
  * response
    ```
    同上

## 挑战期
``` 
    packages/contracts-bedrock/deploy-config/mainnet.json
    finalizationPeriodSeconds": 604800
    
