// transfer_flow.cdc
// 这些是 FungibleToken 和 FlowToken 的标准模拟器地址。
// 重要提示：对于测试网或主网，你必须将其替换为正确的地址！

//import FlowToken from 0x1654653399040a61  主网
//import EVM from 0xe467b9dd11fa00df
//import FungibleToken from 0xf233dcee88fe0abe

// import FungibleToken from 0x9a0766d93b6608b7 测试网
// import FlowToken from 0x7e60df042a9c0868
// Cadence 转账交易代码（测试网合约地址）

//9f778ff7698c4a625dfb7e48ec1a64349842f67b2f1ce9f8f2eabe82c4e4abe2 测试网发送者
//9e80f85475ded77051f7749641ef92d52fa468a68cb8279aec0168a33550c6de709199d79cb6cae9ca7abd87d066d1683b0678c9694d2573db83dc9c7af3ed57
//0xf24aebd595d74581

//84cb6ad7e780fbdaa8ba967dffdb1ec68e1b17320c97fa3d462a536d4957866e 测试网接收者
//1deec9b27633fe87ab1647432eefd2a3ed4bb5f2e477d7b7e9a6c9d7e86fb91d454a3de44a358546aeb6b2ab13d87bc96469c9f8b5ef747909fdb79fa6791931
//0xa72f4ff0bd9ca998
import FungibleToken from 0x9a0766d93b6608b7 
import FlowToken from 0x1654653399040a61
     
import EVM from 0x8c5303eaa26202d6
transaction(amount: UFix64,to: String) {
    let sentVault: @FlowToken.Vault
    let evmRecipient: EVM.EVMAddress?
    var receiver: &{FungibleToken.Receiver}?

    prepare(signer: auth(BorrowValue, SaveValue) &Account) {
        // Reference signer's FlowToken Vault
        let sourceVault = signer.storage.borrow<auth(FungibleToken.Withdraw) &FlowToken.Vault>(from: /storage/flowTokenVault)
            ?? panic("Could not borrow signer's FlowToken.Vault")

        // Init receiver as nil
        self.receiver = nil
        // Ensure address is prefixed with '0x'
        let withPrefix = to.slice(from: 0, upTo: 2) == "0x" ? to : "0x".concat(to)
        // Attempt to parse address as Cadence or EVM address
        let cadenceRecipient = withPrefix.length < 40 ? Address.fromString(withPrefix) : nil
        self.evmRecipient = cadenceRecipient == nil ? EVM.addressFromString(withPrefix) : nil

        // Validate exactly one target address is assigned
        if cadenceRecipient != nil && self.evmRecipient != nil {
            panic("Malformed recipient address - assignable as both Cadence and EVM addresses")
        } else if cadenceRecipient == nil && self.evmRecipient == nil {
            panic("Malformed recipient address - not assignable as either Cadence or EVM address")
        }

        if cadenceRecipient != nil {
            // Assign FungibleToken Receiver if recipient is a Cadence address
            self.receiver = getAccount(cadenceRecipient!).capabilities.borrow<&{FungibleToken.Receiver}>(/public/flowTokenReceiver)
                ?? panic("Could not borrow FungibleToken Receiver from recipient")
        }
        // Create empty FLOW vault to capture funds
        self.sentVault <- sourceVault.withdraw(amount: amount) as! @FlowToken.Vault
    }

    pre {
        self.receiver != nil || self.evmRecipient != nil: "Could not assign a recipient for the transfer"
        self.sentVault.balance == amount: "Attempting to send an incorrect amount of $FLOW"
    }

    execute {
        // Complete Cadence transfer if the FungibleToken Receiver is assigned
        if self.receiver != nil {
            self.receiver!.deposit(from: <-self.sentVault)
        } else {
            // Otherwise, complete EVM transfer
            self.evmRecipient!.deposit(from: <-self.sentVault)
        }
    }
}