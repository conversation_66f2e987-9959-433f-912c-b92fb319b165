const fcl = require("@onflow/fcl");
import crypto from 'crypto';
const EC = require("elliptic").ec;
const ec = new EC("secp256k1"); // 使用 secp256k1 曲线

// 配置 FCL
fcl.config().put('accessNode.api', 'https://rest-testnet.onflow.org'); // Testnet 节点
// fcl.config().put('accessNode.api', 'https://rest-mainnet.onflow.org'); // mainnet 节点

// 账户信息
const senderAddress = '0xf24aebd595d74581'; // 替换为你的测试网地址
const senderPrivateKey = '9f778ff7698c4a625dfb7e48ec1a64349842f67b2f1ce9f8f2eabe82c4e4abe2'; // 替换为你的私钥（64 位十六进制）
const receiverAddress = '0xa72f4ff0bd9ca998'; // 替换为接收者地址
const amount = '10.0'; // 转账金额（FLOW）
const senderKeyId = 0; // 替换为实际的 keyId

const transferCadence = `
import FungibleToken from 0x9a0766d93b6608b7
import FlowToken from 0x7e60df042a9c0868

transaction(amount: UFix64, to: Address) {
    let sentVault: @{FungibleToken.Vault}
    prepare(signer: auth(Storage) &Account) {
        let vaultRef = signer.storage.borrow< auth(FungibleToken.Withdraw) &FlowToken.Vault>(from: /storage/flowTokenVault)
         ?? panic("Could not borrow signer's FlowToken.Vault!")
        self.sentVault <- vaultRef.withdraw(amount: amount)
    }
    execute {
        let receiverRef = getAccount(to)
            .capabilities.get<&{FungibleToken.Vault}>(/public/flowTokenReceiver)
            .borrow()
            ?? panic("Could not borrow receiver reference!")
        receiverRef.deposit(from: <-self.sentVault)
    }
}`;

// 生成签名
function signWithKey(privateKey, message) {
    try {
        // 验证私钥格式
        if (!/^[0-9a-fA-F]{64}$/.test(privateKey)) {
            throw new Error("私钥格式错误：必须是 64 位十六进制字符串，无 0x 前缀");
        }
        const key = ec.keyFromPrivate(Buffer.from(privateKey, "hex"));
        const sig = key.sign(hashMessage(message));
        const n = 32;
        const r = sig.r.toArrayLike(Buffer, "be", n);
        const s = sig.s.toArrayLike(Buffer, "be", n);
        return Buffer.concat([r, s]).toString("hex");
    } catch (error) {
        // @ts-ignore
        throw new Error(`签名生成失败: ${error.message}`);
    }
}

// 哈希消息（使用 SHA2_256）
function hashMessage(message) {
    try {
        if (!/^[0-9a-fA-F]+$/.test(message)) {
            throw new Error("签名消息格式错误：必须是有效的十六进制字符串");
        }
        const hash = crypto.createHash("sha256");
        hash.update(Buffer.from(message, "hex"));
        return hash.digest();
    } catch (error) {
        // @ts-ignore
        throw new Error(`SHA2_256 哈希失败: ${error.message}`);
    }
}

// 获取账户密钥信息（包括序列号和公钥）
async function getAccountKeyInfo(address, keyId) {
    try {
        const account = await fcl.account(address);
        const key = account.keys[keyId];
        if (!key) {
            throw new Error(`密钥索引 ${keyId} 在账户 ${address} 上不存在`);
        }
        if (key.revoked) {
            throw new Error(`密钥索引 ${keyId} 已被撤销`);
        }
        if (key.weight < 1000) {
            throw new Error(`密钥索引 ${keyId} 的权重 ${key.weight} 不足 1000`);
        }
        if (key.signAlgo !== 2) { // 2 表示 ECDSA_secp256k1
            throw new Error(`密钥索引 ${keyId} 的签名算法不是 ECDSA_secp256k1 (signAlgo: ${key.signAlgo})`);
        }
        if (key.hashAlgo !== 1) { // 1 表示 SHA2_256
            throw new Error(`密钥索引 ${keyId} 的哈希算法不是 SHA2_256 (hashAlgo: ${key.hashAlgo})`);
        }
        return {
            sequenceNumber: key.sequenceNumber,
            publicKey: key.publicKey,
        };
    } catch (error) {
        // @ts-ignore
        throw new Error(`获取账户密钥信息失败: ${error.message}`);
    }
}

// 自定义授权函数
async function authorizationFunction(account) {
    const keyInfo = await getAccountKeyInfo(senderAddress, senderKeyId);
    return {
        ...account,
        addr: senderAddress,
        keyId: senderKeyId,
        sequenceNumber: keyInfo.sequenceNumber, // 动态序列号
        signingFunction: async (signable) => {
            console.log("签名消息:", signable.message);
            return {
                addr: senderAddress,
                keyId: senderKeyId,
                signature: signWithKey(senderPrivateKey, signable.message),
            };
        },
    };
}

// 构造并提交交易
export async function sendSignedTransaction() {
    try {
        // 1.获取账户密钥信息
        const keyInfo = await getAccountKeyInfo(senderAddress, senderKeyId);
        console.log("账户密钥信息:", keyInfo);

        // 2.构造交易
        const txResponse = await fcl.send([
            fcl.transaction(transferCadence),
            fcl.args([
                fcl.arg(amount, fcl.t.UFix64),
                fcl.arg(receiverAddress, fcl.t.Address),
            ]),
            fcl.payer(authorizationFunction),
            fcl.proposer(authorizationFunction),
            fcl.authorizations([authorizationFunction]),
            fcl.limit(9999)
        ]);

        // 3.检查交易响应
        console.log("交易响应:", JSON.stringify(txResponse, null, 2));

        // 4.验证transactionId
        if (!txResponse.transactionId || typeof txResponse.transactionId !== "string") {
            throw new Error("无效的 transactionId: " + txResponse.transactionId);
        }

        // 5.等待交易确认
        const txResult = await fcl.tx(txResponse.transactionId).onceSealed();
        console.log("区块 ID:", txResult.blockId);
        console.log("交易状态:", txResult.status);
        if (txResult.status === 4) {
            console.log("交易成功！");
        } else {
            console.error("交易失败:", txResult.errorMessage);
        }
    } catch (error) {
        console.error("错误:", error);
    }
}

// 执行交易
sendSignedTransaction();