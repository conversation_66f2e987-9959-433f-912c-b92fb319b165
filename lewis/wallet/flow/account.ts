import * as fcl from "@onflow/fcl";

fcl.config({
    "accessNode.api": "https://rest-testnet.onflow.org" // 或者你的 devnet52 节点地址
});

async function getAccountKeyInfo(address) {
    const script = `
    // 关键更改在这里：将 pub fun main 改为 pub(everywhere) fun main
    pub(everywhere) fun main(addr: Address): {UInt8: {String: String}} {
        let account = getAccount(addr)
        if account == nil {
            return {}
        }
        let keys = account.keys
        for key in keys.getPublicKeys() {
            let keyIndex = key.keyIndex
            let publicKeyHex = key.publicKey.publicKey.toString()
            result[keyIndex] = {
                "publicKey": publicKeyHex,
                "hashAlgo": key.hashAlgo.rawValue.toString(),
                "signAlgo": key.signAlgo.rawValue.toString(),
                "weight": key.weight.toString(),
                "revoked": key.isRevoked.toString()
            }
        }
        return result
    }
  `;

    const result = await fcl.query({
        cadence: script,
        args: (arg, t) => [
            arg(address, t.Address)
        ]
    });

    console.log(`查询地址 ${address} 的密钥信息:`, result);
    return result;
}

// 调用函数，替换为你要查询的实际地址
getAccountKeyInfo("9e80f85475ded77051f7749641ef92d52fa468a68cb8279aec0168a33550c6de709199d79cb6cae9ca7abd87d066d1683b0678c9694d2573db83dc9c7af3ed57"); // 替换为你的测试地址，例如 0x7e60df042f8c5c56