const fcl = require("@onflow/fcl");
const t = require("@onflow/types");
const fs = require("fs");

// --- 配置 FCL ---
// 这里是用于发送“添加公钥”交易的账户。
// 这个账户必须是目标账户本身，并且其私钥（例如 Key ID 0 的私钥）必须有足够的权限来修改账户密钥。
// 在模拟器中，通常是 0x01 服务账户。
fcl.config({
    "accessNode.api": "https://rest-mainnet.onflow.org", // 连接到本地模拟器
    //"flow.network": "emulator",
    // 用于签署“添加公钥”交易的账户（通常是发起账户本身）
    "service.privateKey": "9f778ff7698c4a625dfb7e48ec1a64349842f67b2f1ce9f8f2eabe82c4e4abe2", // 替换为你的 signer 账户的私钥 (e.g., 0x01)
    "service.keyId": 1, // 替换为你的 signer 账户的 keyId
    "service.address": "0x4deb6e62768f70ac", // 替换为你的 signer 账户的地址 (e.g., 0x01)
});
//9f778ff7698c4a625dfb7e48ec1a64349842f67b2f1ce9f8f2eabe82c4e4abe2
//9e80f85475ded77051f7749641ef92d52fa468a68cb8279aec0168a33550c6de709199d79cb6cae9ca7abd87d066d1683b0678c9694d2573db83dc9c7af3ed57
//0x4deb6e62768f70ac

const addKeyCadence = `
transaction(publicKey: String, signatureAlgorithm: UInt8, hashAlgorithm: UInt8, weight: UFix64) {
    prepare(signer: AuthAccount) {
        let key = publicKey.decodeHex()
        let keyIndex = signer.addPublicKey(
            key: key,
            signatureAlgorithm: signatureAlgorithm, 
            hashAlgorithm: hashAlgorithm,         
            weight: weight                       
        )
        log("Successfully added new public key with Key ID: ".concat(keyIndex.toString()))
    }
}
`;

async function addKeyToFlowAccount() {
    console.log("--- 准备向账户添加新的公钥 ---");

    // 从步骤 2 获取的新密钥信息
    const NEW_PUBLIC_KEY = "61444e9e43675545133db2376b7476d68f20c9aa4e0df46ee04c98a3cd3cb8c3f8f4cb3f087190d14b2085fc78f0deb6a94c3ad9c908ca451e84fdae56b8430c"; // 替换为步骤 2 生成的公钥字符串 (不带 0x 前缀)
    const SIGNATURE_ALGORITHM = 0; // 0 for ECDSA_secp256k1, 2 for ECDSA_P256
    const HASH_ALGORITHM = 1;      // 1 for SHA3_256, 2 for SHA2_256
    const KEY_WEIGHT = 1000;       // 密钥权重 (0-1000), 1000 表示全权

    try {

        const transactionId = await fcl.mutate({
            cadence: addKeyCadence,
            args: (arg, t) => [
                arg(NEW_PUBLIC_KEY, t.String),
                arg(SIGNATURE_ALGORITHM, t.UInt8),
                arg(HASH_ALGORITHM, t.UInt8),
                arg(KEY_WEIGHT.toFixed(1), t.UFix64), // 权重通常是 UFix64 类型
            ],
            proposer: fcl.authz,
            payer: fcl.authz,
            authorizations: [fcl.authz], // 授权账户必须是将被添加公钥的账户本身
            limit: 999, // 适当的 Gas Limit
        });

        console.log(`添加公钥交易 ID: ${transactionId}`);

        const transactionStatus = await fcl.tx(transactionId).onceSealed();
        console.log("交易状态:", transactionStatus.status);
        console.log("交易状态明细:", transactionStatus.statusString);

        if (transactionStatus.status === 4) { // 4 means sealed (success)
            console.log("✅ 成功：新的公钥已添加到账户！");
            // 你可以通过解析交易事件或查询账户来获取新的 keyId
            // 账户信息可以在 Flowscan (测试网/主网) 或 flow-cli query account 找到
        } else {
            console.error("❌ 失败：添加公钥交易未成功。");
            console.error("错误信息:", transactionStatus.errorMessage);
        }

    } catch (error) {
        console.error("发生错误:", error);
    }
}

addKeyToFlowAccount();