const bip39 = require('bip39')
const hdkey = require("hdkey");
const EC = require("elliptic").ec;
const ec = new EC("secp256k1"); // 使用 secp256k1 曲线

// Function to generate public and private keys from a mnemonic
export function generateKeysFromMnemonic(mnemonic, derivationPath = "m/44'/539'/0'/0/0") {
    try {
        // Generate seed from mnemonic (BIP-39)
        const seed = bip39.mnemonicToSeedSync(mnemonic);

        // Derive HD wallet key from seed (BIP-32)
        const hdWallet = hdkey.fromMasterSeed(seed);
        const derivedKey = hdWallet.derive(derivationPath);

        // Get private key (32 bytes)
        const privateKey = derivedKey.privateKey;
        const privateKeyHex = privateKey.toString("hex");

        // Generate public key using secp256k1
        const keyPair = ec.keyFromPrivate(privateKey);
        const publicKey = keyPair.getPublic();
        // Get uncompressed public key (65 bytes, starts with 0x04) and remove 0x04 prefix
        const publicKeyHex = publicKey.encode("hex", false).slice(2); // Remove '04' prefix
        return {
            privateKey: privateKeyHex, // 64 characters (32 bytes)
            publicKey: publicKeyHex,   // 128 characters (64 bytes, uncompressed without 0x04)
            mnemonic,
            derivationPath
        };
    } catch (error) {
        console.error("Error generating keys:", error);
        throw error;
    }
}

generateKeysFromMnemonic("table estate tomorrow differ dial boost traffic disorder vibrant pave cradle august");


