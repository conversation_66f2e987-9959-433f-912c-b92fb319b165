{"compileOnSave": true, "compilerOptions": {"target": "ES2020", "module": "commonjs", "moduleResolution": "node", "experimentalDecorators": true, "emitDecoratorMetadata": true, "inlineSourceMap": true, "noImplicitThis": true, "noUnusedLocals": true, "stripInternal": true, "skipLibCheck": true, "pretty": true, "declaration": false, "typeRoots": ["./typings", "./node_modules/@types"], "outDir": "dist", "allowJs": false, "strict": true, "noImplicitAny": false, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "alwaysStrict": true, "esModuleInterop": true, "noUnusedParameters": false}, "exclude": ["dist", "node_modules", "test"]}