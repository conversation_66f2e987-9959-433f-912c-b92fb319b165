# Flow 区块链深度调研报告
Flow 链一个独立的、全新的 Layer-1 (L1) 区块链。最初设计时就专注于大规模的消费者应用、游戏和数字收藏品 (NFT)，因此在性能、用户体验和开发者工具方面做了大量优化。

## 1. 链特性
- 以太坊 (传统): 采用“单片链”架构，所有节点执行所有功能（共识、执行、存储）。虽然现在转向了分片（Sharding），但基础设计理念不同。使用 Solidity 语言编写智能合约。
- Flow: 采用独特的多角色、管道式架构，将区块链的工作职责分解为四种不同类型的节点（收集节点、共识节点、执行节点、验证节点）。每个节点专注于特定的任务，这使得 Flow 能够实现高吞吐量和并行处理，而无需分片。 使用其自研的资源导向型编程语言 Cadence。Cadence 专为数字资产和 NFT 设计，提供了更强的安全性和可组合性。

## 2. 离线地址和离线签名（签名算法）
- 离线原生地址：通过公私钥不能推导离线原生地址（8字节）。账号地址可以通过Flow CLI、FCL.js 或 Flow SDK 在链下应用程序（客户端）中完成。
- 离线EVM地址：通过公私钥可以推导离线EVM地址（20个字节），通过flow EVM兼容桥接原生地址
- 离线签名： ECDSA和BLS

## 3. 共识算法（确认时间与是否支持质押）
- 共识算法： 始终采用权益证明 (PoS) 机制，并使用 HotStuff 共识协议的变体，这为其提供了近乎即时的交易最终性。
- 确认时间（Time to Finality）： Flow 旨在实现快速最终性（Fast Finality）。由于其 BFT 共识机制和高效的多角色架构，交易一旦被共识节点确认，就几乎是立即最终确定的，无法回滚。通常，Flow 交易的最终性在几秒钟内即可达成（通常为 1-2 个区块确认）。
- 是否支持质押： 支持质押。FLOW 代币是 Flow 区块链的原生代币，用于网络费用、治理和质押。验证器节点必须质押 FLOW 代币才能参与网络运行并获得奖励。

## 4. 账户模型
- 账户模型： 拥有更高级的账户抽象功能，账户本身就是智能合约，支持多签、密钥轮换、模块化等高级特性。
![img.png](img.png)
- 账户体系：每个 Flow 账户都有一个唯一的地址。账户可以存储代码（智能合约）和数据（包括用户的数字资产如 FLOW 代币、NFT 等）。
- 账户抽象： Flow 的账户模型原生支持高级的账户抽象功能，允许更灵活的密钥管理（例如多重签名、轮换密钥、授权限时操作等）。这为用户提供了类似传统互联网服务的体验，如社交恢复、每日交易限额等。
- Cadence 语言对账户的管理： Cadence 语言将资源（如代币、NFT）直接绑定到账户的存储中，并通过类型系统强制执行所有权和移动规则，确保数字资产的安全性和稀缺性。

## 5. 代币精度
- 代币精度： Flow 的原生代币FLOW的最小单位是 "flow"，其精度为 8 位小数。1 FLOW = 10^8flow。

## 6. 密码学算法
- 哈希算法： SHA2_256和SHA3_256。
- 签名算法： ECDSA (ECDSA_secp256k1和ECDSA_P256) 和 BLS (Boneh-Lynn-Shacham)。

## 7. 交易模型
- 交易结构： Flow 交易包含一个或多个授权者、一个代码（包含一个或多个脚本或交易）以及可选的参数。
- 多方授权： 一笔交易可以由多个实体共同授权。例如，一个用户可以发起一个交易，其中包含用户和 DApp 共同签名的部分，或者多个用户共同签署一笔交易。这支持了复杂的交互模式。
- 灵活的费用支付： 除了用户直接支付，交易费用也可以由第三方（如 DApp）赞助，实现免 Gas 交易，极大地提升了用户体验。

## 8. 是否支持代币Token和NFT
- 是 Token (FLOW 代币)： Flow 有其原生的加密货币 FLOW。FLOW 代币用于网络费用（gas）、质押、治理和抵押数据存储。
- 是 NFT： Flow 是 NFT 领域的领导者之一，其 Cadence 语言和资源导向型编程模型从一开始就为数字收藏品和 NFT 提供了强大的原生支持。NBA Top Shot、NFL All Day 等都是 Flow 上的知名 NFT 项目。Flow 的设计使 NFT 的创建、转移和管理更加安全和直观。

## 9. 是否为多链结构
- 否，是单链结构： Flow 是一个单链结构，没有采用分片技术。它通过其独特的多角色架构实现了高性能，而不是通过将网络分成多个独立的分片。所有交易都在一个逻辑链上处理，从而避免了跨分片通信的复杂性和挑战。

## 10. RPC URL 节点和钱包 RPC 节点搭建
- RPCURL节点：Flow 提供了详细的 RPC（Remote Procedure Call）接口文档和公共的 RPC 端点，供开发者与 Flow 区块链进行交互。这些接口允许开发者发送交易、查询链上数据、获取区块信息、调用智能合约等。公共访问节点 URL 通常可在 Flow 官方文档或第三方服务商处找到。
- 钱包 RPC 节点搭建：
  验证器节点： 搭建完整的 Flow 验证器节点（包括收集、执行、验证和共识节点）是可能的，但需要较高的硬件配置、网络带宽和技术知识，并且通常需要质押大量 FLOW 代币。其多角色架构意味着搭建一个完整的验证器更复杂。
- 访问节点（Access Node）： 对于开发者或用户来说，通常不需要搭建完整的验证器节点。Flow 提供了访问节点（Access Node）服务，你可以连接到公共的访问节点（由 Flow 基金会或第三方提供）来查询链上数据和发送交易。
- 钱包节点： 个人用户通常不需要运行一个完整的钱包节点来同步所有数据。像 Dapper Wallet、Ledger Live、Blocto 等钱包服务会帮你处理与 Flow 网络的连接和数据同步。开发者可以使用 Flow SDK 连接到访问节点来构建钱包功能。

## 11. 手续费收费模型
![img_1.png](img_1.png)
每笔交易费用由三部分组成：执行费、纳入费和网络激增因子
- 执行费的事务的执行工作量取决于事务所采用的代码路径及其执行的操作。与执行工作量成本相关的操作可以分为四大类：正常的cadence、循环或函数调用；从storage中读取数据，按读取字节收费；将数据写入storage，按写入的字节收费；帐户创建。
- 纳入费的工作量代表了以下需要完成的工作：将交易纳入区块；将交易信息从一个节点传输到另一个节点；验证交易签名。
- 网络激增因子目前固定为1.0。

| 交易类型      | 预计成本（flow） |
| ----------- | ----------- |
| FT转移     | 0.00000185      |
| 铸造一个小的 NFT（很大程度上取决于 NFT 的大小）   | 0.0000019      |
| 空交易     | 0.000001      |
| 将密钥添加到账户   | 0.000001      |
| 创建1个账户     | 0.00000315    |
| 创建10个账号  | 0.00002261        |
| 部署一个约50kb合约  | 0.00002965       |

## 12. 历史回滚事件
-历史回滚事件： Flow 致力于实现快速最终性，这意味着一旦交易被确认，就无法回滚。因此，Flow 区块链通常没有重大、非预期的历史回滚事件。其 BFT 共识机制确保了交易的确定性。

## 13. 相关同源链
- 相关同源链： Flow并没有直接的“同源链”概念，它是一个独立的、从头构建的区块链。然而，考虑到其开发团队 Dapper Labs 曾是 CryptoKitties 的创造者，该项目最初在以太坊上运行，并因高交易量导致以太坊网络拥堵。这直接促使Dapper Labs团队开发了Flow，以解决以太坊在游戏和 NFT 领域的可扩展性问题。因此，可以认为 Flow 是为解决以太坊在特定领域局限性而诞生的新一代公链。

## 14. 是否带 Memo 和 Tag
- 和以太坊一样，Flow 链上的原生地址不自带 Memo 或 Tag。你直接在 Flow 账户之间转账时，只需要对方的 Flow 地址。
- 如果你在中心化交易所存取 FLOW 代币，交易所可能会要求你提供 Memo 或 Tag。这与区块链本身无关，而是交易所为了在其内部账户系统中识别用户的存款而设的机制（因为它们可能使用共享的充值地址）。

## 15. 扫链的 RPC 接口和开发者文档
api接口地址：https://developers.flow.com/http-api
查询账户信息接口：get https://rest-mainnet.onflow.org/v1/accounts/{address}
查询区块信息接口：get https://rest-mainnet.onflow.org/v1/blocks/{id}
查询交易接口: get https://rest-mainnet.onflow.org/v1/transactions/{id}
发送交易接口: post https://rest-mainnet.onflow.org/v1/transactions
```
 交易发送接口的request有几个关键参数：
 script参数传cadence脚本，
 sequence-number类似nonce自增字段，
 key-index默认为0，可以创建多个keys公钥绑定账号地址
 {
  "script": "string",
  "arguments": [
    "string"
  ],
  "reference_block_id": "string",
  "gas_limit": "string",
  "payer": "string",
  "proposal_key": {
    "address": "string",
    "key_index": "string",
    "sequence_number": "string"
  },
  "authorizers": [
    "string"
  ],
  "payload_signatures": [
    {
      "address": "string",
      "key_index": "string",
      "signature": "string"
    }
  ],
  "envelope_signatures": [
    {
      "address": "string",
      "key_index": "string",
      "signature": "string"
    }
  ]
}
```

## 16. 是否考虑桥
- Flow EVM兼容层 还提供了一个桥接机制，允许资产在 Flow 原生环境（Cadence）和 Flow EVM 环境之间进行转移。实现了与以太坊生态的互操作性，为开发者和用户提供了更多选择和便利

  ## 附录
  rpc node主网域名：https://rest-mainnet.onflow.org
  rpc node测试网域名：https://rest-testnet.onflow.org
  扫链地址：https://www.flowscan.io/和https://evm.flowscan.io
  api接口地址：https://developers.flow.com/http-api
  swagger文件：https://github.com/onflow/flow/blob/master/openapi/go-client-generated/api/swagger.yaml

| 特性 | 比特币 | 以太坊 | solana | flow |     
| ----------- | ----------- | ----------- | ----------- | ----------- |
|账户模型| UTXO (Unspent Transaction Output)：现金票据模式  | 账户模型：EOA (私钥) + CA (合约代码) | 程序中心化账户：程序无状态，数据存储在单独账户，有租金  |  资源导向型账户抽象：所有账户皆智能账户，原生支持多密钥、资源存储 |
|资产所有权| UTXO 直接代表资产  | Token 存在合约内部映射  | Token 存在特定数据账户 (Associated Token Account) | 资源直接存储在用户账户中|
|编程语言| Script (受限脚本语言) | Solidity  | Rust | Cadence (资源导向型)，也支持 Flow EVM 上的 Solidity |
|交易费用| 基于字节大小，PoW 竞争导致波动 | Gas 机制，基于计算复杂度和网络拥堵，费用波动大  |  低廉且可预测，基于指令数量和账户访问 | 主要为存储费 (押金模式)，计算费极低，费用可预测且低廉 |
|交易处理| 顺序执行，所有节点验证所有交易  | 顺序执行，所有节点验证所有交易 | 并行执行 (Sealevel)，利用预声明账户实现 | 多角色并行架构 (收集、共识、执行、验证节点)，流水线处理|
|交易最终性| 概率性 (约 60 分钟 6 确认) | PoS 最终性 (几十秒-几分钟) | 亚秒级最终性 (几百毫秒) | 近乎即时最终性 (约 6 秒)|
|核心优势| 数字黄金，去中心化，抗审查 | 智能合约平台先驱，DeFi 和 dApp 生态最庞大  | 高吞吐量，低延迟，扩展性强 | 高性能、低成本、用户友好，专为大规模消费者应用、游戏和 NFT 设计|