import {generateKeysFromMnemonic} from '../wallet/flow/mnemonic';
import {sendSignedTransaction} from '../wallet/flow'

describe("flow", () => {

  //助记词生成公私钥
  test('createKeyByMnemonic', () => {
    const memonic =" table estate tomorrow differ dial boost traffic disorder vibrant pave cradle august"
    console.log("generateKeysFromMnemonic()",generateKeysFromMnemonic(memonic))
  })

  //原生地址间转账
  test('sendSignedTransaction', async () => {
    await sendSignedTransaction();
  });

});
