# STX 链调研

## 1. 链简介
Stacks 是一个基于比特币的智能合约平台，旨在为比特币应用提供更丰富的功能。它采用分层方法，在比特币结算层的基础上，增加了可扩展性和功能，从而拥有与以太坊等链相同的功能。它拥有自己的代币，用它充当记录机制来维护所有交易的历史分类账。

Stacks 是一种比特币 Layer-2 解决方案，旨在为比特币网络带来智能合约功能和去中心化应用（dApp）。它采用 “转移证明”（PoX）共识机制，将交易锚定在比特币主链上。STX 可用于支付交易费、部署和交互智能合约、质押赚取 BTC 奖励以及对协议升级进行投票等。Stacks 层上的所有交易都会自动进行哈希计算，并在比特币 L1 上进行结算。Stacks 区块由 100% 比特币哈希算力保障安全。为了重新排序 Stacks 区块/交易，攻击者必须重组比特币。截至 2025 年 7 月，STX 价格约为 0.7545 美元，24 小时交易量为 59.91M 美元，市值排名第 71 位。

Stacks 链的目标是使比特币不仅仅是数字黄金，而是一个具有智能合约和去中心化应用能力的全面平台。通过 Stacks，开发者可以构建和部署智能合约，创建去中心化应用，并利用比特币的安全性和稳定性。

## 2. 链信息
- 链名称：Stacks
- 链简称：STX
- 链ID：测试网 0x80000000 ，主网 0x00000001
- 链类型：公链
- 代币精度：6
- 链官网：https://stacks.co
- faucet：https://platform.hiro.so/faucet
- 技术文档：http://docs.hiro.so/guides
- 白皮书：https://stacks-network.github.io/stacks/stacks.pdf
- 区块查询：https://explorer.hiro.so/txid/0xf061f4d306aceb72ec471033282d9ca7b2db89bc4e43cae38a9c563096a53ba8?chain=testnet
- 链logo：https://stacks.co/images/logo.png
- github:https://github.com/hirosystems/stacks.js/blob/main/packages/transactions/README.md

### 区块生产机制 —— 转移证明
Stacks 链的区块生产机制采用 “转移证明”（PoX）共识机制。与其他公链不同，Stacks 链的区块生产不需要 PoW 工作量证明，而是通过 “转移证明” 机制来验证区块的合法性。

烧毁证明是另一种不太常用的共识机制，其中矿工通过“烧毁”（销毁）工作量证明加密货币作为计算资源的代理来进行竞争。而转移证明 (PoX) 是销毁证明机制的扩展。PoX 使用现有区块链（本例中为比特币）的工作量证明加密货币来保护新区块链。然而，与销毁证明不同的是，矿工不会销毁加密货币，而是将承诺的加密货币转移给网络中的其他参与者。

**作用**
- 共识维护：通过 PoX 机制，Stacks 实现了去中心化的区块生成和验证，确保交易记录的一致性和不可篡改性。
- 资源高效利用：不同于 PoW 的算力浪费，PoX 将比特币作为 “价值载体” 用于共识，同时质押 STX 的用户可以获得 BTC 奖励（来自区块生成者转移的 BTC），形成激励循环。
- 跨链协同：PoX 是连接 Stacks（Layer2）与比特币（Layer1）的核心机制，使 Stacks 能在比特币网络之上构建智能合约、去中心化应用（dApp），同时继承比特币的安全性。

### 代币
Stacks (STX) 代币是 Stacks 区块链上的原生代币。最小单位是 1 微 STX。1,000,000 微 STX 可兑换 1 个 Stacks (STX)。

### 账户模型
STX的账户模型是基于比特币 UTXO（未花费交易输出）机制扩展而来的智能账户模型，支持智能合约功能，并采用PoX（Proof of Transfer）共识机制。每个账户包含一个地址、私钥、随机数以及一个或多个资产余额。

可以从私钥推导出公钥，反之不行。

**账户生成**
```
npm install --global @stacks/cli
stx make_keychain -t > cli_keychain.json
```
结果：
账户
```
{
  "mnemonic": "project issue breeze poem enforce repeat wasp cherry work pilot initial okay audit drop table neck foam odor eye wrestle cherry blur light siege",
  "keyInfo": {
    "privateKey": "b82c273337e820804194d1c62c458768bcce39912ae11f0ccc9f0b6cb4f7505201",
    "publicKey": "03fb1a9a4a0064473607e2c06ff0de0e50ab315a513f9ec78c458f9c9796502fca",
    "address": "ST1917NK07TS5XGETRRMA5NRVYNYGV4RPM8B9AG6M",
    "btcAddress": "mnzwU7QWaWfDFjE1cPYs9CajUeKsLe87fm",
    "wif": "cTki4s4XR4v3W8vx17ANNF4WroMf8X5PGsCSQeN4j8CnbkooJyQf",//压缩格式的 btcAddress 私钥。
    "index": 0//账户的随机数，从 0 开始
  }
}
```
'humble drama world bless midnight dignity wear view neck deliver brain timber inquiry curtain cupboard test about upon cricket hip bus element bachelor news'


### Stacking vs Staking 
虽然 Stacks 网络上的堆叠在概念上与质押类似，但 Stacks 不是 PoS 网络，并且存在一些关键区别。
- 在 Staking 中，用户锁定一个代币，并以该代币获得收益。在 Stacking 中，用户锁定一个代币 (STX)，并以“burnchain”代币 (BTC) 获得收益，而不是锁定的代币。在 PoX 中，收益来自有限的外部来源（来自 Stacks 矿工的比特币存款）。在 PoS 中，收益来自货币本身的发行计划，这意味着它在程序上是无限的（但理论上是有限的，我们将在下文中详细讨论）。
- 发行率的设定不同。在以太坊中，发行率由网络使用情况决定；而 Stacks 的发行率特指收益率。

### 技术架构
双层设计
• 主网（Stacks layer-1）：高度去中心化、低 TPS，采用 PoX 共识。
• 子网（Hyperchains/Subnet）：去中心化程度略低、高 TPS，可按需配置白名单节点 。
共识机制 – Proof of Transfer（PoX）
• 矿工：燃烧 BTC 竞标出块权 → 获得 STX 区块奖励 + 交易/合约费用。
• Stackers（质押者）：锁定 STX 一个周期（≈2 周）→ 验证区块并瓜分矿工烧掉的 BTC 作为奖励。
• 区块最终性：每 1 个比特币区块对应 1 个 Stacks 矿工任期；约 100 个 BTC 区块后，Stacks 交易获得比特币级不可逆性 。
智能合约语言 – Clarity
• 解释型、可判定、无重入漏洞；可直接读取比特币链状态，也能被比特币交易触发。
资产桥 – sBTC（Nakamoto 升级后推出）
• 去中心化、非托管的 1:1 BTC 锚定资产。
• 取款需 >70 % Stackers 多重签名，安全性由质押资本高于桥内 BTC 价值来经济保证 。

## 3. 链特点
- 智能合约：使用 Clarity 编程语言，这是一种专为区块链设计的安全、可预测的编程语言，允许开发者构建安全且可预测的智能合约，并且 Clarity 语言可以读取比特币主链的状态，使得 Stacks 层上的智能合约能以无需信任的方式，像比特币交易一样验证所有智能合约和交易记录。
- 与比特币深度整合：作为比特币的第二层解决方案，它在不修改比特币原始协议的基础上，扩展了比特币的功能，实现智能合约和 dApps。Stacks 上的所有交易都经过哈希处理并记录在比特币区块链上，确保了不变性和抗审查性，继承了比特币强大的安全性。
- 支持多种应用场景：为比特币带来了去中心化金融（DeFi）功能，可实现借贷、借款和收益农场等。同时支持非同质化代币（NFTs），艺术家和创作者能铸造和交易数字资产，还提供去中心化身份解决方案和区块链命名系统（BNS）。
- 原生代币功能丰富：原生代币 STX 用途广泛，可用于支付交易费用、执行智能合约，还可通过质押参与网络的共识机制，使持有者获得 BTC 奖励，此外也可用于对协议升级进行投票。
- 跨链支持：通过 Stackswap 跨链桥或借助 Wormhole 协议实现跨链，与多个公链（Ethereum、Bitcoin、Polkadot 等）进行跨链通信，允许开发者在多个链上进行开发，并使用同一代码进行部署。

## 4. 常用 API
- 获取最近交易：`curl -s "https://api.hiro.so/extended/v1/address/ST1917NK07TS5XGETRRMA5NRVYNYGV4RPM8B9AG6M/transactions?limit=10"`
- 查询账户余额（STX 代币）：`curl -X GET "https://api.hiro.so/extended/v1/address/ST1917NK07TS5XGETRRMA5NRVYNYGV4RPM8B9AG6M/balances?unanchored=false"`
- 获取账户信息：`curl https://api.hiro.so/api/v1/address/ST1917NK07TS5XGETRRMA5NRVYNYGV4RPM8B9AG6`
[hiro docs](https://docs.hiro.so/stacks/api/txs)
- 查询非同质化代币的持有情况：`curl -s "https://api.hiro.so/extended/v1/tokens/nft/holdings?principal=ST1917NK07TS5XGETRRMA5NRVYNYGV4RPM8B9AG6M&limit=50"
`

curl -s "https://api.mainnet.hiro.so/extended/v1/address/ST1917NK07TS5XGETRRMA5NRVYNYGV4RPM8B9AG6M/balances"


