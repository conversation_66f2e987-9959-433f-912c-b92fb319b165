import {ethers} from "ethers";
import {Interface} from "@ethersproject/abi";
import Common from "@ethereumjs/common";
import {FeeMarketEIP1559Transaction, Transaction} from "@ethereumjs/tx";
import BigNumber from "Bignumber.js";
// const BigNumber = require('Bignumber.js');

export function createAddress(seedHex: string, addressIndex: number) {
    const masterKey = ethers.utils.HDNode.fromSeed(Buffer.from(seedHex, "hex"));
    const {privateKey, publicKey, address} = masterKey.derivePath("m/44'/60'/0'/0/"+addressIndex.toString());
    return {privateKey, publicKey, address};
}

export function numberToHex(value: any): string {
    const number = new BigNumber(value);
    const result = number.toString(16);
    return "0x"+ result;
}

export function signTransaction(params:{privateKey: string, nonce:number, from:string, to:string, gasLimit:number,
    amount:string, gasPrice:number,decimal: number, chainId: number, tokenAddress:string, callData: string,
    maxPriorityFeePerGas?: number, maxFeePerGas?: number}) {
    let {privateKey, nonce, from, to, gasLimit, amount, gasPrice, decimal, chainId, tokenAddress, callData, maxPriorityFeePerGas, maxFeePerGas} = params;
    const nonceHex = numberToHex(nonce);
    const gasLimitHex = numberToHex(gasLimit);
    const chainIdHex = numberToHex(chainId);
    const amountWei = new BigNumber(amount).times((new BigNumber(10).pow(decimal)));
    const amountWeiHex = numberToHex(amountWei);
    // const callDataBuffer = Buffer.from(callData, "hex");
    const callDataBuffer = callData;
    let txData: any = {
        nonce: nonceHex,
        gasLimit: gasLimitHex,
        to,
        from,
        chainId: chainIdHex,
        value: amountWeiHex,
        data: callDataBuffer,
    }

    if (maxFeePerGas && maxPriorityFeePerGas) {
        txData.maxFeePerGas = numberToHex(maxFeePerGas);
        txData.maxPriorityFeePerGas = numberToHex(maxPriorityFeePerGas);
    } else {
        txData.gasPrice = numberToHex(gasPrice);
    }

    if (tokenAddress && tokenAddress !== "0x00") {
        const ABI = ["function transfer(address, uint256)"];
        const iface = new Interface(ABI);
        if (callData) {
            txData.data = callData;
        } else {
            txData.data = iface.encodeFunctionData("transfer",[to, amountWeiHex]);
            txData.to = tokenAddress;
        }
        txData.value = "0x0";
    }

    let common: any, tx: any;
    if (txData.maxFeePerGas && txData.maxPriorityFeePerGas) {
        common = (Common as any).custom({
           chainId: chainId, defaultHardfork: "london",
        });
        tx = FeeMarketEIP1559Transaction.fromTxData(txData, {common});
    } else {
        common = (Common as any).custom({
            chainId: chainId,
        })
        tx = Transaction.fromTxData(txData, {common});
    }

    const privateKeyBuffer = Buffer.from(privateKey, "hex");
    const signedTx = tx.sign(privateKeyBuffer);
    const serializedTx = signedTx.serialize();
    if (!serializedTx) {
        throw new Error("Unable to serialize transaction");
    }
    return `0x${serializedTx.toString('hex')}`;
}