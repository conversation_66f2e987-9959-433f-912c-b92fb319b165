const bip39 = require('bip39');

export enum WordLanguage {
    english = 'english',
    chinese_simplified = 'chinese_simplified',
    chinese_traditional = 'chinese_traditional',
    korean = 'korean',
    french = 'french',
    italian = 'italian',
    japanese = 'japanese',
    portuguese = 'portuguese'
}

export function generateMnemonic (wordCount: number ,language: WordLanguage) : string {
    if (!wordCount && !language) throw new Error('Must have wordCount and language');
    const wordLengthMap: Record<number, number> = {12: 128, 15: 160, 18: 192, 21:224, 24: 256};
    const keys = Object.keys(wordLengthMap);
    if (!(wordCount in wordLengthMap)) {
        throw new Error('wordCount must in ['+ keys.join(" ") + "]");
    }
    const num = wordLengthMap[wordCount];
    const wordList =bip39.wordlists[language];
    return bip39.generateMnemonic(num, null, wordList);
}

export function encodeMnemonic (mnemonic: string, language: WordLanguage): string {
    if (!mnemonic && !language) throw new Error('Must have mnemonic and language');
    const wordList = bip39.wordlists[language];
    return bip39.mnemonicToEntropy(mnemonic, wordList);
}

export function decodeMnemonic (encrytMnemonic: string, language: WordLanguage): string {
    if (!encrytMnemonic && !language) throw new Error('Must have mnemonic and language');
    const wordList = bip39.wordlists[language];
    return bip39.entropyToMnemonic(encrytMnemonic,wordList);
}

export function mnemonicToSeed (mnemonic: string, password?: string) {
    if (!mnemonic) throw new Error('Must have mnemonic');
    return bip39.mnemonicToSeedSync(mnemonic,password);
}

export function mnemonicToEntropy (params: { mnemonic: any; password: any; }) {
    const { mnemonic, password } = params;
    if (!mnemonic) throw new Error('Must have mnemonic');
    bip39.mnemonicToEntropy(mnemonic, password);
}

export function validateMnemonic (mnemonic: string, language: WordLanguage) {
    if (!mnemonic && !language) throw new Error('Must have mnemonic and language');
    const wordList = bip39.wordlists[language];
    return bip39.mnemonicToEntropy(mnemonic, wordList);
}
