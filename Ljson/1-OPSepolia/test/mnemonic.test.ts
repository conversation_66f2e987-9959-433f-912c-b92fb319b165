const bip39 = require("bip39");
import crypto_ts from 'crypto'
import * as myBip from "../wallet/bip/bip"
import * as myWallet from "../wallet"
const ethers = require("ethers");


describe("mnemonic", () => {
    // 手动创建助记词
    test("create mnemonic manually", () => {
        // 生成熵:16个字节的随机数,128位.
        const entropy = crypto_ts.randomBytes(16);

        // 计算校验和:取sha256的前4位
        const hash = crypto_ts.createHash("sha256").update(entropy).digest();
        const checkSum = hash[0] >> 4;

        // 组合熵与校验和:组合成二进制字符串 8*16+4 总共 132位
        let bits = '';
        for (let i = 0; i < entropy.length; i++) {
            bits += entropy[i].toString(2).padEnd(8, "0");
        }
        bits += checkSum.toString(2).padStart(4,'0');

        // 按11位长度分组,计算每组的值,即为后续单词表中的索引
        const indices = [];
        for (let i = 0; i < bits.length; i+= 11) {
            const index = parseInt(bits.slice(i, i + 11), 2);
            indices.push(index);
        }

        // 根据12个单词的索引值,在单词表中取出单词,就是助记词
        const wordList = bip39.wordlists.english;
        const mnemonic = indices.map(index => wordList[index]).join(" ");
        console.log("mnemonic: ", mnemonic);
    })

    test("create mnemonic by bip lib", () => {
        const language = myBip.WordLanguage.english;
        const mnemonic = myBip.generateMnemonic(24, language);
        console.log("mnemonic: ", mnemonic);

        const enMnemonic = myBip.encodeMnemonic(mnemonic, language);
        console.log("enMnemonic: ", enMnemonic);

        const deMnemonic = myBip.decodeMnemonic(enMnemonic, language);
        console.log("deMnemonic: ", deMnemonic);

        const seed = myBip.mnemonicToSeed(mnemonic);
        console.log("seed: ", seed.toString("hex"));

        const isValid = myBip.validateMnemonic(mnemonic, language);
        console.log("isValid: ", isValid);
    })

    test("mnemonic to wallet", () => {
        const mnemonic = "secret common tunnel biology search dirt scrap wealth frown awkward quiz limit pumpkin give portion remove flip final zoo program cactus when proud fever"
        const seed = myBip.mnemonicToSeed(mnemonic);
        const addressInfo = myWallet.createAddress(seed, 0);
        console.log("addressInfo: ", addressInfo);
    })
})