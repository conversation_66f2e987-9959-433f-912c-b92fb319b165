import * as myBip from "../wallet/bip/bip"
import * as myWallet from "../wallet"
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessageStatus} from "@eth-optimism/sdk";
const ethers = require("ethers");
import dotenv from "dotenv";


dotenv.config();

const privateKey = process.env.PRIVATE_KEY;
if (!privateKey) {
    throw new Error("Private key is missing")
}

const sepoliaRpcUrl = process.env.SEPOLIA_RPC_URL;
if (!sepoliaRpcUrl) {
    throw new Error("sepoliaRpcUrl is missing")
}
const sepoliaProvider = new ethers.providers.JsonRpcProvider(sepoliaRpcUrl);
const sepoliaWallet = new ethers.Wallet(privateKey, sepoliaProvider);


const opSepoliaRpcUrl = process.env.OP_SEPOLIA_RPC_URL;
if (!opSepoliaRpcUrl) {
    throw new Error("opSepoliaRpcUrl is missing")
}
const opProvider = new ethers.providers.JsonRpcProvider(opSepoliaRpcUrl);
const opWallet = new ethers.Wallet(privateKey, opProvider);

const walletAddress = sepoliaWallet.address;

describe("op-lib", () => {
    test("L1 deposit", async () => {
        const bridgeAddress = "******************************************";

        const bridgeAbi = [
            "function depositETH(uint32 _l2Gas, bytes calldata _data) payable returns (uint256)"
        ];

        const bridgeContract = new ethers.Contract(bridgeAddress, bridgeAbi, sepoliaWallet);

        // 要桥过去的ETH数量
        const amount = ethers.utils.parseEther("0.01");

        console.log(`开始跨链转账 ${ethers.utils.formatEther(amount)} ETH...`);

        const l1GasLimit = 2000000;
        const l2GasLimit = 2000000;

        // 调用存款函数
        const tx = await bridgeContract.depositETH(
            l2GasLimit,
            "0x",
            {
                value: amount,
                gasLimit: l1GasLimit,
            }
        );

        console.log(`交易已发送，哈希: ${tx.hash}`);
        console.log(`等待交易确认...`);

        const receipt = await tx.wait();
        console.log(`交易成功！区块号: ${receipt.blockNumber}`);
    })
    // Transaction Hash 0x4f8c2aa1316ea579f9dd8344374d72eab429f37fd0bc862d5504b85dde4cd57f

    test("op-sepolia get balance", async () => {
        const l2Balance = await opProvider.getBalance(walletAddress);
        console.log("L2 余额:", ethers.utils.formatEther(l2Balance), "ETH");
    })

    test("op-sepolia get gas", async () => {
        const gasPrice = await opProvider.getGasPrice();
        console.log("gas price:", gasPrice);
    })


    test("L2 withdraw", async  () => {
        const bridgeAddress = "******************************************";

        const bridgeAbi = [
            "function withdraw(address _l2Token,uint256 _amount,uint32 _minGasLimit,bytes _extraData) external payable returns (uint256)"
        ];
        const bridgeContract = new ethers.Contract(bridgeAddress, bridgeAbi, opWallet);

        // ETH token地址 (Bedrock)
        const l2TokenAddress = "******************************************";

        const amount = ethers.utils.parseEther("0.01");

        console.log(`开始提现 ${ethers.utils.formatEther(amount)} ETH...`);

        const l1GasLimit = 1000000;
        const l2GasLimit = 1000000;
        const tx = await bridgeContract.withdraw(
            l2TokenAddress,
            amount,
            l2GasLimit,  // _l2Gas
            "0x",    // _data
            {
                value: amount,
                gasLimit: l1GasLimit
            }
        );

        console.log(`L2提现交易hash: ${tx.hash}`);
        await tx.wait();
        console.log(`✅ L2提现交易已确认`);
        console.log(`下一步：等待挑战期后在L1 finalize`);
    })

    test("L2 withdraw by sdk", async () => {
        // 创建 CrossChainMessenger
        const messenger = new CrossChainMessenger({
            l1ChainId: 11155111, // Sepolia
            l2ChainId: 11155420, // Optimism Sepolia
            l1SignerOrProvider: sepoliaWallet,
            l2SignerOrProvider: opWallet,
        });

        const amount = ethers.utils.parseEther("0.001");

        console.log(`开始提现 ${ethers.utils.formatEther(amount)} ETH 从L2到L1...`);

        // L2: withdraw ETH
        const tx = await messenger.withdrawETH(amount);
        console.log(`L2提现交易hash: ${tx.hash}`);
        await tx.wait();
        console.log(`✅ L2提现交易已确认`);
    })

    test("status by sdk", async () => {
        const txHash = "0xad82fffa08fb85bb15f3d4fb5501451f9e0d527a0313d16ae7bb2e4b106ba9e4";
        // const txHash = "0x0e071220cff8bd86cda3c4bd0f7124aad02864d6529a5e04454790c9c8b1f469";
        // 创建 CrossChainMessenger
        const messenger = new CrossChainMessenger({
            l1ChainId: 11155111, // Sepolia
            l2ChainId: 11155420, // Optimism Sepolia
            l1SignerOrProvider: sepoliaWallet,
            l2SignerOrProvider: opWallet,
        });
        // 等待挑战期结束
        console.log(`等待消息状态变为 READY_FOR_RELAY...`);
        let status = await messenger.getMessageStatus(txHash);
        console.log(`当前状态: ${MessageStatus[status]}`);
        while (status !== MessageStatus.READY_FOR_RELAY) {
            console.log(`当前状态: ${MessageStatus[status]}，每60s检查一次...`);
            await new Promise((r) => setTimeout(r, 60000));
            status = await messenger.getMessageStatus(txHash);
        }
        console.log(`✅ 挑战期结束，准备在L1 finalize`);

        // 在L1 finalize withdrawal
        const finalizeTx = await messenger.finalizeMessage(txHash);
        console.log(`L1 finalize交易hash: ${finalizeTx.hash}`);
        await finalizeTx.wait();
        console.log(`✅ 完成提现！资金现在在L1可用`);
    },1000 * 60 * 30)

    test("prove by sdk", async () => {
        // const txHash = "0xad82fffa08fb85bb15f3d4fb5501451f9e0d527a0313d16ae7bb2e4b106ba9e4";
        const txHash = "0x0e071220cff8bd86cda3c4bd0f7124aad02864d6529a5e04454790c9c8b1f469";
        // 创建 CrossChainMessenger
        const messenger = new CrossChainMessenger({
            l1ChainId: 11155111, // Sepolia
            l2ChainId: 11155420, // Optimism Sepolia
            l1SignerOrProvider: sepoliaWallet,
            l2SignerOrProvider: opWallet,
        });
        const response = await messenger.proveMessage(txHash);
        console.log(`responseHash:${response.hash}`);
    })

    test("FINALIZATION_PERIOD_SECONDS", async  () => {
        const oracle = new ethers.Contract(
            "******************************************",
            ["function FINALIZATION_PERIOD_SECONDS() view returns (uint256)"],
            opProvider,
        );
        const seconds = await oracle.FINALIZATION_PERIOD_SECONDS();
        console.log(`Finalization Period: ${seconds} seconds`);

        // const bridgeAddress = "******************************************";
        //
        // const bridgeAbi = [
        //     "function FINALIZATION_PERIOD_SECONDS() view returns (uint256)"
        // ];
        //
        // const bridgeContract = new ethers.Contract(bridgeAddress, bridgeAbi, sepoliaWallet);
        //
        // // 调用存款函数
        // const seconds = await bridgeContract.FINALIZATION_PERIOD_SECONDS(
        //     {
        //         value: 0,
        //         gasLimit: 22000,
        //     }
        // );
        // console.log(`Finalization Period: ${seconds} seconds`);
    })
})