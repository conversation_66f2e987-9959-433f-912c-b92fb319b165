import * as myWallet from "../wallet"
const ethers = require("ethers");
import dotenv from "dotenv";


dotenv.config();

const privateKey = process.env.PRIVATE_KEY;
if (!privateKey) {
    throw new Error("Private key is missing")
}

const sepoliaRpcUrl = process.env.SEPOLIA_RPC_URL;
if (!sepoliaRpcUrl) {
    throw new Error("sepoliaRpcUrl is missing")
}
const sepoliaProvider = new ethers.providers.JsonRpcProvider(sepoliaRpcUrl);
const sepoliaWallet = new ethers.Wallet(privateKey, sepoliaProvider);

const walletAddress = sepoliaWallet.address;

describe("sepolia", () => {
    test("sepolia transfer", async () => {
        const nonce = await sepoliaProvider.getTransactionCount(walletAddress);
        console.log("Current nonce:", nonce);

        const rawHex = myWallet.signTransaction({
            privateKey: privateKey.slice(2),
            nonce: nonce,
            from: walletAddress,
            to:"******************************************",
            gasLimit: 91000,
            maxFeePerGas:327993150328,
            maxPriorityFeePerGas: 32799315032,
            gasPrice: 0,
            amount: "0.1",
            decimal: 18,
            chainId: 11155111,
            tokenAddress:"",
            callData: "",
        });
        console.log("rawHex: ", rawHex);

        const tx = await sepoliaProvider.sendTransaction(rawHex);
        console.log("Transaction hash:", tx.hash);

        const receipt = await tx.wait(1);
        console.log("Transaction confirmed in block:", receipt.blockNumber);
        console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
    })

    test("sepolia token transfer", async () => {
        const nonce = await sepoliaProvider.getTransactionCount(walletAddress);
        console.log("Current nonce:", nonce);

        const rawHex = myWallet.signTransaction({
            privateKey: privateKey.slice(2),
            nonce: nonce,
            from: walletAddress,
            to:"******************************************",
            gasLimit: 91000,
            maxFeePerGas:327993150328,
            maxPriorityFeePerGas: 32799315032,
            gasPrice: 0,
            amount: "50",
            decimal: 18,
            chainId: 11155111,
            tokenAddress:"******************************************",
            callData: "",
        });
        console.log("rawHex: ", rawHex);

        const tx = await sepoliaProvider.sendTransaction(rawHex);
        console.log("Transaction hash:", tx.hash);

        const receipt = await tx.wait(1);
        console.log("Transaction confirmed in block:", receipt.blockNumber);
        console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
    })
})