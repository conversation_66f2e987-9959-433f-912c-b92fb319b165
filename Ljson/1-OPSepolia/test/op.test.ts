import * as myWallet from "../wallet"
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessageStatus} from "@eth-optimism/sdk";
const ethers = require("ethers");
import dotenv from "dotenv";


dotenv.config();

const privateKey = process.env.PRIVATE_KEY;
if (!privateKey) {
    throw new Error("Private key is missing")
}

const sepoliaRpcUrl = process.env.SEPOLIA_RPC_URL;
if (!sepoliaRpcUrl) {
    throw new Error("sepoliaRpcUrl is missing")
}
const sepoliaProvider = new ethers.providers.JsonRpcProvider(sepoliaRpcUrl);
const sepoliaWallet = new ethers.Wallet(privateKey, sepoliaProvider);


const opSepoliaRpcUrl = process.env.OP_SEPOLIA_RPC_URL;
if (!opSepoliaRpcUrl) {
    throw new Error("opSepoliaRpcUrl is missing")
}
const opProvider = new ethers.providers.JsonRpcProvider(opSepoliaRpcUrl);
const opWallet = new ethers.Wallet(privateKey, opProvider);

const walletAddress = sepoliaWallet.address;

describe("op-sepolia", () => {
    test("op-sepolia transfer", async () => {
        const nonce = await opProvider.getTransactionCount(walletAddress);
        console.log("Current nonce:", nonce);

        const rawHex = myWallet.signTransaction({
            privateKey: privateKey?.slice(2),
            nonce: nonce,
            from: walletAddress,
            to:"******************************************",
            gasLimit: 91000,
            maxFeePerGas:327993150328,
            maxPriorityFeePerGas: 32799315032,
            gasPrice: 0,
            amount: "0.1",
            decimal: 18,
            chainId: 11155420,
            tokenAddress:"",
            callData: "",
        });

        console.log("rawHex: ", rawHex);

        const tx = await opProvider.sendTransaction(rawHex);
        console.log("Transaction hash:", tx.hash);

        const receipt = await tx.wait(1);
        console.log("Transaction confirmed in block:", receipt.blockNumber);
        console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
    })

    test("op-sepolia token transfer", async  () => {
        const nonce = await opProvider.getTransactionCount(walletAddress);
        console.log("Current nonce:", nonce);
        const rawHex = myWallet.signTransaction({
            privateKey: privateKey.slice(2),
            nonce:10,
            from: walletAddress,
            to:"******************************************",
            gasLimit: 91000,
            maxFeePerGas:327993150328,
            maxPriorityFeePerGas: 32799315032,
            gasPrice: 0,
            amount: "1000",
            decimal: 18,
            chainId: 11155420,
            tokenAddress:"0xTokenContractAddress",// token合约地址
            callData: "",
        });
        console.log("rawHex: ", rawHex);

        const tx = await opProvider.sendTransaction(rawHex);
        console.log("Transaction hash:", tx.hash);

        const receipt = await tx.wait(1);
        console.log("Transaction confirmed in block:", receipt.blockNumber);
        console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
    })

    test("L1 deposit", async () => {
        // L1 Standard Bridge地址
        const bridgeAddress = "******************************************";

        // L1 Standard Bridge ABI (只要 depositETH)
        const bridgeAbi = [
            "function depositETH(uint32 _l2Gas, bytes calldata _data) payable returns (uint256)"
        ];

        const bridgeContract = new ethers.utils.Interface(bridgeAbi);

        // 注意这里要设置
        const l1GasLimit = 2000000;
        const l2GasLimit = 2000000;
        const data = "0x";

        const callData = bridgeContract.encodeFunctionData("depositETH", [
            l2GasLimit,
            data,
        ]);

        const nonce = await sepoliaProvider.getTransactionCount(walletAddress);
        console.log("Current nonce:", nonce);

        // 要桥过去的ETH数量
        const amount = "0.01";
        console.log(`开始跨链转账 ${amount} ETH...`);

        const rawHex = myWallet.signTransaction({
            privateKey: privateKey.slice(2),
            nonce: nonce,
            from: walletAddress,
            to: bridgeAddress,
            gasLimit: l1GasLimit,
            maxFeePerGas:327993150328,
            maxPriorityFeePerGas: 32799315032,
            gasPrice: 0,
            amount: amount,
            decimal: 18,
            chainId: 11155111,
            tokenAddress: "0x00",
            callData: callData,
        });

        const tx = await sepoliaProvider.sendTransaction(rawHex);

        console.log(`L1交易发送成功，hash: ${tx.hash}`);
        console.log("等待交易确认...");

        await tx.wait();
        console.log("✅ L1交易已确认");
    })
    // Transaction Hash 0x4f8c2aa1316ea579f9dd8344374d72eab429f37fd0bc862d5504b85dde4cd57f

    test("op-sepolia get L2 balance", async () => {
        const l2Balance = await opProvider.getBalance(walletAddress);
        console.log("L2 余额:", ethers.utils.formatEther(l2Balance), "ETH");
    })

    test("op-sepolia L2 withdraw", async  () => {
        const bridgeAddress = "******************************************";

        const bridgeAbi = [
            "function withdraw(address _l2Token,uint256 _amount,uint32 _minGasLimit,bytes _extraData) external payable returns (uint256)"
        ];

        const bridgeContract = new ethers.utils.Interface(bridgeAbi);

        // ETH token地址 (Bedrock)
        const l2TokenAddress = "******************************************";

        const amountStr = "0.01"
        const amount = ethers.utils.parseEther(amountStr);

        const l1GasLimit = 1000000;
        const l2GasLimit = 1000000;
        const data = "0x";

        const callData = bridgeContract.encodeFunctionData("withdraw", [
            l2TokenAddress,
            amount,
            l1GasLimit,
            data,
        ]);

        const nonce = await opProvider.getTransactionCount(walletAddress);
        console.log("Current nonce:", nonce);

        console.log(`开始跨链转账 ${amountStr} ETH...`);

        const rawHex = myWallet.signTransaction({
            privateKey: privateKey.slice(2),
            nonce: nonce,
            from: walletAddress,
            to: bridgeAddress,
            gasLimit: l2GasLimit,
            maxFeePerGas:327993150328,
            maxPriorityFeePerGas: 32799315032,
            gasPrice: 0,
            amount: amountStr,
            decimal: 18,
            chainId: 11155420,
            tokenAddress: "0x00",
            callData: callData,
        });

        const tx = await opProvider.sendTransaction(rawHex);

        console.log(`L2提现交易hash: ${tx.hash}`);
        await tx.wait();
        console.log(`✅ L2提现交易已确认`);

        console.log(`下一步：等待挑战期后在L1 finalize`);
    }, 1000 * 600)

    test("withdraw status", async () => {
        const txHash = "0xad82fffa08fb85bb15f3d4fb5501451f9e0d527a0313d16ae7bb2e4b106ba9e4";
        // const txHash = "0x0e071220cff8bd86cda3c4bd0f7124aad02864d6529a5e04454790c9c8b1f469";
        // const txHash = "0x9f6c0453153b660a8ce90968228ea4718e9d7d1269f273e0cd20b44ba6137ecc";
        // 创建 CrossChainMessenger
        const messenger = new CrossChainMessenger({
            l1ChainId: 11155111, // Sepolia
            l2ChainId: 11155420, // Optimism Sepolia
            l1SignerOrProvider: sepoliaWallet,
            l2SignerOrProvider: opWallet,
        });
        // 等待挑战期结束
        console.log(`等待消息状态变为 READY_FOR_RELAY...`);
        let status = await messenger.getMessageStatus(txHash);
        console.log(`当前状态: ${MessageStatus[status]}`);
        while (status !== MessageStatus.READY_FOR_RELAY) {
            console.log(`当前状态: ${MessageStatus[status]}，每60s检查一次...`);
            await new Promise((r) => setTimeout(r, 60000));
            status = await messenger.getMessageStatus(txHash);
        }
        console.log(`✅ 挑战期结束，准备在L1 finalize`);

        // 在L1 finalize withdrawal
        const finalizeTx = await messenger.finalizeMessage(txHash);
        console.log(`L1 finalize交易hash: ${finalizeTx.hash}`);
        await finalizeTx.wait();
        console.log(`✅ 完成提现！资金现在在L1可用`);
    },1000 * 60 * 30)

    test("send prove", async () => {
        // const txHash = "0xad82fffa08fb85bb15f3d4fb5501451f9e0d527a0313d16ae7bb2e4b106ba9e4";
        const txHash = "0x0e071220cff8bd86cda3c4bd0f7124aad02864d6529a5e04454790c9c8b1f469";
        // 创建 CrossChainMessenger
        const messenger = new CrossChainMessenger({
            l1ChainId: 11155111, // Sepolia
            l2ChainId: 11155420, // Optimism Sepolia
            l1SignerOrProvider: sepoliaWallet,
            l2SignerOrProvider: opWallet,
        });
        const response = await messenger.proveMessage(txHash);
        console.log(`responseHash:${response.hash}`);
    })
})