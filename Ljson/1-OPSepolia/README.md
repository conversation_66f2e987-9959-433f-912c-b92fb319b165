# OP 作业

## 1. 基于 OP 做 ETH 转账测试

``` javascript
// op-sepolia ETH 转账
test("op-sepolia transfer", async () => {
    const nonce = await opProvider.getTransactionCount(walletAddress);
    console.log("Current nonce:", nonce);

    const rawHex = myWallet.signTransaction({
        privateKey: privateKey?.slice(2),
        nonce: nonce,
        from: walletAddress,
        to:"******************************************",
        gasLimit: 91000,
        maxFeePerGas:327993150328,
        maxPriorityFeePerGas: 32799315032,
        gasPrice: 0,
        amount: "0.1",
        decimal: 18,
        chainId: 11155420,
        tokenAddress:"",
        callData: "",
    });

    console.log("rawHex: ", rawHex);

    const tx = await opProvider.sendTransaction(rawHex);
    console.log("Transaction hash:", tx.hash);

    const receipt = await tx.wait(1);
    console.log("Transaction confirmed in block:", receipt.blockNumber);
    console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
})
```
- 交易hash: 0x4450eca24b78b7ebec5de98bf4d120204a615f6031dc848919686080a3517317
- 交易详情: [https://sepolia.etherscan.io/tx/0x4450eca24b78b7ebec5de98bf4d120204a615f6031dc848919686080a3517317](https://sepolia.etherscan.io/tx/0x4450eca24b78b7ebec5de98bf4d120204a615f6031dc848919686080a3517317)


## 2. 基于 OP 做 Token 转账测试

```javascript
// op-sepolia Token 转账
test("op-sepolia token transfer", async  () => {
    const nonce = await opProvider.getTransactionCount(walletAddress);
    console.log("Current nonce:", nonce);
    const rawHex = myWallet.signTransaction({
        privateKey: privateKey.slice(2),
        nonce:10,
        from: walletAddress,
        to:"******************************************",
        gasLimit: 91000,
        maxFeePerGas:327993150328,
        maxPriorityFeePerGas: 32799315032,
        gasPrice: 0,
        amount: "1000",
        decimal: 18,
        chainId: 11155420,
        tokenAddress:"0xTokenContractAddress",// token合约地址
        callData: "",
    });
    console.log("rawHex: ", rawHex);

    const tx = await opProvider.sendTransaction(rawHex);
    console.log("Transaction hash:", tx.hash);

    const receipt = await tx.wait(1);
    console.log("Transaction confirmed in block:", receipt.blockNumber);
    console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
})
```
- 签名过程:整体签名过程与eth转账类似,需要指定tokenAddress.
- 该钱包没有token,没有演示流程

## 3. Op 充值

```javascript
test("L1 deposit", async () => {
    const bridgeAddress = "******************************************";
    
    const bridgeAbi = [
        "function depositETH(uint32 _l2Gas, bytes calldata _data) payable returns (uint256)"
    ];

    const bridgeContract = new ethers.utils.Interface(bridgeAbi);
    
    const l1GasLimit = 2000000;
    const l2GasLimit = 2000000;
    const data = "0x";

    const callData = bridgeContract.encodeFunctionData("depositETH", [
        l2GasLimit,
        data,
    ]);

    const nonce = await sepoliaProvider.getTransactionCount(walletAddress);
    console.log("Current nonce:", nonce);

    // 要桥过去的ETH数量
    const amount = "0.01";
    console.log(`开始跨链转账 ${amount} ETH...`);

    const rawHex = myWallet.signTransaction({
        privateKey: privateKey.slice(2),
        nonce: nonce,
        from: walletAddress,
        to: bridgeAddress,
        gasLimit: l1GasLimit,
        maxFeePerGas:327993150328,
        maxPriorityFeePerGas: 32799315032,
        gasPrice: 0,
        amount: amount,
        decimal: 18,
        chainId: 11155111,
        tokenAddress: "0x00",
        callData: callData,
    });

    const tx = await sepoliaProvider.sendTransaction(rawHex);

    console.log(`L1交易发送成功，hash: ${tx.hash}`);
    console.log("等待交易确认...");

    await tx.wait();
    console.log("✅ L1交易已确认");
})
```
- 交易hash: 0x1e3c60287f74d2b192789a85142b2ae1b928d1961a49ce0b192c060c56a5e4d7
- sepolia交易详情: [https://sepolia.etherscan.io/tx/0x1e3c60287f74d2b192789a85142b2ae1b928d1961a49ce0b192c060c56a5e4d7](https://sepolia.etherscan.io/tx/0x1e3c60287f74d2b192789a85142b2ae1b928d1961a49ce0b192c060c56a5e4d7)
- OP浏览器查看钱包地址,Internal中,有对应的收款交易.


## 4. Op 提现

```javascript
test("op-sepolia L2 withdraw", async  () => {
    const bridgeAddress = "******************************************";

    const bridgeAbi = [
        "function withdraw(address _l2Token,uint256 _amount,uint32 _minGasLimit,bytes _extraData) external payable returns (uint256)"
    ];

    const bridgeContract = new ethers.utils.Interface(bridgeAbi);

    // ETH token地址 (Bedrock)
    const l2TokenAddress = "******************************************";

    const amountStr = "0.01"
    const amount = ethers.utils.parseEther(amountStr);

    const l1GasLimit = 1000000;
    const l2GasLimit = 1000000;
    const data = "0x";

    const callData = bridgeContract.encodeFunctionData("withdraw", [
        l2TokenAddress,
        amount,
        l1GasLimit,
        data,
    ]);

    const nonce = await opProvider.getTransactionCount(walletAddress);
    console.log("Current nonce:", nonce);

    console.log(`开始跨链转账 ${amountStr} ETH...`);

    const rawHex = myWallet.signTransaction({
        privateKey: privateKey.slice(2),
        nonce: nonce,
        from: walletAddress,
        to: bridgeAddress,
        gasLimit: l2GasLimit,
        maxFeePerGas:327993150328,
        maxPriorityFeePerGas: 32799315032,
        gasPrice: 0,
        amount: amountStr,
        decimal: 18,
        chainId: 11155420,
        tokenAddress: "0x00",
        callData: callData,
    });

    const tx = await opProvider.sendTransaction(rawHex);

    console.log(`L2提现交易hash: ${tx.hash}`);
    await tx.wait();
    console.log(`✅ L2提现交易已确认`);

    console.log(`下一步：等待挑战期后在L1 finalize`);
}, 1000 * 600)
```
- 交易hash:0x9f6c0453153b660a8ce90968228ea4718e9d7d1269f273e0cd20b44ba6137ecc
- OP交易详情:[https://sepolia-optimism.etherscan.io/tx/0x9f6c0453153b660a8ce90968228ea4718e9d7d1269f273e0cd20b44ba6137ecc](https://sepolia-optimism.etherscan.io/tx/0x9f6c0453153b660a8ce90968228ea4718e9d7d1269f273e0cd20b44ba6137ecc)
- 由于提现流程需要L2同步到L1需要一定时间的等待,交易会进入以下几种状态
  - STATE_ROOT_NOT_PUBLISHED = 0, // L2 Output还没提交到L1
  - READY_TO_PROVE = 1, // L2 Output已经提交，可以提交prove
  - IN_CHALLENGE_PERIOD = 2, // 已经prove了，进入挑战期，等待结束
  - READY_FOR_RELAY = 3, // 挑战期结束，可以finalize
  - RELAYED = 4, // 已经finalize完成，资金到账
- 当 数据同步到 L1之后,需要 提交prove,提交完成之后进入挑战期
  - 提交prove合约函数: `"function proveWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes), uint256, (bytes32,bytes32,bytes32,bytes32), bytes) external",` 
  - 但是 进入挑战期之后,1天时间,状态都没有变为 READY_FOR_RELAY
- 挑战期结束,调用 finalizeWithdrawalTransaction
  - 合约函数: ` "function finalizeWithdrawalTransaction((uint256,address,address,uint256,uint256,bytes), bytes32) external"`

## 5. 扫链rpc
- eth_blockNumber:获取区块高度
- eth_getBlockByNumber: 获取区块详情
- eth_getBlockByHash
- eth_getLogs
- eth_call 调用合约,查看交易是否已过挑战期等
  - latestOutputIndex
  - getL2Output
  - finalizationPeriodSeconds
  - finalizeWithdrawalTransaction

## 6. 挑战期

#### 主网
* 挑战期长度：7天
* [配置文件:optimism/packages/contracts-bedrock/deploy-config
  /mainnet.json](https://github.com/ethereum-optimism/optimism/blob/develop/packages/contracts-bedrock/deploy-config/mainnet.json)
  * "finalizationPeriodSeconds": 604800,
* [读取合约值: finalizationPeriodSeconds](https://etherscan.io/address/******************************************#readProxyContract#F8) 
  * The minimum time (in seconds) that must elapse before a withdrawal can be finalized.
  * 604800 uint256
* [主网OptimismPortalProxy合约:proofMaturityDelaySeconds](https://etherscan.io/address/******************************************#readProxyContract#F12)
  * 604800 uint256

#### 测试网
* 挑战期长度：7天
* [测试网OptimismPortalProxy合约:proofMaturityDelaySeconds](https://sepolia.etherscan.io/address/******************************************#readProxyContract#F12)
  * 604800 uint256

#### [op各合约地址文档](https://docs.optimism.io/superchain/addresses)


