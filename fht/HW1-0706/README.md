# HW1 OP
-- written by fht
## 1. Deposit 

### 1.1 Deposit ETH from L1 to L2
test "deposit from L1 to L2 sepolia"

depositETH: 
0x5ee486d33fb07e11fa6f3011198cb1e82d8dd1128d09712f2951874e5a056a30

depositETHto:
0xb1e6fe7de150239812938bca0d9f70f4b4f03a901986947561b17dbf942fd177

### 1.2 Deposit ERC20 from L1 to L2
test "deposit from L1 to L2 sepolia"

Before running this, I deployed the TWT token on Sepolia and createStandardL2Token on L2. 

TWT token eth sepolia:
******************************************

TWT token op sepolia (mapping from L1):
******************************************

However, the transaction failed, need to check the reason.

depositERC20:
0x3f0cadfb3cb1ed9096d7e25f761ad9f8037367d8df70cd409509cb01d97695ea

depositERC20To: TODO

## 2. Transfer 
### 2.1 Transfer ETH on OP
0xc91423a453856807833e8eb2eec2d54d2a58c0b195cb511eaefe27b8a56b0960
### 2.2 Transfer ERC20 on OP
Need 1.2 to be done first.


## 3. RPC
https://documenter.getpostman.com/view/4117254/ethereum-json-rpc/RVu7CT5J#6451b2d4-6d44-15b3-5110-fd1b1f522e1a

Same as ETH, but with different RPC. https://optimism-sepolia.drpc.org

{
"jsonrpc":"2.0",
"method":"eth_getTransactionCount",
"params":[
"******************************************",
"latest"
],
"id":1
}


## 4. faultGameWithdrawalDelay
https://github.com/ethereum-optimism/optimism/blob/develop/packages/contracts-bedrock/deploy-config/sepolia.json

"faultGameWithdrawalDelay": 604800, (7 days in seconds)

"disputeGameFinalityDelaySeconds": 302400, (3.5 days in seconds)

7 days - 10.5 days

