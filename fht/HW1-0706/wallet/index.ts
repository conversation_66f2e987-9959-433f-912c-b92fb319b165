import {Interface} from '@ethersproject/abi';
import {FeeMarketEIP1559Transaction, Transaction} from '@ethereumjs/tx'
import Common from '@ethereumjs/common'
const ethers = require('ethers');
const BigNumber = require('bignumber.js');

export function numberToHex(value: any) {
    const number = BigNumber(value);
    const result = number.toString(16);
    return '0x' + result;
}
export function signTransaction(params: { privateKey: string; nonce: number; from: string; to: string; gasLimit: number; amount: string; gasPrice: number; decimal: number; chainId: any; tokenAddress: string; callData: string;  maxPriorityFeePerGas?: number; maxFeePerGas?: number; }) {
    let { privateKey, nonce, from, to, gasPrice, gasLimit, amount, tokenAddress, callData,  decimal, maxPriorityFeePerGas, maxFeePerGas, chainId } = params;
    const transactionNonce = numberToHex(nonce);
    const gasLimits = numberToHex(gasLimit);
    const chainIdHex = numberToHex(chainId);
    let newAmount = BigNumber(amount).times((BigNumber(10).pow(decimal)));
    const numBalanceHex = numberToHex(newAmount);
    let txData: any = {
        nonce: transactionNonce,
        gasLimit: gasLimits,
        to,
        from,
        chainId: chainIdHex,
        value: numBalanceHex
    }
    if (maxFeePerGas && maxPriorityFeePerGas) {
        txData.maxFeePerGas = numberToHex(maxFeePerGas);
        txData.maxPriorityFeePerGas = numberToHex(maxPriorityFeePerGas);
    } else {
        txData.gasPrice = numberToHex(gasPrice);
    }
    if (tokenAddress && tokenAddress !== "0x00") {
        const ABI = ["function transfer(address to, uint amount)"];
        const iface = new Interface(ABI);
        if (params.callData) {
            txData.data = callData;
            txData.value = "0x0";
        } else {
            txData.data = iface.encodeFunctionData("transfer", [to, numBalanceHex]);
            txData.to = tokenAddress;
        }
        txData.value = "0x0";
    }
    let common: any, tx: any;
    if (txData.maxFeePerGas && txData.maxPriorityFeePerGas) {
        common = (Common as any).custom({
            chainId: chainId,
            defaultHardfork: "london"
        });
        tx = FeeMarketEIP1559Transaction.fromTxData(txData, {
            common
        });
    } else {
        common = (Common as any).custom({ chainId: chainId })
        tx = Transaction.fromTxData(txData, {
            common
        });
    }
    const privateKeyBuffer = Buffer.from(privateKey, "hex");
    const signedTx = tx.sign(privateKeyBuffer);
    const serializedTx = signedTx.serialize();
    if (!serializedTx) {
        throw new Error("sign is null or undefined");
    }
    return `0x${serializedTx.toString('hex')}`;
}
export function depositL1ToL2(params: { privateKey: string; nonce: number; from: string; to: string; gasLimit: number; amount: string; gasPrice: number; decimal: number; chainId: any; bridgeAddress: string; callData: string; maxPriorityFeePerGas?: number; maxFeePerGas?: number; depositType?: 'ETH' | 'ERC20'; l1Token?: string; l2Token?: string; minGasLimit?: number; extraData?: string; }) {
    let { privateKey, nonce, from, to, gasPrice, gasLimit, amount, bridgeAddress, callData, decimal, maxPriorityFeePerGas, maxFeePerGas, chainId, depositType, l1Token, l2Token, minGasLimit, extraData } = params;
    const transactionNonce = numberToHex(nonce);
    const gasLimits = numberToHex(gasLimit);
    const chainIdHex = numberToHex(chainId);
    let newAmount = BigNumber(amount).times((BigNumber(10).pow(decimal)));
    const numBalanceHex = numberToHex(newAmount);
    let txData: any = {
        nonce: transactionNonce,
        gasLimit: gasLimits,
        to,
        from,
        chainId: chainIdHex,
        value: numBalanceHex
    }
    if (maxFeePerGas && maxPriorityFeePerGas) {
        txData.maxFeePerGas = numberToHex(maxFeePerGas);
        txData.maxPriorityFeePerGas = numberToHex(maxPriorityFeePerGas);
    } else {
        txData.gasPrice = numberToHex(gasPrice);
    }

    // Handle L1 to L2 deposit operations

    /*
    * function depositETH(uint32 _minGasLimit, bytes calldata _extraData)
    * function depositETHTo(address _to, uint32 _minGasLimit, bytes calldata _extraData)
    *
    * function depositERC20(address _l1Token,address _l2Token,uint256 _amount, uint32 _minGasLimit, bytes calldata _extraData)
    * function depositERC20To(address _l1Token, address _l2Token, address _to, uint256 _amount, uint32 _minGasLimit, bytes calldata _extraData)
    * */
    if (depositType === 'ETH') {
        const depositETHABI = ["function depositETH(uint32, bytes)", "function depositETHTo(address, uint32, bytes)"];
        const iface = new Interface(depositETHABI);
        const minGasLimitValue = minGasLimit || 200000; // set default minimum gas limit = 200000
        const extraDataBytes = extraData || "0x"; // set default extra data to empty bytes

        if (to) {
            txData.data = iface.encodeFunctionData("depositETHTo", [to, minGasLimitValue, extraDataBytes]);
        } else {
            txData.data = iface.encodeFunctionData("depositETH", [minGasLimitValue, extraDataBytes]);
        }
        txData.value = numBalanceHex;

    } else if (depositType === 'ERC20') {
        const depositERC20ABI = [
            "function depositERC20(address, address, uint256, uint32, bytes)",
            "function depositERC20To(address, address, address, uint256, uint32, bytes)"
        ];
        const iface = new Interface(depositERC20ABI);
        const minGasLimitValue = minGasLimit || 200000;
        const extraDataBytes = extraData || "0x";

        if (to) {
            txData.data = iface.encodeFunctionData("depositERC20To", [l1Token, l2Token, to, numBalanceHex, minGasLimitValue, extraDataBytes]);
        } else {
            txData.data = iface.encodeFunctionData("depositERC20", [l1Token, l2Token, numBalanceHex, minGasLimitValue, extraDataBytes]);
        }
        txData.value = "0x0";
    }
    txData.to = bridgeAddress;
    let common: any, tx: any;
    if (txData.maxFeePerGas && txData.maxPriorityFeePerGas) {
        common = (Common as any).custom({
            chainId: chainId,
            defaultHardfork: "london"
        });
        tx = FeeMarketEIP1559Transaction.fromTxData(txData, {
            common
        });
    } else {
        common = (Common as any).custom({ chainId: chainId })
        tx = Transaction.fromTxData(txData, {
            common
        });
    }
    const privateKeyBuffer = Buffer.from(privateKey, "hex");
    const signedTx = tx.sign(privateKeyBuffer);
    const serializedTx = signedTx.serialize();
    if (!serializedTx) {
        throw new Error("sign is null or undefined");
    }
    return `0x${serializedTx.toString('hex')}`;
}



