import {signTransaction, depositL1ToL2} from "../wallet";


describe("opchain test", () => {
    // Test depositETH from L1 to L2 (<PERSON><PERSON><PERSON><PERSON> sepolia testnet here)
    test("deposit from L1 to L2 sepolia", async () => {
        const privateKey = "";
        // Sign transaction
        const rawHex = depositL1ToL2({
            privateKey: privateKey,
            nonce: 34,
            from: "******************************************",
            to: "",
            gasLimit: 300000,
            maxFeePerGas: 40000000000,
            maxPriorityFeePerGas: 30000000000,
            gasPrice: 0,
            amount: "0.02",
            decimal: 18,
            chainId: 11155111,
            bridgeAddress: "******************************************",
            callData: "",
            depositType: "ERC20",
            l1Token: "******************************************",
            l2Token: "******************************************",
            minGasLimit: 10000,
            extraData: "0x",
        });
        console.log(rawHex);
    });

    // Test ETH Transfer
    test("sign ETH transfer", async () => {
        const privateKey = "";
        // Sign transaction
        const rawHex = signTransaction({
            privateKey: privateKey,
            nonce: 13,
            from: "******************************************",
            to: "******************************************",
            gasLimit: 90000,
            maxFeePerGas: 327993150328,
            maxPriorityFeePerGas: 32799315032,
            gasPrice: 0,
            amount: "0.01",
            decimal: 18,
            chainId: 11155420,
            tokenAddress: "0x00",
            callData: "",
        });
        console.log(rawHex);
    });

    // Test ERC20 Transfer
    test("sign ERC20 transfer", async () => {
        const privateKey = "";
        // Sign transaction
        const rawHex = signTransaction({
            privateKey: privateKey,
            nonce: 7,
            from: "******************************************",
            to: "******************************************",
            gasLimit: 90000,
            maxFeePerGas: 327993150328,
            maxPriorityFeePerGas: 32799315032,
            gasPrice: 0,
            amount: "100",
            decimal: 18,
            chainId: 11155111,
            tokenAddress: "******************************************",
            callData: "",
        });
        console.log(rawHex);
    });
})
