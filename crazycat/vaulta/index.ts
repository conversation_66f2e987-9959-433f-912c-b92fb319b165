import * as hdkey from 'hdkey';
import * as wif from 'wif';
import { PrivateKey, PublicKey, APIClient, Action, Transaction, SignedTransaction, Bytes, Serializer, Name, Authority, ABI, Struct } from '@wharfkit/antelope';
import * as fs from 'fs';
import * as path from 'path';

const VAULTA_CHAIN_ID = "aca376f206b8fc25a6ed44dbdc66547c36c6c33e3a119ffbeaef943642f0e906";

const abiPath = path.join(__dirname, 'abi.json');
const VAULTA_ABI = ABI.from(JSON.parse(fs.readFileSync(abiPath, 'utf8')));

/**
 * Validate Vaulta account name format
 */
function validateAccountName(accountName: string): boolean {
    const regex = /^([a-z1-5.]{1,11}[a-z1-5]$)|([a-z1-5.]{12}[a-j1-5]$)/;

    if (accountName.startsWith('.') || accountName.endsWith('.') || accountName.includes('..')) {
        return false;
    }

    return regex.test(accountName);
}

/**
 * Create private key and public key using BIP44
 */
export function createKeyPair(seed: string, index: number = 0): {
    privateKey: string;
    publicKey: string;
} {
    const seedBuffer = Buffer.from(seed, 'hex');
    const master = hdkey.fromMasterSeed(seedBuffer);
    const path = `m/44'/194'/0'/0/${index}`;
    const node = master.derive(path);

    const privateKeyBuffer = node.privateKey;
    if (!privateKeyBuffer) {
        throw new Error('无法派生私钥');
    }

    const privateKeyWif = wif.encode({
        version: 128,
        privateKey: privateKeyBuffer,
        compressed: false
    });

    const privateKey = PrivateKey.from(privateKeyWif);
    const publicKey = PublicKey.from(privateKey.toPublic());

    return {
        privateKey: privateKeyWif,
        publicKey: publicKey.toString(),
    };
}

interface VaultaTransactionResponse {
    signatures: string[];
    compression: boolean;
    packed_context_free_data: string;
    packed_trx: string;
}

/**
 * Create account with offline signing
 */
export function createAccount(
    creatorPrivateKey: string,
    creatorAccountName: string,
    newAccountName: string,
    ownerPublicKey: string,
    activePublicKey: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(newAccountName)) {
        throw new Error(`账户名 "${newAccountName}" 不符合Vaulta格式要求。必须符合正则表达式: (^[a-z1-5.]{1,11}[a-z1-5]$)|(^[a-z1-5.]{12}[a-j1-5]$)`);
    }

    const privateKey = PrivateKey.from(creatorPrivateKey);

    const newAccountData = {
        creator: creatorAccountName,
        name: newAccountName,
        owner: {
            threshold: 1,
            keys: [{
                key: ownerPublicKey,
                weight: 1,
            }],
            accounts: [],
            waits: [],
        },
        active: {
            threshold: 1,
            keys: [{
                key: activePublicKey,
                weight: 1,
            }],
            accounts: [],
            waits: [],
        },
    };

    const encodedData = Serializer.encode({
        object: newAccountData,
        abi: VAULTA_ABI,
        type: "newaccount"
    });

    const createAccountAction = Action.from({
        account: 'core.vaulta',
        name: 'newaccount',
        authorization: [{
            actor: creatorAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [createAccountAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;
    
    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * Buy RAM by bytes with offline signing
 */
export function buyRamBytes(
    payerPrivateKey: string,
    payerAccountName: string,
    receiverAccountName: string,
    bytes: number,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(payerAccountName)) {
        throw new Error(`付款者账户名 "${payerAccountName}" 不符合Vaulta格式要求`);
    }

    if (!validateAccountName(receiverAccountName)) {
        throw new Error(`接收者账户名 "${receiverAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(payerPrivateKey);

    const buyRamData = {
        payer: payerAccountName,
        receiver: receiverAccountName,
        bytes: bytes
    };

    const buyRamBytesABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "buyrambytes",
                base: "",
                fields: [
                    { name: "payer", type: "name" },
                    { name: "receiver", type: "name" },
                    { name: "bytes", type: "uint32" }
                ]
            }
        ],
        actions: [
            {
                name: "buyrambytes",
                type: "buyrambytes",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: buyRamData,
        abi: buyRamBytesABI,
        type: "buyrambytes"
    });

    const buyRamAction = Action.from({
        account: 'core.vaulta',
        name: 'buyrambytes',
        authorization: [{
            actor: payerAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [buyRamAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;
    
    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * Buy RAM by token amount with offline signing
 */
export function buyRam(
    payerPrivateKey: string,
    payerAccountName: string,
    receiverAccountName: string,
    amount: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(payerAccountName)) {
        throw new Error(`付款者账户名 "${payerAccountName}" 不符合Vaulta格式要求`);
    }

    if (!validateAccountName(receiverAccountName)) {
        throw new Error(`接收者账户名 "${receiverAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(payerPrivateKey);

    const buyRamData = {
        payer: payerAccountName,
        receiver: receiverAccountName,
        quant: amount
    };

    const buyRamABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "buyram",
                base: "",
                fields: [
                    { name: "payer", type: "name" },
                    { name: "receiver", type: "name" },
                    { name: "quant", type: "asset" }
                ]
            }
        ],
        actions: [
            {
                name: "buyram",
                type: "buyram",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: buyRamData,
        abi: buyRamABI,
        type: "buyram"
    });

    const buyRamAction = Action.from({
        account: 'core.vaulta',
        name: 'buyram',
        authorization: [{
            actor: payerAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [buyRamAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * Swap EOS tokens to A tokens with offline signing
 */
export function swapEosToA(
    senderPrivateKey: string,
    senderAccountName: string,
    amount: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(senderAccountName)) {
        throw new Error(`发送者账户名 "${senderAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(senderPrivateKey);

    const transferData = {
        from: senderAccountName,
        to: "swap.vaulta",
        quantity: amount,
        memo: "swap"
    };

    const encodedData = Serializer.encode({
        object: transferData,
        abi: VAULTA_ABI,
        type: "transfer"
    });

    const transferAction = Action.from({
        account: 'core.vaulta',
        name: 'transfer',
        authorization: [{
            actor: senderAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [transferAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * Swap A tokens to EOS tokens with offline signing
 */
export function swapAToEos(
    senderPrivateKey: string,
    senderAccountName: string,
    amount: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(senderAccountName)) {
        throw new Error(`发送者账户名 "${senderAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(senderPrivateKey);

    const transferData = {
        from: senderAccountName,
        to: "swap.vaulta",
        quantity: amount,
        memo: "swap"
    };

    const encodedData = Serializer.encode({
        object: transferData,
        abi: VAULTA_ABI,
        type: "transfer"
    });

    const transferAction = Action.from({
        account: 'core.vaulta',
        name: 'transfer',
        authorization: [{
            actor: senderAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [transferAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * Buy RAM for self with offline signing
 */
export function buyRamSelf(
    payerPrivateKey: string,
    payerAccountName: string,
    amount: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(payerAccountName)) {
        throw new Error(`付款者账户名 "${payerAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(payerPrivateKey);

    const buyRamSelfData = {
        payer: payerAccountName,
        quant: amount
    };

    const buyRamSelfABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "buyramself",
                base: "",
                fields: [
                    { name: "payer", type: "name" },
                    { name: "quant", type: "asset" }
                ]
            }
        ],
        actions: [
            {
                name: "buyramself",
                type: "buyramself",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: buyRamSelfData,
        abi: buyRamSelfABI,
        type: "buyramself"
    });

    const buyRamSelfAction = Action.from({
        account: 'core.vaulta',
        name: 'buyramself',
        authorization: [{
            actor: payerAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [buyRamSelfAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * Delegate bandwidth resources with offline signing
 */
export function delegateBw(
    fromPrivateKey: string,
    fromAccountName: string,
    receiverAccountName: string,
    stakeNetQuantity: string,
    stakeCpuQuantity: string,
    transfer: boolean,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(fromAccountName)) {
        throw new Error(`抵押者账户名 "${fromAccountName}" 不符合Vaulta格式要求`);
    }

    if (!validateAccountName(receiverAccountName)) {
        throw new Error(`接收者账户名 "${receiverAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(fromPrivateKey);

    const delegateBwData = {
        from: fromAccountName,
        receiver: receiverAccountName,
        stake_net_quantity: stakeNetQuantity,
        stake_cpu_quantity: stakeCpuQuantity,
        transfer: transfer
    };

    const delegateBwABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "delegatebw",
                base: "",
                fields: [
                    { name: "from", type: "name" },
                    { name: "receiver", type: "name" },
                    { name: "stake_net_quantity", type: "asset" },
                    { name: "stake_cpu_quantity", type: "asset" },
                    { name: "transfer", type: "bool" }
                ]
            }
        ],
        actions: [
            {
                name: "delegatebw",
                type: "delegatebw",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: delegateBwData,
        abi: delegateBwABI,
        type: "delegatebw"
    });

    const delegateBwAction = Action.from({
        account: 'core.vaulta',
        name: 'delegatebw',
        authorization: [{
            actor: fromAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [delegateBwAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * PowerUp temporary resource rental with offline signing
 */
export function powerUp(
    payerPrivateKey: string,
    payerAccountName: string,
    receiverAccountName: string,
    days: number,
    netFrac: number,
    cpuFrac: number,
    maxPayment: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(payerAccountName)) {
        throw new Error(`付款者账户名 "${payerAccountName}" 不符合Vaulta格式要求`);
    }

    if (!validateAccountName(receiverAccountName)) {
        throw new Error(`接收者账户名 "${receiverAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(payerPrivateKey);

    const powerUpData = {
        payer: payerAccountName,
        receiver: receiverAccountName,
        days: days,
        net_frac: netFrac,
        cpu_frac: cpuFrac,
        max_payment: maxPayment
    };

    const powerUpABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "powerup",
                base: "",
                fields: [
                    { name: "payer", type: "name" },
                    { name: "receiver", type: "name" },
                    { name: "days", type: "uint32" },
                    { name: "net_frac", type: "int64" },
                    { name: "cpu_frac", type: "int64" },
                    { name: "max_payment", type: "asset" }
                ]
            }
        ],
        actions: [
            {
                name: "powerup",
                type: "powerup",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: powerUpData,
        abi: powerUpABI,
        type: "powerup"
    });

    const powerUpAction = Action.from({
        account: 'core.vaulta',
        name: 'powerup',
        authorization: [{
            actor: payerAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [powerUpAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * Create account with RAM and resources with offline signing
 */
export function createAccountWithResources(
    creatorPrivateKey: string,
    creatorAccountName: string,
    newAccountName: string,
    ownerPublicKey: string,
    activePublicKey: string,
    ramAmount: string,
    netStake: string,
    cpuStake: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(creatorAccountName)) {
        throw new Error(`创建者账户名 "${creatorAccountName}" 不符合Vaulta格式要求`);
    }

    if (!validateAccountName(newAccountName)) {
        throw new Error(`新账户名 "${newAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(creatorPrivateKey);

    const newAccountData = {
        creator: creatorAccountName,
        name: newAccountName,
        owner: {
            threshold: 1,
            keys: [{
                key: ownerPublicKey,
                weight: 1
            }],
            accounts: [],
            waits: []
        },
        active: {
            threshold: 1,
            keys: [{
                key: activePublicKey,
                weight: 1
            }],
            accounts: [],
            waits: []
        }
    };

    const buyRamData = {
        payer: creatorAccountName,
        receiver: newAccountName,
        quant: ramAmount
    };

    const delegateBwData = {
        from: creatorAccountName,
        receiver: newAccountName,
        stake_net_quantity: netStake,
        stake_cpu_quantity: cpuStake,
        transfer: true
    };

    const encodedNewAccount = Serializer.encode({
        object: newAccountData,
        abi: VAULTA_ABI,
        type: "newaccount"
    });

    const encodedBuyRam = Serializer.encode({
        object: buyRamData,
        abi: VAULTA_ABI,
        type: "buyram"
    });

    const encodedDelegateBw = Serializer.encode({
        object: delegateBwData,
        abi: VAULTA_ABI,
        type: "delegatebw"
    });

    const actions = [
        Action.from({
            account: 'core.vaulta',
            name: 'newaccount',
            authorization: [{
                actor: creatorAccountName,
                permission: 'active',
            }],
            data: encodedNewAccount,
        }),
        Action.from({
            account: 'core.vaulta',
            name: 'buyram',
            authorization: [{
                actor: creatorAccountName,
                permission: 'active',
            }],
            data: encodedBuyRam,
        }),
        Action.from({
            account: 'core.vaulta',
            name: 'delegatebw',
            authorization: [{
                actor: creatorAccountName,
                permission: 'active',
            }],
            data: encodedDelegateBw,
        })
    ];

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: actions,
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * Transfer tokens with offline signing
 */
export function transfer(
    fromPrivateKey: string,
    fromAccountName: string,
    toAccountName: string,
    quantity: string,
    memo: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(fromAccountName)) {
        throw new Error(`发送者账户名 "${fromAccountName}" 不符合Vaulta格式要求`);
    }

    if (!validateAccountName(toAccountName)) {
        throw new Error(`接收者账户名 "${toAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(fromPrivateKey);

    const transferData = {
        from: fromAccountName,
        to: toAccountName,
        quantity: quantity,
        memo: memo
    };

    const transferABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "transfer",
                base: "",
                fields: [
                    { name: "from", type: "name" },
                    { name: "to", type: "name" },
                    { name: "quantity", type: "asset" },
                    { name: "memo", type: "string" }
                ]
            }
        ],
        actions: [
            {
                name: "transfer",
                type: "transfer",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: transferData,
        abi: transferABI,
        type: "transfer"
    });

    const transferAction = Action.from({
        account: 'core.vaulta',
        name: 'transfer',
        authorization: [{
            actor: fromAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [transferAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}
