/**
 * Vaulta 区块链 TypeScript SDK
 *
 * 这个模块提供了与 Vaulta 区块链交互的完整功能集，包括：
 * - 密钥对生成和管理（基于 BIP44 标准）
 * - 账户创建和管理
 * - 资源管理（RAM 购买、带宽委托、PowerUp 租赁）
 * - 代币转账和交换
 * - 离线交易签名
 *
 * 所有交易都支持离线签名，无需连接到区块链网络即可构建和签名交易。
 * 签名后的交易可以通过其他方式（如 API）提交到 Vaulta 网络。
 *
 * <AUTHOR>
 * @version 1.0.0
 * @license MIT
 */

// ===== 依赖导入 =====

// HD 钱包密钥派生库，用于从种子生成分层确定性密钥
import * as hdkey from 'hdkey';

// 钱包导入格式库，用于私钥的 WIF 格式编码
import * as wif from 'wif';

// Antelope (EOS) 区块链 SDK，提供密钥管理、交易构建和签名功能
import { PrivateKey, PublicKey, APIClient, Action, Transaction, SignedTransaction, Bytes, Serializer, Name, Authority, ABI, Struct } from '@wharfkit/antelope';

// Node.js 内置模块
import * as fs from 'fs';    // 文件系统操作
import * as path from 'path'; // 路径处理

/**
 * Vaulta 区块链网络的链 ID (Chain ID)
 *
 * 这是 Vaulta 区块链网络的唯一标识符，用于：
 * 1. 确保交易只能在正确的区块链网络上执行
 * 2. 防止重放攻击（replay attacks）
 * 3. 在交易签名过程中作为签名摘要的一部分
 *
 * 格式：64位十六进制字符串（256位哈希值）
 * 网络：Vaulta 主网
 *
 * @constant {string} VAULTA_CHAIN_ID - Vaulta 区块链的链标识符
 */
const VAULTA_CHAIN_ID = "aca376f206b8fc25a6ed44dbdc66547c36c6c33e3a119ffbeaef943642f0e906";

// ===== 区块链配置常量 =====

/**
 * Vaulta 智能合约 ABI (Application Binary Interface) 文件路径
 * ABI 定义了智能合约的接口，包括函数签名、参数类型等
 */
const abiPath = path.join(__dirname, 'abi.json');

/**
 * Vaulta 智能合约的 ABI 对象
 * 用于序列化和反序列化智能合约调用的数据
 *
 * @constant {ABI} VAULTA_ABI - 从 abi.json 文件加载的 ABI 定义
 */
const VAULTA_ABI = ABI.from(JSON.parse(fs.readFileSync(abiPath, 'utf8')));

// ===== 工具函数 =====

/**
 * 验证 Vaulta 账户名格式是否符合规范
 *
 * Vaulta 账户名规则：
 * 1. 长度为 1-12 个字符
 * 2. 只能包含小写字母 a-z、数字 1-5 和点号 .
 * 3. 不能以点号开头或结尾
 * 4. 不能包含连续的点号
 * 5. 如果是 12 位，最后一位只能是 a-j 或 1-5
 *
 * @param {string} accountName - 要验证的账户名
 * @returns {boolean} 如果账户名格式正确返回 true，否则返回 false
 *
 * @example
 * validateAccountName("alice"); // true
 * validateAccountName("bob.test"); // true
 * validateAccountName("invalid.name."); // false (以点号结尾)
 * validateAccountName("toolongname123"); // false (超过12位)
 */
function validateAccountName(accountName: string): boolean {
    // 正则表达式：匹配 1-11 位字符 + 最后一位 a-z1-5，或者 12 位字符 + 最后一位 a-j1-5
    const regex = /^([a-z1-5.]{1,11}[a-z1-5]$)|([a-z1-5.]{12}[a-j1-5]$)/;

    // 检查是否以点号开头、结尾或包含连续点号
    if (accountName.startsWith('.') || accountName.endsWith('.') || accountName.includes('..')) {
        return false;
    }

    // 使用正则表达式验证格式
    return regex.test(accountName);
}

// ===== 密钥管理函数 =====

/**
 * 使用 BIP44 标准从种子生成密钥对
 *
 * BIP44 是一个分层确定性钱包的标准，允许从单个种子生成多个密钥对。
 * 使用的派生路径为：m/44'/194'/0'/0/{index}
 * 其中 194 是 EOS 的币种代码
 *
 * @param {string} seed - 十六进制格式的种子字符串
 * @param {number} [index=0] - 密钥索引，用于生成不同的密钥对
 * @returns {Object} 包含私钥和公钥的对象
 * @returns {string} returns.privateKey - WIF 格式的私钥
 * @returns {string} returns.publicKey - 公钥字符串
 *
 * @throws {Error} 当无法派生私钥时抛出错误
 *
 * @example
 * const keyPair = createKeyPair("a1b2c3d4...", 0);
 * console.log(keyPair.privateKey); // "5K..."
 * console.log(keyPair.publicKey);  // "EOS..."
 */
export function createKeyPair(seed: string, index: number = 0): {
    privateKey: string;
    publicKey: string;
} {
    // 将十六进制种子转换为 Buffer
    const seedBuffer = Buffer.from(seed, 'hex');

    // 从种子创建主密钥
    const master = hdkey.fromMasterSeed(seedBuffer);

    // BIP44 派生路径：m/44'/194'/0'/0/{index}
    // 44' = BIP44 标准
    // 194' = EOS 币种代码
    // 0' = 账户索引
    // 0 = 外部链（用于接收地址）
    // index = 地址索引
    const path = `m/44'/194'/0'/0/${index}`;
    const node = master.derive(path);

    // 获取派生的私钥
    const privateKeyBuffer = node.privateKey;
    if (!privateKeyBuffer) {
        throw new Error('无法派生私钥');
    }

    // 将私钥编码为 WIF (Wallet Import Format) 格式
    const privateKeyWif = wif.encode({
        version: 128,        // Bitcoin 主网版本号
        privateKey: privateKeyBuffer,
        compressed: false    // 使用未压缩格式
    });

    // 创建 Antelope 格式的私钥和公钥对象
    const privateKey = PrivateKey.from(privateKeyWif);
    const publicKey = PublicKey.from(privateKey.toPublic());

    return {
        privateKey: privateKeyWif,
        publicKey: publicKey.toString(),
    };
}

// ===== 类型定义 =====

/**
 * Vaulta 交易响应接口
 * 定义了离线签名交易后返回的数据结构
 *
 * @interface VaultaTransactionResponse
 */
interface VaultaTransactionResponse {
    /** 交易签名数组，包含用私钥对交易摘要的签名 */
    signatures: string[];

    /** 是否使用压缩格式（当前固定为 false） */
    compression: boolean;

    /** 打包的上下文无关数据（通常为空字符串） */
    packed_context_free_data: string;

    /** 打包的交易数据，十六进制格式的序列化交易 */
    packed_trx: string;
}

// ===== 账户管理函数 =====

/**
 * 使用离线签名创建新的 Vaulta 账户
 *
 * 此函数创建一个新账户的交易，包括设置 owner 和 active 权限。
 * 交易在本地签名，无需连接到区块链网络。
 *
 * @param {string} creatorPrivateKey - 创建者的私钥（WIF 格式）
 * @param {string} creatorAccountName - 创建者的账户名
 * @param {string} newAccountName - 新账户的名称
 * @param {string} ownerPublicKey - 新账户的 owner 权限公钥
 * @param {string} activePublicKey - 新账户的 active 权限公钥
 * @param {number} refBlockNum - 参考区块号，用于交易有效性验证
 * @param {number} refBlockPrefix - 参考区块前缀，用于交易有效性验证
 * @param {number} [expiration] - 交易过期时间戳（秒），默认为当前时间 + 1小时
 * @returns {VaultaTransactionResponse} 签名后的交易数据
 *
 * @throws {Error} 当新账户名格式不正确时抛出错误
 *
 * @example
 * const result = createAccount(
 *   "5K...", // 创建者私钥
 *   "creator", // 创建者账户名
 *   "newuser", // 新账户名
 *   "EOS...", // owner 公钥
 *   "EOS...", // active 公钥
 *   12345, // 参考区块号
 *   67890  // 参考区块前缀
 * );
 */
export function createAccount(
    creatorPrivateKey: string,
    creatorAccountName: string,
    newAccountName: string,
    ownerPublicKey: string,
    activePublicKey: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    // 验证新账户名格式
    if (!validateAccountName(newAccountName)) {
        throw new Error(`账户名 "${newAccountName}" 不符合Vaulta格式要求。必须符合正则表达式: (^[a-z1-5.]{1,11}[a-z1-5]$)|(^[a-z1-5.]{12}[a-j1-5]$)`);
    }

    // 从 WIF 格式创建私钥对象
    const privateKey = PrivateKey.from(creatorPrivateKey);

    // 构建新账户数据结构
    const newAccountData = {
        creator: creatorAccountName,  // 创建者账户名
        name: newAccountName,         // 新账户名

        // owner 权限配置（最高权限，用于账户管理）
        owner: {
            threshold: 1,             // 权限阈值：需要权重总和 >= 1 才能执行操作
            keys: [{                  // 公钥列表
                key: ownerPublicKey,  // owner 权限的公钥
                weight: 1,            // 该公钥的权重
            }],
            accounts: [],             // 账户权限列表（空）
            waits: [],               // 延迟权限列表（空）
        },

        // active 权限配置（日常操作权限）
        active: {
            threshold: 1,             // 权限阈值
            keys: [{                  // 公钥列表
                key: activePublicKey, // active 权限的公钥
                weight: 1,            // 该公钥的权重
            }],
            accounts: [],             // 账户权限列表（空）
            waits: [],               // 延迟权限列表（空）
        },
    };

    // 使用 ABI 序列化账户数据
    const encodedData = Serializer.encode({
        object: newAccountData,    // 要序列化的数据对象
        abi: VAULTA_ABI,          // 使用的 ABI 定义
        type: "newaccount"        // 数据类型名称
    });

    // 创建账户操作（Action）
    const createAccountAction = Action.from({
        account: 'core.vaulta',   // 目标智能合约账户
        name: 'newaccount',       // 调用的合约方法名
        authorization: [{         // 授权信息
            actor: creatorAccountName,  // 执行者账户名
            permission: 'active',       // 使用的权限级别
        }],
        data: encodedData,        // 序列化后的操作数据
    });

    // 交易头部信息
    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1), // 过期时间（ISO格式）
        ref_block_num: refBlockNum,        // 参考区块号（用于防重放攻击）
        ref_block_prefix: refBlockPrefix,  // 参考区块前缀
        max_net_usage_words: 0,            // 最大网络使用量（0表示无限制）
        max_cpu_usage_ms: 0,               // 最大CPU使用时间（0表示无限制）
        delay_sec: 0,                      // 延迟执行秒数
    };

    // 构建完整交易对象
    const transaction = Transaction.from({
        ...transactionHeader,              // 展开交易头部信息
        actions: [createAccountAction],    // 包含的操作列表
    });

    // 生成交易签名摘要（包含链ID以防止重放攻击）
    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);

    // 使用私钥对摘要进行签名
    const signature = privateKey.signDigest(digest);

    // 创建已签名的交易对象
    const signedTransaction = SignedTransaction.from({
        ...transaction,           // 展开原始交易
        signatures: [signature],  // 添加签名
    });

    // 序列化交易为十六进制字符串
    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    // 上下文无关数据（通常为空）
    const packedContextFreeData = "";

    // 返回交易响应数据
    return {
        signatures: [signature.toString()],           // 签名数组
        compression: false,                           // 压缩标志
        packed_context_free_data: packedContextFreeData, // 打包的上下文无关数据
        packed_trx: packedTrx                        // 打包的交易数据
    };
}

// ===== 资源管理函数 =====

/**
 * 使用离线签名按字节数购买 RAM
 *
 * RAM 是 Vaulta 区块链上存储数据所需的资源。此函数允许为指定账户购买特定字节数的 RAM。
 * 购买的 RAM 将分配给接收者账户，用于存储智能合约状态、账户数据等。
 *
 * @param {string} payerPrivateKey - 付款者的私钥（WIF 格式）
 * @param {string} payerAccountName - 付款者的账户名
 * @param {string} receiverAccountName - 接收 RAM 的账户名
 * @param {number} bytes - 要购买的 RAM 字节数
 * @param {number} refBlockNum - 参考区块号
 * @param {number} refBlockPrefix - 参考区块前缀
 * @param {number} [expiration] - 交易过期时间戳（秒），默认为当前时间 + 1小时
 * @returns {VaultaTransactionResponse} 签名后的交易数据
 *
 * @throws {Error} 当付款者或接收者账户名格式不正确时抛出错误
 *
 * @example
 * const result = buyRamBytes(
 *   "5K...", // 付款者私钥
 *   "payer", // 付款者账户名
 *   "receiver", // 接收者账户名
 *   4096, // 购买 4KB RAM
 *   12345, // 参考区块号
 *   67890  // 参考区块前缀
 * );
 */
export function buyRamBytes(
    payerPrivateKey: string,
    payerAccountName: string,
    receiverAccountName: string,
    bytes: number,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    // 验证付款者账户名格式
    if (!validateAccountName(payerAccountName)) {
        throw new Error(`付款者账户名 "${payerAccountName}" 不符合Vaulta格式要求`);
    }

    // 验证接收者账户名格式
    if (!validateAccountName(receiverAccountName)) {
        throw new Error(`接收者账户名 "${receiverAccountName}" 不符合Vaulta格式要求`);
    }

    // 从 WIF 格式创建私钥对象
    const privateKey = PrivateKey.from(payerPrivateKey);

    const buyRamData = {
        payer: payerAccountName,
        receiver: receiverAccountName,
        bytes: bytes
    };

    const buyRamBytesABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "buyrambytes",
                base: "",
                fields: [
                    { name: "payer", type: "name" },
                    { name: "receiver", type: "name" },
                    { name: "bytes", type: "uint32" }
                ]
            }
        ],
        actions: [
            {
                name: "buyrambytes",
                type: "buyrambytes",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: buyRamData,
        abi: buyRamBytesABI,
        type: "buyrambytes"
    });

    const buyRamAction = Action.from({
        account: 'core.vaulta',
        name: 'buyrambytes',
        authorization: [{
            actor: payerAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [buyRamAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;
    
    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * 使用离线签名按代币数量购买 RAM
 *
 * 与 buyRamBytes 不同，此函数允许指定要花费的代币数量来购买 RAM，
 * 而不是指定具体的字节数。实际获得的 RAM 数量取决于当前的市场价格。
 *
 * @param {string} payerPrivateKey - 付款者的私钥（WIF 格式）
 * @param {string} payerAccountName - 付款者的账户名
 * @param {string} receiverAccountName - 接收 RAM 的账户名
 * @param {string} amount - 要花费的代币数量，格式如 "1.0000 EOS"
 * @param {number} refBlockNum - 参考区块号
 * @param {number} refBlockPrefix - 参考区块前缀
 * @param {number} [expiration] - 交易过期时间戳（秒），默认为当前时间 + 1小时
 * @returns {VaultaTransactionResponse} 签名后的交易数据
 *
 * @throws {Error} 当付款者或接收者账户名格式不正确时抛出错误
 *
 * @example
 * const result = buyRam(
 *   "5K...", // 付款者私钥
 *   "payer", // 付款者账户名
 *   "receiver", // 接收者账户名
 *   "1.0000 EOS", // 花费 1 EOS 购买 RAM
 *   12345, // 参考区块号
 *   67890  // 参考区块前缀
 * );
 */
export function buyRam(
    payerPrivateKey: string,
    payerAccountName: string,
    receiverAccountName: string,
    amount: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    // 验证付款者账户名格式
    if (!validateAccountName(payerAccountName)) {
        throw new Error(`付款者账户名 "${payerAccountName}" 不符合Vaulta格式要求`);
    }

    // 验证接收者账户名格式
    if (!validateAccountName(receiverAccountName)) {
        throw new Error(`接收者账户名 "${receiverAccountName}" 不符合Vaulta格式要求`);
    }

    // 从 WIF 格式创建私钥对象
    const privateKey = PrivateKey.from(payerPrivateKey);

    const buyRamData = {
        payer: payerAccountName,
        receiver: receiverAccountName,
        quant: amount
    };

    const buyRamABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "buyram",
                base: "",
                fields: [
                    { name: "payer", type: "name" },
                    { name: "receiver", type: "name" },
                    { name: "quant", type: "asset" }
                ]
            }
        ],
        actions: [
            {
                name: "buyram",
                type: "buyram",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: buyRamData,
        abi: buyRamABI,
        type: "buyram"
    });

    const buyRamAction = Action.from({
        account: 'core.vaulta',
        name: 'buyram',
        authorization: [{
            actor: payerAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [buyRamAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

// ===== 代币交换函数 =====

/**
 * 使用离线签名将 EOS 代币交换为 A 代币
 *
 * 通过向 swap.vaulta 合约发送 EOS 代币来执行代币交换。
 * 交换比例由智能合约自动计算。
 *
 * @param {string} senderPrivateKey - 发送者的私钥（WIF 格式）
 * @param {string} senderAccountName - 发送者的账户名
 * @param {string} amount - 要交换的 EOS 数量，格式如 "10.0000 EOS"
 * @param {number} refBlockNum - 参考区块号
 * @param {number} refBlockPrefix - 参考区块前缀
 * @param {number} [expiration] - 交易过期时间戳（秒）
 * @returns {VaultaTransactionResponse} 签名后的交易数据
 */
export function swapEosToA(
    senderPrivateKey: string,
    senderAccountName: string,
    amount: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(senderAccountName)) {
        throw new Error(`发送者账户名 "${senderAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(senderPrivateKey);

    const transferData = {
        from: senderAccountName,
        to: "swap.vaulta",
        quantity: amount,
        memo: "swap"
    };

    const encodedData = Serializer.encode({
        object: transferData,
        abi: VAULTA_ABI,
        type: "transfer"
    });

    const transferAction = Action.from({
        account: 'core.vaulta',
        name: 'transfer',
        authorization: [{
            actor: senderAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [transferAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * 使用离线签名将 A 代币交换为 EOS 代币
 *
 * 通过向 swap.vaulta 合约发送 A 代币来执行代币交换。
 * 交换比例由智能合约自动计算。
 *
 * @param {string} senderPrivateKey - 发送者的私钥（WIF 格式）
 * @param {string} senderAccountName - 发送者的账户名
 * @param {string} amount - 要交换的 A 代币数量，格式如 "10.0000 A"
 * @param {number} refBlockNum - 参考区块号
 * @param {number} refBlockPrefix - 参考区块前缀
 * @param {number} [expiration] - 交易过期时间戳（秒）
 * @returns {VaultaTransactionResponse} 签名后的交易数据
 */
export function swapAToEos(
    senderPrivateKey: string,
    senderAccountName: string,
    amount: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(senderAccountName)) {
        throw new Error(`发送者账户名 "${senderAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(senderPrivateKey);

    const transferData = {
        from: senderAccountName,
        to: "swap.vaulta",
        quantity: amount,
        memo: "swap"
    };

    const encodedData = Serializer.encode({
        object: transferData,
        abi: VAULTA_ABI,
        type: "transfer"
    });

    const transferAction = Action.from({
        account: 'core.vaulta',
        name: 'transfer',
        authorization: [{
            actor: senderAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [transferAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * Buy RAM for self with offline signing
 */
export function buyRamSelf(
    payerPrivateKey: string,
    payerAccountName: string,
    amount: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(payerAccountName)) {
        throw new Error(`付款者账户名 "${payerAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(payerPrivateKey);

    const buyRamSelfData = {
        payer: payerAccountName,
        quant: amount
    };

    const buyRamSelfABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "buyramself",
                base: "",
                fields: [
                    { name: "payer", type: "name" },
                    { name: "quant", type: "asset" }
                ]
            }
        ],
        actions: [
            {
                name: "buyramself",
                type: "buyramself",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: buyRamSelfData,
        abi: buyRamSelfABI,
        type: "buyramself"
    });

    const buyRamSelfAction = Action.from({
        account: 'core.vaulta',
        name: 'buyramself',
        authorization: [{
            actor: payerAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [buyRamSelfAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * 使用离线签名委托带宽资源
 *
 * 将 EOS 代币抵押为网络带宽（NET）和 CPU 资源，分配给指定账户使用。
 * 可以选择是否将资源所有权转移给接收者。
 *
 * @param {string} fromPrivateKey - 委托者的私钥（WIF 格式）
 * @param {string} fromAccountName - 委托者的账户名
 * @param {string} receiverAccountName - 接收资源的账户名
 * @param {string} stakeNetQuantity - 抵押给网络带宽的代币数量，格式如 "1.0000 EOS"
 * @param {string} stakeCpuQuantity - 抵押给 CPU 的代币数量，格式如 "1.0000 EOS"
 * @param {boolean} transfer - 是否转移资源所有权给接收者
 * @param {number} refBlockNum - 参考区块号
 * @param {number} refBlockPrefix - 参考区块前缀
 * @param {number} [expiration] - 交易过期时间戳（秒）
 * @returns {VaultaTransactionResponse} 签名后的交易数据
 */
export function delegateBw(
    fromPrivateKey: string,
    fromAccountName: string,
    receiverAccountName: string,
    stakeNetQuantity: string,
    stakeCpuQuantity: string,
    transfer: boolean,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(fromAccountName)) {
        throw new Error(`抵押者账户名 "${fromAccountName}" 不符合Vaulta格式要求`);
    }

    if (!validateAccountName(receiverAccountName)) {
        throw new Error(`接收者账户名 "${receiverAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(fromPrivateKey);

    const delegateBwData = {
        from: fromAccountName,
        receiver: receiverAccountName,
        stake_net_quantity: stakeNetQuantity,
        stake_cpu_quantity: stakeCpuQuantity,
        transfer: transfer
    };

    const delegateBwABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "delegatebw",
                base: "",
                fields: [
                    { name: "from", type: "name" },
                    { name: "receiver", type: "name" },
                    { name: "stake_net_quantity", type: "asset" },
                    { name: "stake_cpu_quantity", type: "asset" },
                    { name: "transfer", type: "bool" }
                ]
            }
        ],
        actions: [
            {
                name: "delegatebw",
                type: "delegatebw",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: delegateBwData,
        abi: delegateBwABI,
        type: "delegatebw"
    });

    const delegateBwAction = Action.from({
        account: 'core.vaulta',
        name: 'delegatebw',
        authorization: [{
            actor: fromAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [delegateBwAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * 使用离线签名进行 PowerUp 临时资源租赁
 *
 * PowerUp 是一种临时租赁系统资源的机制，允许用户支付少量费用
 * 来获得短期的网络带宽和 CPU 资源，而无需长期抵押代币。
 *
 * @param {string} payerPrivateKey - 付款者的私钥（WIF 格式）
 * @param {string} payerAccountName - 付款者的账户名
 * @param {string} receiverAccountName - 接收资源的账户名
 * @param {number} days - 租赁天数
 * @param {number} netFrac - 网络带宽资源份额（微秒级别）
 * @param {number} cpuFrac - CPU 资源份额（微秒级别）
 * @param {string} maxPayment - 最大支付金额，格式如 "1.0000 EOS"
 * @param {number} refBlockNum - 参考区块号
 * @param {number} refBlockPrefix - 参考区块前缀
 * @param {number} [expiration] - 交易过期时间戳（秒）
 * @returns {VaultaTransactionResponse} 签名后的交易数据
 */
export function powerUp(
    payerPrivateKey: string,
    payerAccountName: string,
    receiverAccountName: string,
    days: number,
    netFrac: number,
    cpuFrac: number,
    maxPayment: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(payerAccountName)) {
        throw new Error(`付款者账户名 "${payerAccountName}" 不符合Vaulta格式要求`);
    }

    if (!validateAccountName(receiverAccountName)) {
        throw new Error(`接收者账户名 "${receiverAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(payerPrivateKey);

    const powerUpData = {
        payer: payerAccountName,
        receiver: receiverAccountName,
        days: days,
        net_frac: netFrac,
        cpu_frac: cpuFrac,
        max_payment: maxPayment
    };

    const powerUpABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "powerup",
                base: "",
                fields: [
                    { name: "payer", type: "name" },
                    { name: "receiver", type: "name" },
                    { name: "days", type: "uint32" },
                    { name: "net_frac", type: "int64" },
                    { name: "cpu_frac", type: "int64" },
                    { name: "max_payment", type: "asset" }
                ]
            }
        ],
        actions: [
            {
                name: "powerup",
                type: "powerup",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: powerUpData,
        abi: powerUpABI,
        type: "powerup"
    });

    const powerUpAction = Action.from({
        account: 'core.vaulta',
        name: 'powerup',
        authorization: [{
            actor: payerAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [powerUpAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

/**
 * 使用离线签名创建账户并同时分配 RAM 和资源
 *
 * 这是一个便捷函数，在单个交易中完成三个操作：
 * 1. 创建新账户
 * 2. 为新账户购买 RAM
 * 3. 为新账户委托网络带宽和 CPU 资源
 *
 * @param {string} creatorPrivateKey - 创建者的私钥（WIF 格式）
 * @param {string} creatorAccountName - 创建者的账户名
 * @param {string} newAccountName - 新账户的名称
 * @param {string} ownerPublicKey - 新账户的 owner 权限公钥
 * @param {string} activePublicKey - 新账户的 active 权限公钥
 * @param {string} ramAmount - 购买的 RAM 数量，格式如 "1.0000 EOS"
 * @param {string} netStake - 网络带宽抵押数量，格式如 "1.0000 EOS"
 * @param {string} cpuStake - CPU 资源抵押数量，格式如 "1.0000 EOS"
 * @param {number} refBlockNum - 参考区块号
 * @param {number} refBlockPrefix - 参考区块前缀
 * @param {number} [expiration] - 交易过期时间戳（秒）
 * @returns {VaultaTransactionResponse} 签名后的交易数据
 */
export function createAccountWithResources(
    creatorPrivateKey: string,
    creatorAccountName: string,
    newAccountName: string,
    ownerPublicKey: string,
    activePublicKey: string,
    ramAmount: string,
    netStake: string,
    cpuStake: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    if (!validateAccountName(creatorAccountName)) {
        throw new Error(`创建者账户名 "${creatorAccountName}" 不符合Vaulta格式要求`);
    }

    if (!validateAccountName(newAccountName)) {
        throw new Error(`新账户名 "${newAccountName}" 不符合Vaulta格式要求`);
    }

    const privateKey = PrivateKey.from(creatorPrivateKey);

    const newAccountData = {
        creator: creatorAccountName,
        name: newAccountName,
        owner: {
            threshold: 1,
            keys: [{
                key: ownerPublicKey,
                weight: 1
            }],
            accounts: [],
            waits: []
        },
        active: {
            threshold: 1,
            keys: [{
                key: activePublicKey,
                weight: 1
            }],
            accounts: [],
            waits: []
        }
    };

    const buyRamData = {
        payer: creatorAccountName,
        receiver: newAccountName,
        quant: ramAmount
    };

    const delegateBwData = {
        from: creatorAccountName,
        receiver: newAccountName,
        stake_net_quantity: netStake,
        stake_cpu_quantity: cpuStake,
        transfer: true
    };

    const encodedNewAccount = Serializer.encode({
        object: newAccountData,
        abi: VAULTA_ABI,
        type: "newaccount"
    });

    const encodedBuyRam = Serializer.encode({
        object: buyRamData,
        abi: VAULTA_ABI,
        type: "buyram"
    });

    const encodedDelegateBw = Serializer.encode({
        object: delegateBwData,
        abi: VAULTA_ABI,
        type: "delegatebw"
    });

    const actions = [
        Action.from({
            account: 'core.vaulta',
            name: 'newaccount',
            authorization: [{
                actor: creatorAccountName,
                permission: 'active',
            }],
            data: encodedNewAccount,
        }),
        Action.from({
            account: 'core.vaulta',
            name: 'buyram',
            authorization: [{
                actor: creatorAccountName,
                permission: 'active',
            }],
            data: encodedBuyRam,
        }),
        Action.from({
            account: 'core.vaulta',
            name: 'delegatebw',
            authorization: [{
                actor: creatorAccountName,
                permission: 'active',
            }],
            data: encodedDelegateBw,
        })
    ];

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: actions,
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}

// ===== 代币转账函数 =====

/**
 * 使用离线签名进行代币转账
 *
 * 此函数创建一个代币转账交易，将指定数量的代币从发送者账户转移到接收者账户。
 * 支持添加备注信息，交易在本地签名完成。
 *
 * @param {string} fromPrivateKey - 发送者的私钥（WIF 格式）
 * @param {string} fromAccountName - 发送者的账户名
 * @param {string} toAccountName - 接收者的账户名
 * @param {string} quantity - 转账数量和币种，格式如 "10.0000 EOS"
 * @param {string} memo - 转账备注信息
 * @param {number} refBlockNum - 参考区块号
 * @param {number} refBlockPrefix - 参考区块前缀
 * @param {number} [expiration] - 交易过期时间戳（秒），默认为当前时间 + 1小时
 * @returns {VaultaTransactionResponse} 签名后的交易数据
 *
 * @throws {Error} 当发送者或接收者账户名格式不正确时抛出错误
 *
 * @example
 * const result = transfer(
 *   "5K...", // 发送者私钥
 *   "alice", // 发送者账户名
 *   "bob", // 接收者账户名
 *   "10.0000 EOS", // 转账金额
 *   "Payment for services", // 备注
 *   12345, // 参考区块号
 *   67890  // 参考区块前缀
 * );
 */
export function transfer(
    fromPrivateKey: string,
    fromAccountName: string,
    toAccountName: string,
    quantity: string,
    memo: string,
    refBlockNum: number,
    refBlockPrefix: number,
    expiration: number = Math.floor(Date.now() / 1000) + 3600
): VaultaTransactionResponse {

    // 验证发送者账户名格式
    if (!validateAccountName(fromAccountName)) {
        throw new Error(`发送者账户名 "${fromAccountName}" 不符合Vaulta格式要求`);
    }

    // 验证接收者账户名格式
    if (!validateAccountName(toAccountName)) {
        throw new Error(`接收者账户名 "${toAccountName}" 不符合Vaulta格式要求`);
    }

    // 从 WIF 格式创建私钥对象
    const privateKey = PrivateKey.from(fromPrivateKey);

    const transferData = {
        from: fromAccountName,
        to: toAccountName,
        quantity: quantity,
        memo: memo
    };

    const transferABI = ABI.from({
        version: "eosio::abi/1.2",
        types: [],
        variants: [],
        structs: [
            {
                name: "transfer",
                base: "",
                fields: [
                    { name: "from", type: "name" },
                    { name: "to", type: "name" },
                    { name: "quantity", type: "asset" },
                    { name: "memo", type: "string" }
                ]
            }
        ],
        actions: [
            {
                name: "transfer",
                type: "transfer",
                ricardian_contract: ""
            }
        ],
        tables: [],
        ricardian_clauses: []
    });

    const encodedData = Serializer.encode({
        object: transferData,
        abi: transferABI,
        type: "transfer"
    });

    const transferAction = Action.from({
        account: 'core.vaulta',
        name: 'transfer',
        authorization: [{
            actor: fromAccountName,
            permission: 'active',
        }],
        data: encodedData,
    });

    const transactionHeader = {
        expiration: new Date(expiration * 1000).toISOString().slice(0, -1),
        ref_block_num: refBlockNum,
        ref_block_prefix: refBlockPrefix,
        max_net_usage_words: 0,
        max_cpu_usage_ms: 0,
        delay_sec: 0,
    };

    const transaction = Transaction.from({
        ...transactionHeader,
        actions: [transferAction],
    });

    const digest = transaction.signingDigest(VAULTA_CHAIN_ID);
    const signature = privateKey.signDigest(digest);

    const signedTransaction = SignedTransaction.from({
        ...transaction,
        signatures: [signature],
    });

    const encodedTransaction = Serializer.encode({ object: transaction });
    const packedTrx = encodedTransaction.hexString;

    const packedContextFreeData = "";

    return {
        signatures: [signature.toString()],
        compression: false,
        packed_context_free_data: packedContextFreeData,
        packed_trx: packedTrx
    };
}
