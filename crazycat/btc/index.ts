import BIP32Factory from 'bip32';
import * as ecc from 'tiny-secp256k1';
import * as bitcoin from 'bitcoinjs-lib';
import ECPairFactory from 'ecpair';
// @ts-ignore
const bitcore = require('bitcore-lib');
// @ts-ignore
import elliptic from 'elliptic';
const bip32 = BIP32Factory(ecc);
const Secp256k1 = new elliptic.ec('secp256k1');
// init ECC lib
bitcoin.initEccLib(ecc);
// Create ECPair factory
const ECPair = ECPairFactory(ecc);
// @ts-ignore
import {toXOnly} from 'bitcoinjs-lib/src/psbt/bip371';
interface ImportBtcAddressParams {
    privateKey: string;
    chain: string;
    typeAddress: string
}

interface TransactionInput {
    address: string;
    txid: string;
    amount: number;
    vout: number;
}

interface TransactionOutput {
    address: string;
    amount: number;
}

interface SignObj {
    inputs: TransactionInput[];
    outputs: TransactionOutput[];
}

interface TaprootTransactionInput {
    address: string;
    txid: string;
    amount: number;
    vout: number;
    privateKey: string;
}

interface TaprootSignObj {
    inputs: TaprootTransactionInput[];
    outputs: TransactionOutput[];
}



export function signTaprootTransaction(params: { signObj: TaprootSignObj; network?: string }): string {
    const { signObj, network = 'bitcoin' } = params;

    if (network !== 'bitcoin' && network !== 'btc') {
        throw new Error('Taproot is only supported on Bitcoin network');
    }

    const psbt = new bitcoin.Psbt({ network: bitcoin.networks.bitcoin });

    for (const input of signObj.inputs) {
        if (!input.privateKey || input.privateKey.length !== 64) {
            throw new Error('Invalid private key format for Taproot signing');
        }

        const privateKeyBuffer = Buffer.from(input.privateKey, 'hex');
        const keyPair = ECPair.fromPrivateKey(privateKeyBuffer, { network: bitcoin.networks.bitcoin });

        const xOnlyPubkey = toXOnly(keyPair.publicKey);

        const p2tr = bitcoin.payments.p2tr({
            internalPubkey: xOnlyPubkey,
            network: bitcoin.networks.bitcoin
        });

        if (p2tr.address !== input.address) {
            throw new Error(`Address mismatch for input ${input.txid}:${input.vout}`);
        }

        psbt.addInput({
            hash: input.txid,
            index: input.vout,
            sequence: 0,
            witnessUtxo: {
                value: BigInt(input.amount),
                script: p2tr.output!
            },
            tapInternalKey: xOnlyPubkey
        });
    }

    for (const output of signObj.outputs) {
        psbt.addOutput({
            address: output.address,
            value: BigInt(output.amount)
        });
    }

    for (let i = 0; i < signObj.inputs.length; i++) {
        const input = signObj.inputs[i];
        const privateKeyBuffer = Buffer.from(input.privateKey, 'hex');

        const keyPair = ECPair.fromPrivateKey(privateKeyBuffer, {
            network: bitcoin.networks.bitcoin
        });
        const xOnlyPubkey = toXOnly(keyPair.publicKey);

        const tweakedSigner = keyPair.tweak(
            bitcoin.crypto.taggedHash('TapTweak', xOnlyPubkey)
        );

        psbt.signInput(i, tweakedSigner);
    }

    psbt.finalizeAllInputs();

    const transaction = psbt.extractTransaction();
    return transaction.toHex();
}
