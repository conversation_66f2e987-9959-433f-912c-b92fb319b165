
# optimism 挑战期
packages/contracts-bedrock/src/L1/OptimismPortal2.sol
OptimismPortal2.finalizeWithdrawalTransaction() 中的 checkWithdrawal 方法 会 验证挑战期

// A proven withdrawal must wait at least `PROOF_MATURITY_DELAY_SECONDS` before finalizing.
if (block.timestamp - provenWithdrawal.timestamp <= PROOF_MATURITY_DELAY_SECONDS) {
    revert OptimismPortal_ProofNotOldEnough();
}

packages/contracts-bedrock/deploy-config/sepolia.json 部署脚本中 定义了 sepolia 的挑战期为7天

"proofMaturityDelaySeconds": 604800
**

# btc taproot离线签名

txhash: 7c6660955f8f3e141b23593a8e49eda81f981659672521f872562f7bed516b24

api: https://bitcoin-mainnet.core.chainstack.com

# Vaulta 区块链调研报告

## 1. 密码学算法

### 算法描述
Vaulta 区块链作为 EOS 的继承者，采用以下密码学算法：

**签名算法：**
- **ECDSA (Elliptic Curve Digital Signature Algorithm)**
    - 支持 secp256k1 曲线（与比特币和以太坊兼容）
    - 支持 secp256r1 (P-256) 曲线
- **BLS (Boneh-Lynn-Shacham) 签名**
    - 用于 Savanna 共识算法中的聚合签名
    - 提供更高效的多签名验证

**哈希算法：**
- SHA-256
- RIPEMD-160（用于地址生成）

### 算法优缺点及应用场景

**ECDSA secp256k1：**
- 优点：与以太坊生态兼容，广泛支持
- 缺点：签名大小相对较大
- 应用：用户账户签名、EVM 兼容性

**BLS 签名：**
- 优点：支持签名聚合，提高共识效率
- 缺点：计算复杂度较高
- 应用：Savanna 共识算法中的快速最终确认

### 证据链接
- [Vaulta Savanna 共识文档](https://vaulta.gitbook.io/vaulta-guides/networks/vaulta-native/savanna-consensus)
- [EOS 密码学实现](https://github.com/VaultaFoundation/vaulta-system-contract)

## 2. 链的特性

### 基本原理
Vaulta 是基于 EOS 技术栈的高性能区块链，采用委托权益证明（DPoS）共识机制结合 Savanna 算法，支持智能合约和去中心化应用。

### 共识算法
- **算法类型：** 委托权益证明（DPoS）+ Savanna 共识算法
- **质押支持：** 是，支持代币质押和委托投票
- **交易确认时间：** 1 秒内达到真正最终确认（True Finality）
- **确认区块数：** 1 个区块即可达到不可逆确认

### 账户模型
- **模型类型：** 账户模型（类似以太坊）
- **钱包开发意义：**
    - 支持人类可读的账户名（12字符以内）
    - 多权限系统（owner、active、custom）
    - 支持多签名和权重机制
    - 资源管理系统（CPU、NET、RAM）

### 单链或多链
- **架构类型：** 混合架构
- **特殊特性：**
    - Vaulta Native：原生高性能链
    - Vaulta EVM：完全兼容以太坊的执行环境
    - 双虚拟机支持：WASM + EVM

### 代币精度
- **原生代币：** Vaulta (A)
- **精度：** 4 位小数（1.0000 A）
- **离线交易：** 需要精确的小数位处理

### 账户体系
- **地址生成：** 基于账户名系统，非公钥派生
- **多签名支持：** 原生支持多权限和权重机制
- **特殊账户：** 系统账户、智能合约账户

### 交易构建体系
- **交易结构：**
    - Actions（操作列表）
    - Authorization（授权信息）
    - Expiration（过期时间）
    - Reference Block（参考区块）
    - CPU/NET 资源消耗

### 代币与 NFT
- **原生支持：** 是
- **标准协议：**
    - 原生代币标准
    - ERC-20/ERC-721（通过 EVM 支持）
- **实现方式：** 智能合约实现

### Memo/Tag 支持
- **支持情况：** 支持交易备注
- **用途：** 交易标识、应用层数据传递

### 证据链接
- [Vaulta 开发者文档](https://docs.vaulta.com/docs/latest/quick-start/introduction)
- [Vaulta 网络架构](https://vaulta.gitbook.io/vaulta-guides/networks/vaulta-native)

## 3. RPC 接口

### 主要 RPC 接口

**区块同步状态：**
- 接口：`POST /v1/chain/get_info`
- 功能：获取链信息和同步状态
- 示例：`https://api.vaulta.com/v1/chain/get_info`

**余额查询：**
- 接口：`POST /v1/chain/get_currency_balance`
- 功能：查询代币余额
- 参数：`{"code": "eosio.token", "account": "accountname", "symbol": "A"}`

**账户信息：**
- 接口：`POST /v1/chain/get_account`
- 功能：获取账户详细信息
- 参数：`{"account_name": "accountname"}`

**交易记录：**
- 接口：`POST /v1/history/get_actions`
- 功能：查询账户交易历史
- 注意：需要历史插件支持

**签名参数：**
- 接口：`POST /v1/chain/get_required_keys`
- 功能：获取交易所需签名密钥
- 参数：transaction 对象和可用密钥列表

**最新区块：**
- 接口：`POST /v1/chain/get_info`
- 功能：获取最新区块信息

**区块解析：**
- 接口：`POST /v1/chain/get_block`
- 功能：获取指定区块详情
- 参数：`{"block_num_or_id": "block_number"}`

**交易详情：**
- 接口：`POST /v1/chain/get_transaction_status`
- 功能：查询交易状态
- 参数：`{"id": "transaction_id"}`

**特殊 RPC：**
- `POST /v1/chain/push_transaction` - 广播交易
- `POST /v1/chain/get_table_rows` - 查询智能合约表数据
- `POST /v1/chain/get_producer_schedule` - 获取出块者调度

### 证据链接
- [Vaulta Chain API 文档](https://docs.vaulta.com/apis/spring/latest/chain.api/)

## 4. 钱包节点部署

### 节点类型选择
**推荐部署：** API 节点
- 提供完整的 RPC 服务
- 不参与共识，资源需求较低
- 适合钱包和交易所集成

### 硬件要求
**最低配置：**
- CPU：4 核心
- 内存：8 GB RAM
- 存储：500 GB SSD
- 网络：100 Mbps 带宽

**推荐配置：**
- CPU：8 核心
- 内存：16 GB RAM
- 存储：1 TB NVMe SSD
- 网络：1 Gbps 带宽

### 开发和运维分工

**开发职责：**
- 节点配置和部署脚本
- API 集成和封装
- 数据同步监控
- 备份和恢复策略

**运维职责：**
- 硬件维护和监控
- 网络连接管理
- 安全更新和补丁
- 性能优化和调优

### 证据链接
- [Vaulta 节点运维指南](https://docs.vaulta.com/docs/latest/node-operation/getting-started/)

## 5. 费用模型

### 交易费用机制
Vaulta 采用资源模型而非传统 Gas 费用：

**资源类型：**
- **CPU：** 计算资源，用于执行交易
- **NET：** 网络带宽，用于传输交易数据
- **RAM：** 存储资源，用于保存链上数据

**费用计算：**
- CPU/NET：通过质押获得，可租赁
- RAM：市场化定价，可买卖
- 交易费用极低，主要消耗质押的资源

**EVM 费用：**
- Vaulta EVM 使用传统 Gas 模型
- Gas 费用显著低于以太坊主网
- 设计为在高活动期间保持稳定

### 证据链接
- [Vaulta 资源管理](https://vaulta.gitbook.io/vaulta-guides/networks/network-resources)
- [Vaulta EVM 费用](https://docs.eosnetwork.com/evm/architecture/token-economy/)

## 6. 历史回滚事件

### 回滚事件调查
经过调研，Vaulta 区块链作为 EOS 的继承者，继承了 EOS 的稳定性特点：

**重大事件：**
- **EOS 到 Vaulta 迁移：** 2024年平滑过渡，无回滚事件
- **Savanna 升级：** 2024年成功部署，提供真正最终确认

**网络稳定性：**
- Savanna 共识算法提供 1 秒真正最终确认
- BLS 签名聚合提高共识效率
- 未发生重大分叉或回滚事件

### 证据链接
- [EOS 到 Vaulta 迁移公告](https://eosnetwork.com/resources/eos-hard-fork-spring-1-0-savanna-consensus/)
- [Savanna 共识介绍](https://vaulta.gitbook.io/vaulta-guides/networks/vaulta-native/savanna-consensus)

## 7. 同源链

### 技术同源性分析
Vaulta 区块链基于 EOS 技术栈，与以下链存在技术同源关系：

**直接同源链：**
- **EOS：** Vaulta 是 EOS 的直接继承者
- **WAX：** 基于 EOSIO 技术栈
- **Telos：** 使用相似的 DPoS 共识

**架构相似链：**
- **BNB Smart Chain：** 类似的 DPoS 机制
- **Polygon：** 相似的侧链架构

### 竞争优势
- **真正最终确认：** 1 秒不可逆确认
- **双虚拟机：** Native + EVM 支持
- **资源模型：** 更灵活的费用结构
- **高性能：** 10,000+ TPS

### 证据链接
- [Vaulta 技术架构](https://docs.vaulta.com/docs/latest/core-concepts/)
- [EOS 生态系统](https://eosnetwork.com/)

## 8. 浏览器与开发者资源

### 官方区块链浏览器
**主网浏览器：**
- [Vaulta 区块浏览器](https://explorer.vaulta.com/)
- [EOS 网络浏览器](https://explorer.eosnetwork.com/)

**EVM 浏览器：**
- [Vaulta EVM 浏览器](https://explorer.evm.vaulta.com/)

### 开发者文档
- [Vaulta 开发者门户](https://docs.vaulta.com/)
- [Vaulta 用户指南](https://vaulta.gitbook.io/vaulta-guides/)
- [API 文档](https://docs.vaulta.com/apis/spring/latest/chain.api/)

### 开发工具
- [Vaulta Web IDE](https://ide.vaulta.com/)
- [Vaulta CLI 工具](https://docs.vaulta.com/docs/latest/quick-start/local-development)
- [智能合约模板](https://github.com/vaultafoundation/template-projects)

### 市场信息
- [CoinMarketCap - Vaulta](https://coinmarketcap.com/currencies/vaulta/)
- [Vaulta 官网](https://vaulta.com/)
- [Vaulta 门户](https://unicove.com/)

### 社区资源
- [Vaulta Telegram](https://t.me/vaulta)
- [Vaulta Twitter](https://x.com/Vaulta_)
- [Vaulta GitHub](https://github.com/VaultaFoundation)

### 证据链接
- [Vaulta 资源目录](https://vaulta.gitbook.io/vaulta-guides/resources/directory)

## 9. JavaScript SDK 查找

### 官方 JavaScript SDK

**WharfKit (推荐)**
- **名称：** WharfKit
- **功能概述：**
    - 现代化的 Antelope/EOS/Vaulta 区块链 SDK
    - 模块化设计：Session Kit、Contract Kit、Account Kit
    - 完整的会话管理和用户认证
    - 智能合约交互和账户管理
    - 插件系统支持钱包和账户创建
    - 专业的 UI 渲染器和多语言支持

**核心组件：**
- **Session Kit** - 会话管理：`@wharfkit/session`
- **Contract Kit** - 智能合约交互：`@wharfkit/contract`
- **Account Kit** - 账户管理：`@wharfkit/account`

### Vaulta EVM SDK
- **名称：** ethers.js / web3.js
- **功能概述：** 标准以太坊 SDK，完全兼容 Vaulta EVM
- **网络配置：**
    - RPC URL: `https://api.evm.eosnetwork.com`
    - Chain ID: 17777

### 传统 SDK（已弃用）
- **EOSJS：** 已被 WharfKit 取代，不再推荐使用
- **迁移建议：** 新项目应使用 WharfKit，现有项目建议迁移

### SDK 特点
- **现代化架构：** 基于 TypeScript，模块化设计
- **插件系统：** 支持钱包、账户创建、UI 等插件
- **用户体验：** 专业 UI 设计，多语言支持
- **双虚拟机支持：** Native + EVM
- **活跃维护：** Antelope Coalition 官方支持
- **完整生态：** 覆盖所有区块链交互需求

### 证据链接
- [WharfKit 官方网站](https://wharfkit.com/)
- [WharfKit GitHub 组织](https://github.com/wharfkit)
- [WharfKit Session Kit 文档](https://wharfkit.com/docs/session-kit)
- [WharfKit 账户创建插件](https://wharfkit.com/docs/session-kit/plugin-account-creation)
- [Vaulta JavaScript SDK 文档](https://docs.vaulta.com/docs/latest/web-applications/javascript-sdk)




