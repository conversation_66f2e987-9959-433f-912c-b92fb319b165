import { Interface } from "@ethersproject/abi";
import { ethers } from "ethers";


export async function signTransaction(params: any) {
    const { privateKey, nonce, from, to, gasLimit, gasPrice, amount, data, chainId, decimal, maxFeePerGas, maxPriorityFeePerGas, bridgeAddress } = params;


    const wallet = new ethers.Wallet(Buffer.from(privateKey,"hex"));

    const txData:any = {
        nonce: ethers.utils.hexlify(nonce),
        from,
        to,
        gasLimit: ethers.utils.hexlify(gasLimit),
        value: ethers.utils.hexlify(ethers.utils.parseUnits(amount,decimal)),
        chainId
    }

    if (maxFeePerGas && maxPriorityFeePerGas){
        txData.type = 2;
        txData.maxFeePerGas = ethers.utils.hexlify(maxFeePerGas);
        txData.maxPriorityFeePerGas = ethers.utils.hexlify(maxPriorityFeePerGas);
    }else{
        txData.gasPrice = ethers.utils.hexlify(gasPrice);
    }

    if (bridgeAddress && bridgeAddress != "0x00"){
        const ABI = [
            "function depositETH(uint32, bytes)"
        ];

        const iface = new Interface(ABI);
        txData.data = iface.encodeFunctionData('depositETH', [gasLimit, hex"0x"]);
        txData.to = bridgeAddress;
        txData.value = 0;
    }

    if (data){
        txData.data = data;
    }

    return await wallet.signTransaction(txData);
}