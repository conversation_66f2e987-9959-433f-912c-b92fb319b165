import {signTaprootTransaction} from '../btc/index';

test('signTaprootTransaction', () => {
    let fromAddress = "**************************************************************"
    let toAddress = "**************************************************************"
    const signObj = {
        inputs: [{
            address: fromAddress,
            txid: "6624f4e7262edda15b350b41959ab1a2fb559f6a6f6bbba2c5b741e80296727e",
            amount: 30980, // 0.001 BTC in satoshis
            vout: 0,
            privateKey: ""
        }],
        outputs: [
            {
                address: toAddress,
                amount: 5000
            },
            {
                address: fromAddress,
                amount: 23670
            }
        ],

    };

    try {
        const signedTx = signTaprootTransaction({
            signObj,
            network: 'bitcoin'
        });

        console.log('Signed Taproot Transaction:', signedTx);
        console.log('Transaction type:', typeof signedTx);
        console.log('Transaction length:', signedTx.length);
        console.log('Is valid hex:', /^[0-9a-fA-F]+$/.test(signedTx));
    } catch (error) {
        console.log('Expected error for mock transaction:', error);
    }
});