import { signTransaction } from "../op";

describe("op test", () => {
    it("sing op",  async () => {
        const rawHex = await signTransaction({
            "privateKey": "f6483e9c029d9044f8db866e8bf090a8839a3f0b0860685b10990a8b21f2c7f9",
            "nonce": 85,
            "from": "0x31b69bdC28124bcc1918a0a6cA3b34920e604E0F",
            "to": "0xFBb0621E0B23b5478B630BD55a5f21f67730B0F1",
            "gasLimit": 200000,
            "amount": "0.1",
            "gasPrice": 195000000000,
            "decimal": 18,
            "chainId": 11155111,
            "bridgeAddress": "0xFBb0621E0B23b5478B630BD55a5f21f67730B0F1"
        })
        console.log(rawHex);
    });
});