import * as ethers from "ethers";
import {Interface} from "@ethersproject/abi";
import {FeeMarketEIP1559Transaction, Transaction} from "@ethereumjs/tx";
import Common from "@ethereumjs/common";

const BigNumber = require('bignumber.js');

const SUPPORT_CHAIN_NETWORK = {
    1: 'Ethereum',
    324: 'ZksyncEra',
    42161: 'Arbitrum',
    42170: 'ArbitrumNova',
    5000: 'Mantle',
    56: 'BscChain',
    128: 'Heco',
    137: 'Polygon',
    10001: 'EthereumPow',
    61: 'EthereumClassic',
    8217: 'klay',
    1101: 'PolygonZk',
    66: 'OkexChain',
    9001: 'Evmos',
    10: 'Optimism',
    59144: 'Linea',
    8453: 'Base',
    17000: 'Holesky'
};

/**
 * 根据 助记词seed 创建钱包地址
 * @param seedHex 助记词
 * @param addressIndex
 * @constructor
 */
export function CreateAddress(seedHex: string, addressIndex: string) {
    const rootNode = ethers.utils.HDNode.fromSeed(Buffer.from(seedHex, "hex"));

    const {
        privateKey,
        publicKey,
        address
    } = rootNode.derivePath("m/44'/60'/0'/0/" + addressIndex + '');
    return JSON.stringify({
        privateKey,
        publicKey,
        address
    });
}

/**
 * 根据私钥导入钱包
 * @param privateKey
 * @constructor
 */
export function ImportEthWallet(privateKey: string) {
    const wallet = new ethers.Wallet(Buffer.from(privateKey, 'hex'));
    return JSON.stringify({
        privateKey,
        address: wallet.address
    });
}

/**
 * 验证地址
 * @param address
 * @constructor
 */
export function VerifyAddress(address: string) {
    return ethers.utils.isAddress(address);
}

export function OpL1BridgeDepositEth() {
    const activityInterface = new ethers.utils.Interface([
        "function depositETH(uint32, bytes)",
    ]);

    return activityInterface.encodeFunctionData("depositETH", [21000, ethers.utils.hexlify('0x')]);
}

export async function signTransaction2(params: {
    privateKey: string;
    nonce: number;
    from: string;
    to: string;
    gasLimit: number;
    amount: string;
    gasPrice: number;
    decimal: number;
    chainId: any;
    tokenAddress: string;
    callData: string;
    maxPriorityFeePerGas?: number;
    maxFeePerGas?: number;
}) {
    let {
        privateKey,
        nonce,
        from,
        to,
        gasPrice,
        gasLimit,
        amount,
        tokenAddress,
        callData,
        decimal,
        maxPriorityFeePerGas,
        maxFeePerGas,
        chainId
    } = params;
    const transactionNonce = ethers.utils.hexlify(nonce);
    const gasLimits = ethers.utils.hexlify(gasLimit);
    const chainIdHex = ethers.utils.hexlify(chainId);
    const numBalanceHex = ethers.utils.hexlify(ethers.utils.parseUnits(amount, decimal));
    console.log(numBalanceHex)
    let txData: any = {
        nonce: transactionNonce,
        gasLimit: gasLimits,
        to,
        from,
        chainId: chainIdHex,
        value: numBalanceHex,
        data: callData, // fixme
    }
    if (maxFeePerGas && maxPriorityFeePerGas) {
        txData.maxFeePerGas = ethers.utils.hexlify(maxFeePerGas);
        txData.maxPriorityFeePerGas = ethers.utils.hexlify(maxPriorityFeePerGas);
    } else {
        txData.gasPrice = ethers.utils.hexlify(gasPrice);
    }
    if (tokenAddress && tokenAddress !== "0x00") {
        const ABI = ["function transfer(address to, uint amount)"];
        const iface = new Interface(ABI);
        if (params.callData) {
            txData.data = callData;
            txData.value = "0x0";
        } else {
            txData.data = iface.encodeFunctionData("transfer", [to, numBalanceHex]);
            txData.to = tokenAddress;
        }
        txData.value = "0x0";
    }
    let common: any, tx: any;
    if (txData.maxFeePerGas && txData.maxPriorityFeePerGas) {
        common = (Common as any).custom({
            chainId: chainId,
            defaultHardfork: "london"
        });
        tx = FeeMarketEIP1559Transaction.fromTxData(txData, {
            common
        });
    } else {
        common = (Common as any).custom({chainId: chainId})
        tx = Transaction.fromTxData(txData, {
            common
        });
    }
    const privateKeyBuffer = Buffer.from(privateKey, "hex");
    const signedTx = tx.sign(privateKeyBuffer);
    const serializedTx = signedTx.serialize();
    if (!serializedTx) {
        throw new Error("sign is null or undefined");
    }
    return `0x${serializedTx.toString('hex')}`;
}