import {
    Create<PERSON>ddress,
    ImportEthWallet,
    OpL1BridgeDepositEth,
    signTransaction2,
    VerifyAddress
} from '../wallet';
import {mnemonicToSeed} from "../wallet/bip/bip";
import * as ethers from "ethers";

describe('CreateAddress', () => {
    test('should work', () => {
        const mnemonic = 'neck hurt mountain garment ski ranch teach cup zero there tobacco swift'
        const seed = mnemonicToSeed({mnemonic: mnemonic, password: ''});
        const address = CreateAddress(seed.toString('hex'), '0')
        console.log(address)
    });
});

describe('ImportEthWallet', () => {
    test('should work', () => {
        const privateKey = '0685373600dabedfdd6a74024fb527c45887cec412784a479d1aa6581853e945'
        const addressInfo = ImportEthWallet(privateKey)
        console.log(addressInfo)
    });
});

describe('VerifyAddress', () => {
    test('should work', () => {
        const address = '****************************************** '
        const addressInfo = VerifyAddress(address)
        console.log(addressInfo)
    });
});

describe('signTransaction2', () => {
    // test('sign', async () => {
    //     const rawHex = await signTransaction({
    //         "privateKey": "0685373600dabedfdd6a74024fb527c45887cec412784a479d1aa6581853e945",
    //         "nonce": 11,
    //         "from": "******************************************",
    //         "to": "******************************************",
    //         "gasLimit": 30000,
    //         "amount": "10",
    //         "gasPrice": 5795062165,
    //         "decimal": 18,
    //         "chainId": 17000,
    //         "tokenAddress": "******************************************"
    //     })
    //     console.log(rawHex)
    // });

    test('sign', async () => {
        const activityInterface = new ethers.utils.Interface([
            "function depositETH(uint32, bytes)",
        ]);
        const callData =  activityInterface.encodeFunctionData("depositETH", [21000, ethers.utils.hexlify('0x')])
        console.log("callData=======", callData)

        const rawHex = await signTransaction2({
            "privateKey": "0685373600dabedfdd6a74024fb527c45887cec412784a479d1aa6581853e945",
            "nonce": 5,
            "from": "******************************************",
            "to": "******************************************",
            "gasLimit": 3000000,
            "amount": "0.1",
            "gasPrice": 5795062165,
            "decimal": 18,
            "chainId": 11155111,
            "tokenAddress": "0x00",
            "callData": callData
        })
        console.log("sign=======", rawHex)
    });
})

