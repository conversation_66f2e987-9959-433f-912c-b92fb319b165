import { generateMnemonic, mnemonicToSeed } from "../wallet/bip/bip";
import { createAddress, signTransaction, importAddress } from "../wallet";
const ethers = require("ethers");
const provider = new ethers.providers.JsonRpcProvider(
  "https://polygon-rpc.com"
);

describe("eth unit test case", () => {
  // Test address creation
  test("createAddress", () => {
    const mnemonic = generateMnemonic({ number: 12, language: "english" });
    const params = {
      mnemonic: mnemonic,
      password: "",
    };
    const seed = mnemonicToSeed(params);
    const account = createAddress(seed.toString("hex"), "0");
    console.log(account);
  });

  // Test address import
  test("importAddress", () => {
    const account = importAddress("YOUR_PRIVATE_KEY");
    console.log(account);
  });

  // Test Token Transfer
  test("sign and broadcast token transfer", async () => {
    const privateKey = "YOUR_PRIVATE_KEY";
    const wallet = new ethers.Wallet(privateKey, provider);
    
    // Transfer parameters
    const transferParams = {
      tokenAddress: "******************************************",  // USDT contract address
      to: "******************************************",          // Recipient address
      amount: "1",                                                 // Transfer amount
      decimal: 6                                                   // USDT decimals
    };

    // Get current gas prices
    const feeData = await provider.getFeeData();
    console.log("Current gas prices:", {
      maxFeePerGas: ethers.utils.formatUnits(feeData.maxFeePerGas || 0, "gwei"),
      maxPriorityFeePerGas: ethers.utils.formatUnits(feeData.maxPriorityFeePerGas || 0, "gwei"),
    });

    // Get current nonce
    const nonce = await provider.getTransactionCount(wallet.address);
    console.log("Current nonce:", nonce);

    // Sign transaction
    const rawHex = signTransaction({
      privateKey: privateKey,
      nonce: Number(nonce),
      from: wallet.address,
      to: transferParams.to,
      gasLimit: 100000,
      maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
      maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
      gasPrice: 0,
      amount: transferParams.amount,
      decimal: transferParams.decimal,
      chainId: 137,
      tokenAddress: transferParams.tokenAddress,
      callData: "",
    });

    // Broadcast transaction
    const tx = await provider.sendTransaction(rawHex);
    console.log("Transaction hash:", tx.hash);

    // Wait for confirmation
    const receipt = await tx.wait(1);
    console.log("Transaction confirmed in block:", receipt.blockNumber);
    console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
  }, 60000);

  // Test Contract Call (Activity Creation)
  test("sign and broadcast contract call", async () => {
    const testActivity = {
      businessName: "test1",
      activityContent: '{"activityContentDescription":"test1","activityContentAddress":"test1","activityContentLink":"test1"}',
      latitude: 25.0329636,
      longitude: 121.5654268,
      activityDeadLine: **********,
      totalDropAmts: ethers.utils.parseUnits("1", 6),
      dropType: 1,
      dropNumber: 1,
      minDropAmt: ethers.utils.parseUnits("1", 6),
      maxDropAmt: ethers.utils.parseUnits("1", 6),
      tokenAddress: "******************************************",
    };
  //YOUR_PRIVATE_KEY
    const privateKey = "cadf7450f8a7f15b5a3c9eb094b7de3fcef85465ee3de3d3a3bb687f31c79289";
    const wallet = new ethers.Wallet(privateKey, provider);
    //******************************************
    const spenderAddress = "******************************************";



    // Create contract interface  function depositETH(uint32 _minGasLimit, bytes calldata _extraData)
    const activityInterface = new ethers.utils.Interface([
      "function depositETH(uint32, bytes)",
    ]);

    // Encode function call data
    const callData = activityInterface.encodeFunctionData("depositETH", [
      21000,
      '0x'
    ]);

// Sign transaction
    const rawHex = signTransaction({
      privateKey: privateKey,
      nonce: Number(await provider.getTransactionCount(wallet.address)),
      from: wallet.address,
      to: spenderAddress,
      gasLimit: 500000,
      maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
      maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
      gasPrice: 0,
      amount: "0.1",
      decimal: 18,
      chainId: 11155420,
      tokenAddress: '0x00',
      callData: callData,
    });

    // Broadcast transaction
    const tx = await provider.sendTransaction(rawHex);
    console.log("Transaction hash:", tx.hash);

    // Wait for confirmation
    const receipt = await tx.wait(1);
    console.log("Transaction confirmed in block:", receipt.blockNumber);
    console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
  }, 60000);
});

test("sign op tx", async () => {
  const privateKey = "cadf7450f8a7f15b5a3c9eb094b7de3fcef85465ee3de3d3a3bb687f31c79289";
  // Sign transaction
  const rawHex = signTransaction({
    privateKey: privateKey,
    nonce: 82,
    from: "******************************************",
    to: "******************************************",
    gasLimit: 91000,
    maxFeePerGas: 327993150328,
    maxPriorityFeePerGas: 32799315032,
    gasPrice: 0,
    amount: "0.1",
    decimal: 18,
    chainId: 11155420,
    tokenAddress: "0x00",
    callData: "",
  });
  console.log(rawHex)
})

test("sign op token tx", async () => {
  const privateKey = "cadf7450f8a7f15b5a3c9eb094b7de3fcef85465ee3de3d3a3bb687f31c79289";
  // Sign transaction
  const rawHex = signTransaction({
    privateKey: privateKey,
    nonce: 83,
    from: "******************************************",
    to: "******************************************",
    gasLimit: 91000,
    maxFeePerGas: 327993150328,
    maxPriorityFeePerGas: 32799315032,
    gasPrice: 0,
    amount: "200",
    decimal: 18,
    chainId: 11155420,
    tokenAddress: "******************************************",
    callData: "",
  });
  console.log(rawHex)
})

test("depositETH op tx", async () => {
  const activityInterface = new ethers.utils.Interface([
    "function depositETH(uint32, bytes)",
  ]);

  // Encode function call data
  const callData = activityInterface.encodeFunctionData("depositETH", [
    21000,
    '0x'
  ]);
  const privateKey = "cadf7450f8a7f15b5a3c9eb094b7de3fcef85465ee3de3d3a3bb687f31c79289";
  // Sign transaction
  const rawHex = signTransaction({
    privateKey: privateKey,
    nonce: 87,
    from: "******************************************",
    to: "******************************************",
    gasLimit: 91000,
    maxFeePerGas: 327993150328,
    maxPriorityFeePerGas: 32799315032,
    gasPrice: 0,
    amount: "0.1",
    decimal: 18,
    chainId: 11155420,
    tokenAddress: "0x00",
    callData: callData,
  });
  console.log(rawHex)
})

test("withdraw op tx", async () => {
  const activityInterface = new ethers.utils.Interface([
    "function withdraw(address, uint256, uint32, bytes)",
  ]);

  const callData = activityInterface.encodeFunctionData("withdraw", ['******************************************', 1, 21000, '0x']);

  const privateKey = "cadf7450f8a7f15b5a3c9eb094b7de3fcef85465ee3de3d3a3bb687f31c79289";
  // Sign transaction
  const rawHex = signTransaction({
    privateKey: privateKey,
    nonce: 93,
    from: "******************************************",
    to: "******************************************",
    gasLimit: 91000,
    maxFeePerGas: 327993150328,
    maxPriorityFeePerGas: 32799315032,
    gasPrice: 0,
    amount: "0.1",
    decimal: 18,
    chainId: 11155420,
    tokenAddress: "0x00",
    callData: callData,
  });
  console.log(rawHex)
})