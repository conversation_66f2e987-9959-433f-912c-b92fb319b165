

1.btc 离线地址生成



console.log

{

&nbsp;       privateKey: '\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*\*',

&nbsp;       publicKey: '0237a6b87876ac213954376dac621b6c2b2437e4f5f097a5cf63ee61eb2f060b12',

&nbsp;       address: '**************************************************************'

}



&nbsp;     

2.Bitcoin 离线签名

&nbsp;    postman  api:https://bitcoin-mainnet.core.chainstack.com

&nbsp;     request:

&nbsp;       {

&nbsp;               "id": 1,

&nbsp;               "jsonrpc": "2.0",

&nbsp;               "method": "sendrawtransaction",

&nbsp;               "params": \[

&nbsp;               "020000000001018a694b6d1d97b2694d54f778b27458f33cb7f3448567abcc7e6050b0c487d7d50600000000ffffffff0202090000000000002251204f8e885af98237289d2ba857ba6cd7eecc6939c86bdd9a1008aee6595c26fdf9204e0000000000002251206a34952ea787bca0217db488a9cbc2b1417654395804c2f9977d96ac1d6c0b1401400a5f57cebe4f9a999560403eb3d82a8bb6179ee43105a9b1439099abf8c8f18651d37a801f879ae9dd870f00a2750a1f0aa40bb2f168f9fb832ffc0de63da4ae00000000"

&nbsp;               ]

&nbsp;       }

&nbsp;       



&nbsp;       respose:

&nbsp;       {

&nbsp;               "result": "cb6ce24bfd71fe7fd9b1a9577362a6d12b37cf0b9c8153ca5351701e2dd5b6e8",

&nbsp;               "error": null,

&nbsp;               "id": 1

&nbsp;       }



