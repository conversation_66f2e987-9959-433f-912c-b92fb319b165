
# ETH 充值 (L1 → L2) 测试结果

💰 ETH 充值测试

      at Object.<anonymous> (test/op-bridge-simple.test.ts:104:13)

console.log
📝 调用数据: 0x9a2ac6d50000000000000000000000004997594a6d49130ae18da7647819f7019b3abdef0000000000000000000000000000000000000000000000000000000000030d4000000000000000000000000000000000000000000000000000000000000000600000000000000000000000000000000000000000000000000000000000000000

      at Object.<anonymous> (test/op-bridge-simple.test.ts:134:13)

console.log
📝 目标合约: ******************************************

      at Object.<anonymous> (test/op-bridge-simple.test.ts:135:13)

console.log
✅ ETH 充值成功: 0xa9ed047652340b61c4ad1bf08981d138e05f2dc35f3147246e2546c3d9af4709

      at Object.<anonymous> (test/op-bridge-simple.test.ts:140:15)

console.log
🔗 Sepolia: https://sepolia.etherscan.io/tx/0xa9ed047652340b61c4ad1bf08981d138e05f2dc35f3147246e2546c3d9af4709

      at Object.<anonymous> (test/op-bridge-simple.test.ts:141:15)


# 挑战期

主网配置文件中的设置： optimism/packages/contracts-bedrock/deploy-config/mainnet.json

```solidity
{
  "proofMaturityDelaySeconds": 604800,        // 7天
  "disputeGameFinalityDelaySeconds": 302400,  // 3.5天
  "faultGameWithdrawalDelay": 604800          // DelayedWETH中的7天延迟
}
```

checkWithdrawal函数中的完整验证逻辑：packages/contracts-bedrock/src/L1
```solidity
function checkWithdrawal(bytes32 _withdrawalHash, address _proofSubmitter) public view {
    // 获取已证明的提现信息
    ProvenWithdrawal memory provenWithdrawal = provenWithdrawals[_withdrawalHash][_proofSubmitter];
    IDisputeGame disputeGameProxy = provenWithdrawal.disputeGameProxy;

    // 检查提现是否已被证明（时间戳非零）
    if (provenWithdrawal.timestamp == 0) {
        revert OptimismPortal_Unproven();
    }

    // 检查证明时间戳是否晚于争议游戏创建时间
    if (provenWithdrawal.timestamp <= disputeGameProxy.createdAt().raw()) {
        revert OptimismPortal_InvalidProofTimestamp();
    }

    // 关键检查：必须等待至少PROOF_MATURITY_DELAY_SECONDS（7天）
    if (block.timestamp - provenWithdrawal.timestamp <= PROOF_MATURITY_DELAY_SECONDS) {
        revert OptimismPortal_ProofNotOldEnough();
    }

    // 检查争议游戏的根声明是否有效（包含额外的3.5天延迟）
    if (!anchorStateRegistry.isGameClaimValid(disputeGameProxy)) {
        revert OptimismPortal_InvalidRootClaim();
    }
}
```
最终确定提现的完整流程: packages/contracts-bedrock/src/L1
```solidity
function finalizeWithdrawalTransaction(
    Types.WithdrawalTransaction memory _tx,
    address _proofSubmitter
) external {
    // 计算提现哈希
    bytes32 withdrawalHash = Hashing.hashWithdrawal(_tx);

    // 检查提现是否可以最终确定（包含7天挑战期检查）
    checkWithdrawal(withdrawalHash, _proofSubmitter);

    // 标记提现为已最终确定，防止重放
    finalizedWithdrawals[withdrawalHash] = true;

    // 解锁ETH
    if (_tx.value > 0) ethLockbox.unlockETH(_tx.value);

    // 执行提现交易
    bool success = SafeCall.callWithMinGas(_tx.target, _tx.gasLimit, _tx.value, _tx.data);
}
```


# XLM 调研报告 

**调研信息**

- 名称：XLM (Stellar Lumens)
- 官方网站：[https://stellar.org/](https://stellar.org/)
- 开发者文档：[https://developers.stellar.org/](https://developers.stellar.org/)
- GitHub 仓库：[https://github.com/stellar](https://github.com/stellar)

## 1. 密码学算法

### 1.1 签名算法

Stellar 使用 **Ed25519** 数字签名算法，这是一种基于椭圆曲线的 EdDSA (Edwards-curve Digital Signature Algorithm) 实现。

**技术特点：**

- **算法类型**：EdDSA (Ed25519)
- **曲线**：Curve25519
- **密钥长度**：256 位
- **签名长度**：64 字节
- **公钥长度**：32 字节

**优势：**

- **高性能**：Ed25519 可实现每秒 70,000 次签名验证
- **安全性强**：抗侧信道攻击，具有确定性签名
- **量子抗性**：相比 ECDSA，对量子计算攻击具有更好的抗性
- **无需随机数**：避免了 ECDSA 中随机数生成的安全风险

**应用场景：**

- 交易签名和验证
- 账户地址生成
- 多重签名支持

**链接：**
- [Stellar 签名文档](https://developers.stellar.org/docs/learn/fundamentals/transactions/signatures-multisig)
- [Ed25519 技术分析](https://2finance.medium.com/understanding-elliptic-curve-digital-signature-algorithm-ecdsa-secp256k1-and-eddsa-curve25519-56ff82fc4f74)

## 2. 链的特性

### 2.1 基本原理

Stellar 是一个开源的分布式账本网络，专为快速、低成本的跨境支付和资产发行而设计。

**核心特点：**

- **分布式账本**：去中心化的账本系统
- **智能合约支持**：集成 Soroban 智能合约平台
- **资产发行**：支持自定义资产和代币
- **跨境支付**：专为国际汇款优化

### 2.2 共识算法

**算法名称**：Stellar Consensus Protocol (SCP) 
**基础架构**：Federated Byzantine Agreement (FBA)

**技术特点：**

- **开放成员资格**：任何人都可以成为验证节点
- **去中心化控制**：无中央权威机构
- **用户定义信任**：每个节点选择自己信任的节点集合
- **无货币奖励**：验证者无经济激励，依靠网络价值驱动

**共识属性：**

- **容错性**：✅ 支持
- **安全性**：✅ 优先保证
- **活跃性**：⚠️ 在极端情况下可能受限

**交易确认：**

- **确认时间**：3-5 秒
- **最终性**：即时最终性（无需等待多个确认）
- **TPS**：理论上可达 1,000+ TPS

**质押支持**：❌ 不支持质押机制

### 2.3 账户模型

**模型类型**：账户模型（非 UTXO）

**账户特点：**

- **序列号**：每个账户维护递增的序列号
- **余额跟踪**：直接记录账户余额
- **状态存储**：账户可存储额外数据和配置

**钱包开发意义：**

- 简化余额查询和管理
- 支持复杂的账户配置
- 便于实现多重签名

### 2.4 单链架构

**架构类型**：单链架构

**特点：**

- **统一网络**：所有交易在同一条链上处理
- **简化设计**：避免跨链复杂性
- **高度集成**：智能合约与传统支付无缝集成

**局限性：**

- 扩展性受单链性能限制
- 无原生跨链功能

### 2.5 代币精度

**原生代币**：Lumens (XLM) 
**最小单位**：Stroop 
**精度**：7 位小数 (1 XLM = 10,000,000 stroops)
**离线交易**：需要完整精度数据以确保计算准确性

### 2.6 账户体系

**地址生成规则：**

- 基于 Ed25519 公钥
- 使用 Base32 编码
- 地址格式：G + 55个字符 (如：GAHK7EEG2WWHVKDNT4CEQFZGKF2LGDSW2IVM4S5DP42RBW3K6BTODB4A)

**特殊账户类型：**

- **多重签名账户**：支持最多 20 个签名者
- **智能合约账户**：可部署和调用智能合约
- **预授权交易**：支持预先授权特定交易

### 2.7 交易构建体系

**交易结构：**

```ts
`Transaction {
	source_account: AccountID 
    fee: uint32 
    seq_num: SequenceNumber  
    time_bounds: TimeBounds (可选)  
    memo: Memo (可选)  
    operations: Operation[]  
    signatures: DecoratedSignature[] 
}`
```


**序列化格式**：XDR (External Data Representation) 

**签名流程**：
1. 构建交易对象
2. XDR 序列化
3. SHA-256 哈希
4. Ed25519 签名

### 2.8 代币与 NFT

**原生资产支持**：✅ 完全支持 
**协议标准**：Stellar Asset Contract (SAC)

**功能特点：**

- **资产发行**：任何账户都可发行自定义资产
- **信任线机制**：接收方需明确信任资产发行方
- **资产控制**：发行方可设置授权、冻结等标志
- **智能合约代币**：支持基于 Soroban 的代币合约

**NFT 支持**：✅ 通过智能合约实现 
**实现方式**：使用 Soroban 智能合约平台

### 2.9 Memo/Tag 支持

**支持状态**：✅ 完全支持

**Memo 类型：**

- **MEMO_TEXT**：最多 28 字节的文本
- **MEMO_ID**：64 位无符号整数
- **MEMO_HASH**：32 字节哈希值
- **MEMO_RETURN**：32 字节返回哈希

**用途：**

- 交易所充值标识
- 支付备注信息
- 智能合约参数传递

**链接：**

- [Stellar 数据结构文档](https://developers.stellar.org/docs/learn/fundamentals/stellar-data-structures)
- [Stellar 共识协议白皮书](https://stellar.org/learn/stellar-consensus-protocol)

## 3. 费用模型

Stellar 采用双重费用机制：**包含费用 (Inclusion Fee)** + **资源费用 (Resource Fee)**

### 3.1 包含费用 (Inclusion Fee)

**计算公式**：包含费用 = 操作数量 × 有效基础费用

**基础参数：**

- **网络最低费用**：100 stroops/操作 (0.00001 XLM)
- **最大操作数**：100 操作/交易（智能合约交易限制为 1 操作）

**动态定价机制：**

- **正常情况**：支付最低费用 100 stroops
- **网络拥堵**：进入激增定价模式，按出价竞争

### 3.2 资源费用 (Resource Fee)

**适用范围**：仅智能合约交易

**资源类型：**

- **CPU 指令**：合约执行消耗的计算资源
- **账本条目访问**：读写账本数据的次数
- **账本 I/O**：读写的字节数
- **交易大小**：交易本身的字节大小
- **事件和返回值**：合约产生的事件和返回数据大小
- **账本空间租金**：数据存储的租金费用

**费用组成：**

- **不可退还费用**：CPU、读写、带宽费用
- **可退还费用**：租金、事件、返回值费用（按实际使用退还）

### 3.3 存储动态定价

**机制**：基于账本大小的动态写入费用

- 账本小于阈值：费用逐渐增长
- 超过阈值：费用线性增长（1000倍因子）

**链接：**

- [Stellar 费用和计量文档](https://developers.stellar.org/docs/learn/fundamentals/fees-resource-limits-metering)
- [网络资源限制和费用](https://developers.stellar.org/docs/networks/resource-limits-fees)

## 4. 历史回滚事件

### 4.1 2019年5月15日网络中断事件

**事件概述**：Stellar 网络在 2019 年 5 月 15 日 5:00 AM (UTC) 停止运行 67 分钟

**事件原因**：

- SCP 共识协议在特定节点配置下的活跃性问题
- 网络中关键验证节点的配置导致无法达成共识

**影响范围**：

- 全网交易处理暂停
- 新区块生成停止
- 网络完全不可用

**解决措施**：

- 调整验证节点配置
- 优化 SCP 协议参数
- 加强网络监控机制

**后续改进**：

- 增强网络韧性
- 改进节点配置指导
- 建立更好的故障恢复机制

**链接：**

- [SYSSEC 研究报告](https://www.syssec.kr/publications)
- [网络中断分析文章](https://www.publish0x.com/cryptocurrency/what-is-stellar-xlm-a-comprehensive-guide-to-understanding-s-xmyexr)

### 4.2 其他稳定性记录

**总体评估**：除 2019 年事件外，Stellar 网络保持了良好的稳定性记录 **运行时间**：自 2014 年以来整体可用性超过 99.9%

## 5. 同源链

### 5.1 与 Ripple (XRP) 的关系

**历史关联**：Stellar 最初是 Ripple 的分叉项目

**共同创始人**：Jed McCaleb（同时创立了 Ripple 和 Stellar）

**分叉时间**：2014 年

**主要差异：**

|特性|Stellar (XLM)|Ripple (XRP)|
|---|---|---|
|共识算法|SCP (联邦拜占庭协议)|RPCA (Ripple 协议共识算法)|
|网络开放性|完全开放，任何人可运行节点|相对封闭，UNL 节点控制|
|目标市场|个人和小企业跨境支付|银行和金融机构|
|智能合约|支持 (Soroban)|有限支持|
|去中心化程度|更高|相对较低|

### 5.2 技术演进差异

**Stellar 独特发展：**

- 开发了独特的 SCP 共识算法
- 集成了 Soroban 智能合约平台
- 专注于普惠金融和去中心化

**竞争优势：**

- 更开放的网络架构
- 更低的交易费用
- 更强的去中心化特性
- 完整的智能合约支持

**链接：**

- [Stellar vs Ripple 对比分析](https://www.quora.com/Why-did-Jed-McCaleb-leave-Ripple-to-start-Stellar)
- [Stellar 技术介绍](https://medium.com/@alexprut/stellar-xlm-quick-introduction-2498f478473e)

## 6. RPC 接口

Stellar 提供完整的 RPC API 接口，支持所有区块链交互需求。

### 6.1 核心 RPC 方法

#### 6.1.1 网络状态查询

**getHealth**

- **功能**：获取节点健康状态
- **参数**：无
- **返回**：节点状态信息
- **Request:**
```json
curl --location 'https://soroban-testnet.stellar.org' \
--header 'Content-Type: application/json' \
--data '{
  "jsonrpc": "2.0",
  "id": 8675309,
  "method": "getHealth"
}'
```
- **Response:**
```json
{
    "jsonrpc": "2.0",
    "id": 8675309,
    "result": {
        "status": "healthy",
        "latestLedger": 404765,
        "oldestLedger": 283806,
        "ledgerRetentionWindow": 120960
    }
}
```


**getLatestLedger**

- **功能**：获取最新区块信息
- **参数**：无
- **返回**：最新区块高度、哈希、时间戳
- **Request:**
```json
curl --location 'https://soroban-testnet.stellar.org' \
--header 'Content-Type: application/json' \
--data '{
  "jsonrpc": "2.0",
  "id": 8675309,
  "method": "getLatestLedger"
}'
```
- **Response:**
```json
{
    "jsonrpc": "2.0",
    "id": 8675309,
    "result": {
        "id": "5f0735124d23a552da2c472924eacb2a85cc989f937242b1c7620c1e76bf4a7f",
        "protocolVersion": 22,
        "sequence": 404789
    }
}
```

**getNetwork**

- **功能**：获取网络配置信息
- **参数**：无
- **返回**：网络 ID、协议版本等
- **Request:**
```json
curl --location 'https://soroban-testnet.stellar.org' \
--header 'Content-Type: application/json' \
--data '{
  "jsonrpc": "2.0",
  "id": 8675309,
  "method": "getNetwork"
}'
```
- **Response:**
```json
{
    "jsonrpc": "2.0",
    "id": 8675309,
    "result": {
        "friendbotUrl": "https://friendbot.stellar.org/",
        "passphrase": "Test SDF Network ; September 2015",
        "protocolVersion": 22
    }
}
```



#### 6.1.2 区块和账本查询

**getLedgers**

- **功能**：获取区块列表和详细信息
- **参数**：startLedger, limit, cursor
- **返回**：区块元数据、交易列表
- **Request:**
```json
curl --location 'https://soroban-testnet.stellar.org' \
--header 'Content-Type: application/json' \
--data '{
  "jsonrpc": "2.0",
  "id": 8675309,
  "method": "getLedgers",
  "params": {
    "startLedger": 404789,
    "pagination": {
      "limit": 2
    }
  }
}'
```
- **Response:**
```json
{
    "jsonrpc": "2.0",
    "id": 8675309,
    "result": {
        "ledgers": [
            {
                "hash": "5f0735124d23a552da2c472924eacb2a85cc989f937242b1c7620c1e76bf4a7f",
                "sequence": 404789,
                "ledgerCloseTime": "1752293199",
                "headerXdr": "Xwc1Ek0jpVLaLEcpJOrLKoXMmJ+TckKxx2IMHna/Sn8AAAAW7n3m8cYGRst4Op8cXPcnroEAZp7vMQVGrsZ4UYuoa/k1AKfjGsiCZ484xPjlEkEEjnslQIr0QYmrG1fUa2IzxwAAAABocd9PAAAAAAAAAAEAAAAA1XJp2WJQ90ltB5vi6DUSNu//6NOcLga/q7FCHxf8ZxwAAABABIyR0w1BQoTCZVd7z/Yc/b5d9k4T6ismNff+KhgYgmMKnfbp9IzQEvPsASS1xjMboy7C99A2tMH6MA/g8ZaNAd8/YZgEqS/bQFcZLcQ910jqd4rcUrxJjOgFJMAUuBEZbZK0+TSiT9k4f6x8vgzc8TG/sunoTqvAsWrAEzy512MABi01DeC2s6dkAAAAAAFXGjYMZQAAAAAAAAAAAAAKEQAAAGQATEtAAAAAyLipRAb7L3wNG3NC9FfG0HI2AVuJCpY7hSW/ptouXpcDBCi3ONy+J+KK7DW+IGsCPGuudZhceOzI49p8cYGMzK/HxcBhueENDaDnmjFzttbI2LS47RZYkDk52XsEDAOyOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=",
                "metadataXdr": "AAAAAQAAAABfBzUSTSOlUtosRykk6ssqhcyYn5NyQrHHYgwedr9KfwAAABbufebxxgZGy3g6nxxc9yeugQBmnu8xBUauxnhRi6hr+TUAp+MayIJnjzjE+OUSQQSOeyVAivRBiasbV9RrYjPHAAAAAGhx308AAAAAAAAAAQAAAADVcmnZYlD3SW0Hm+LoNRI27//o05wuBr+rsUIfF/xnHAAAAEAEjJHTDUFChMJlV3vP9hz9vl32ThPqKyY19/4qGBiCYwqd9un0jNAS8+wBJLXGMxujLsL30Da0wfowD+Dxlo0B3z9hmASpL9tAVxktxD3XSOp3itxSvEmM6AUkwBS4ERltkrT5NKJP2Th/rHy+DNzxMb+y6ehOq8CxasATPLnXYwAGLTUN4Lazp2QAAAAAAVcaNgxlAAAAAAAAAAAAAAoRAAAAZABMS0AAAADIuKlEBvsvfA0bc0L0V8bQcjYBW4kKljuFJb+m2i5elwMEKLc43L4n4orsNb4gawI8a651mFx47Mjj2nxxgYzMr8fFwGG54Q0NoOeaMXO21sjYtLjtFliQOTnZewQMA7I4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHufebxxgZGy3g6nxxc9yeugQBmnu8xBUauxnhRi6hr+QAAAAIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABANQmkAAAAAAAAAAA=="
            }
        ],
        "latestLedger": 404888,
        "latestLedgerCloseTime": 1752293694,
        "oldestLedger": 283929,
        "oldestLedgerCloseTime": 1751688396,
        "cursor": "404790"
    }
}
```



**getLedgerEntries**

- **功能**：获取账本条目（账户、合约数据等）
- **参数**：keys (账本条目键列表)
- **返回**：账本条目详细信息
- **用途**：查询账户余额、合约状态、资产信息
- **Request:**

- **Response:**


#### 6.1.3 交易相关

**getTransaction**

- **功能**：根据交易哈希查询交易详情
- **参数**：hash (交易哈希)
- **返回**：完整交易信息、执行结果
- **Request:**
```json
curl --location 'https://soroban-testnet.stellar.org' \
--header 'Content-Type: application/json' \
--data '{
  "jsonrpc": "2.0",
  "id": 8675309,
  "method": "getTransaction",
  "params": {
    "hash": "2f587d4e6b1686d864c587695f1a4320646c258e928568e6f966dd1260ad1a56"
  }
}'
```
- **Response:**
```json
{
    "jsonrpc": "2.0",
    "id": 8675309,
    "result": {
        "latestLedger": 405044,
        "latestLedgerCloseTime": "1752294475",
        "oldestLedger": 284085,
        "oldestLedgerCloseTime": "1751689176",
        "status": "SUCCESS",
        "txHash": "2f587d4e6b1686d864c587695f1a4320646c258e928568e6f966dd1260ad1a56",
        "applicationOrder": 1,
        "feeBump": false,
        "envelopeXdr": "AAAAAgAAAABPXmarz7LRzb/kLiaEROekV/tAWYtWFYRjxrWXHb3v3wAAAGQABfG0AAAADgAAAAEAAAAAAAAAAAAAAABocd3WAAAAAAAAAAEAAAAAAAAAAQAAAABVkLbqeKDg97hm/h4mFSVHLGamyl27Iu74tXIlkirnBAAAAAAAAAAAAJiWgAAAAAAAAAABHb3v3wAAAEB7faDspyFJ1tncbUE4qc/+fzHOJvrBH+yOvw5bF34yzyTE6UzY8pzPUzO78aXCoxHutFDLALpjF7cMn48rfiYP",
        "resultXdr": "AAAAAAAAAGQAAAAAAAAAAQAAAAAAAAABAAAAAAAAAAA=",
        "resultMetaXdr": "AAAAAwAAAAAAAAACAAAAAwAGLOgAAAAAAAAAAE9eZqvPstHNv+QuJoRE56RX+0BZi1YVhGPGtZcdve/fAAAAF0C3PggABfG0AAAADQAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAAAAAAAMAAAAAAAYsuwAAAABocdzsAAAAAAAAAAEABizoAAAAAAAAAABPXmarz7LRzb/kLiaEROekV/tAWYtWFYRjxrWXHb3v3wAAABdAtz4IAAXxtAAAAA4AAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAADAAAAAAAGLOgAAAAAaHHdzgAAAAAAAAABAAAABAAAAAMABizoAAAAAAAAAABPXmarz7LRzb/kLiaEROekV/tAWYtWFYRjxrWXHb3v3wAAABdAtz4IAAXxtAAAAA4AAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAADAAAAAAAGLOgAAAAAaHHdzgAAAAAAAAABAAYs6AAAAAAAAAAAT15mq8+y0c2/5C4mhETnpFf7QFmLVhWEY8a1lx29798AAAAXQB6niAAF8bQAAAAOAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAwAAAAAABizoAAAAAGhx3c4AAAAAAAAAAwAGLLsAAAAAAAAAAFWQtup4oOD3uGb+HiYVJUcsZqbKXbsi7vi1ciWSKucEAAAAF1A2jIAABb/SAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAQAGLOgAAAAAAAAAAFWQtup4oOD3uGb+HiYVJUcsZqbKXbsi7vi1ciWSKucEAAAAF1DPIwAABb/SAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=",
        "ledger": 404712,
        "createdAt": "1752292814"
    }
}
```


**getTransactions**

- **功能**：查询交易列表
- **参数**：startLedger, filters, limit
- **返回**：交易列表和详细信息
- **Request:**
```json
curl --location 'https://soroban-testnet.stellar.org' \
--header 'Content-Type: application/json' \
--data '{
  "jsonrpc": "2.0",
  "id": 8675309,
  "method": "getTransactions",
  "params": {
    "startLedger": 404712,
    "pagination": {
      "limit": 5
    }
  }
}'
```
- **Response:**
```json
{
    "jsonrpc": "2.0",
    "id": 8675309,
    "result": {
        "transactions": [
            {
                "status": "SUCCESS",
                "txHash": "2f587d4e6b1686d864c587695f1a4320646c258e928568e6f966dd1260ad1a56",
                "applicationOrder": 1,
                "feeBump": false,
                "envelopeXdr": "AAAAAgAAAABPXmarz7LRzb/kLiaEROekV/tAWYtWFYRjxrWXHb3v3wAAAGQABfG0AAAADgAAAAEAAAAAAAAAAAAAAABocd3WAAAAAAAAAAEAAAAAAAAAAQAAAABVkLbqeKDg97hm/h4mFSVHLGamyl27Iu74tXIlkirnBAAAAAAAAAAAAJiWgAAAAAAAAAABHb3v3wAAAEB7faDspyFJ1tncbUE4qc/+fzHOJvrBH+yOvw5bF34yzyTE6UzY8pzPUzO78aXCoxHutFDLALpjF7cMn48rfiYP",
                "resultXdr": "AAAAAAAAAGQAAAAAAAAAAQAAAAAAAAABAAAAAAAAAAA=",
                "resultMetaXdr": "AAAAAwAAAAAAAAACAAAAAwAGLOgAAAAAAAAAAE9eZqvPstHNv+QuJoRE56RX+0BZi1YVhGPGtZcdve/fAAAAF0C3PggABfG0AAAADQAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAAAAAAAMAAAAAAAYsuwAAAABocdzsAAAAAAAAAAEABizoAAAAAAAAAABPXmarz7LRzb/kLiaEROekV/tAWYtWFYRjxrWXHb3v3wAAABdAtz4IAAXxtAAAAA4AAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAADAAAAAAAGLOgAAAAAaHHdzgAAAAAAAAABAAAABAAAAAMABizoAAAAAAAAAABPXmarz7LRzb/kLiaEROekV/tAWYtWFYRjxrWXHb3v3wAAABdAtz4IAAXxtAAAAA4AAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAADAAAAAAAGLOgAAAAAaHHdzgAAAAAAAAABAAYs6AAAAAAAAAAAT15mq8+y0c2/5C4mhETnpFf7QFmLVhWEY8a1lx29798AAAAXQB6niAAF8bQAAAAOAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAwAAAAAABizoAAAAAGhx3c4AAAAAAAAAAwAGLLsAAAAAAAAAAFWQtup4oOD3uGb+HiYVJUcsZqbKXbsi7vi1ciWSKucEAAAAF1A2jIAABb/SAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAQAGLOgAAAAAAAAAAFWQtup4oOD3uGb+HiYVJUcsZqbKXbsi7vi1ciWSKucEAAAAF1DPIwAABb/SAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=",
                "ledger": 404712,
                "createdAt": 1752292814
            },
            {
                "status": "FAILED",
                "txHash": "7036180eebdcaa07e3e9ec94272f0fc4d6dd6d36fd121df35ed08f557c966b90",
                "applicationOrder": 1,
                "feeBump": false,
                "envelopeXdr": "AAAAAgAAAAA4GPHCwI6POYUt5FMvTaKYlD+KQsPy+wuVYYAYs9VXOACYloAAAAxmAAAATgAAAAEAAAAAAAAAAAAAAABocd4bAAAAAQAAABBfUkVGVU5EXzNiMzFkODc5AAAAAQAAAAEAAAAArsnT8n12eYgZfe9AaabnLVyOP3kkRycxsS2EPuj4n0AAAAABAAAAAAXpDljIm81YeQxKLefLAt3xEW+9A3ZSVhIwC1u7vlrNAAAAAVVTRAAAAAAAGiUU1rgIxPdmf6qCHQeIiu2JBuoH3liO/gcd6x5xuN0AAAAAA3UCgAAAAAAAAAACs9VXOAAAAEAh0r3L6IpqbD4VzI7fD8bMYZXIyJJJ4o+ClV09J4/aiqQp/c5Epakc+WBaGN6Dq5NRY6/Ab3RymPQfsZ4ZybMO6PifQAAAAEBRDpvLqW7AvqmrkAU0s00vc7kVDwSUXPOxsZpfpXq23+SBbGP46Lzp1ISBu+yW+uDH38cRrtgDc2yzCBZgXNgG",
                "resultXdr": "AAAAAAAAAGT/////AAAAAQAAAAAAAAAB/////gAAAAA=",
                "resultMetaXdr": "AAAAAwAAAAAAAAACAAAAAwAGLOwAAAAAAAAAADgY8cLAjo85hS3kUy9NopiUP4pCw/L7C5VhgBiz1Vc4AAAAF0h2xswAAAxmAAAATQAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAAAAAAAMAAAAAAAYr6wAAAABocdjbAAAAAAAAAAEABizsAAAAAAAAAAA4GPHCwI6POYUt5FMvTaKYlD+KQsPy+wuVYYAYs9VXOAAAABdIdsbMAAAMZgAAAE4AAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAADAAAAAAAGLOwAAAAAaHHd4gAAAAAAAAAAAAAAAAAAAAA=",
                "ledger": 404716,
                "createdAt": 1752292834
            }
        ],
        "latestLedger": 405063,
        "latestLedgerCloseTimestamp": 1752294570,
        "oldestLedger": 284104,
        "oldestLedgerCloseTimestamp": 1751689272,
        "cursor": "1738241984172033"
    }
}
```

**sendTransaction**

- **功能**：提交交易到网络
- **参数**：transaction (XDR 编码的交易)
- **返回**：交易哈希、状态
- **Request:**
```json
curl --location 'https://soroban-testnet.stellar.org' \
--header 'Content-Type: application/json' \
--data '{
    "jsonrpc": "2.0",
    "id": 8675309,
    "method": "sendTransaction",
    "params": {
        "transaction": "AAAAAgAAAABPXmarz7LRzb/kLiaEROekV/tAWYtWFYRjxrWXHb3v3wAAAGQABfG0AAAADgAAAAEAAAAAAAAAAAAAAABocd3WAAAAAAAAAAEAAAAAAAAAAQAAAABVkLbqeKDg97hm/h4mFSVHLGamyl27Iu74tXIlkirnBAAAAAAAAAAAAJiWgAAAAAAAAAABHb3v3wAAAEB7faDspyFJ1tncbUE4qc/+fzHOJvrBH+yOvw5bF34yzyTE6UzY8pzPUzO78aXCoxHutFDLALpjF7cMn48rfiYP",
        "xdrFormat": "base64"
    }
}'
```
- **Response:**
```json
{
    "jsonrpc": "2.0",
    "id": 8675309,
    "result": {
        "status": "PENDING",
        "hash": "2f587d4e6b1686d864c587695f1a4320646c258e928568e6f966dd1260ad1a56",
        "latestLedger": 404711,
        "latestLedgerCloseTime": "1752292809"
    }
}
```

**simulateTransaction**

- **功能**：模拟交易执行（不实际提交）
- **参数**：transaction, resourceConfig
- **返回**：预估费用、资源消耗、执行结果
- **用途**：交易签名前的费用估算和验证
- **Request:**
- **Response:**


#### 6.1.4 事件和费用

**getEvents**

- **功能**：查询智能合约事件
- **参数**：startLedger, filters, topics
- **返回**：事件列表和详细数据
- **Request:**
- **Response:**

**getFeeStats**

- **功能**：获取网络费用统计
- **参数**：无
- **返回**：当前费用水平、历史统计
- **Request:**
```json
curl --location 'https://soroban-testnet.stellar.org' \
--header 'Content-Type: application/json' \
--data '{
  "jsonrpc": "2.0",
  "id": 8675309,
  "method": "getFeeStats"
}'
```
- **Response:**
```json
{
    "jsonrpc": "2.0",
    "id": 8675309,
    "result": {
        "sorobanInclusionFee": {
            "max": "100",
            "min": "100",
            "mode": "100",
            "p10": "100",
            "p20": "100",
            "p30": "100",
            "p40": "100",
            "p50": "100",
            "p60": "100",
            "p70": "100",
            "p80": "100",
            "p90": "100",
            "p95": "100",
            "p99": "100",
            "transactionCount": "3",
            "ledgerCount": 50
        },
        "inclusionFee": {
            "max": "100",
            "min": "100",
            "mode": "100",
            "p10": "100",
            "p20": "100",
            "p30": "100",
            "p40": "100",
            "p50": "100",
            "p60": "100",
            "p70": "100",
            "p80": "100",
            "p90": "100",
            "p95": "100",
            "p99": "100",
            "transactionCount": "6",
            "ledgerCount": 10
        },
        "latestLedger": 405101
    }
}
```
#### 6.1.5 版本信息

**getVersionInfo**

- **功能**：获取 RPC 服务版本信息
- **参数**：无
- **返回**：版本号、协议支持情况
- **Request:**
- **Response:**


### 6.2 特殊功能说明

**余额查询**：通过 `getLedgerEntries` 查询账户条目 **交易记录**：通过 `getTransactions` 按地址或时间范围查询 **签名参数**：通过 `simulateTransaction` 获取所需的序列号、费用等参数

### 6.3 第三方数据平台

由于 RPC 主要面向实时数据，历史数据查询建议使用：

- **Stellar Expert**：[https://stellar.expert](https://stellar.expert/)
- **StellarChain**：[https://stellarchain.io](https://stellarchain.io/)
- **Hubble**：Stellar 官方数据分析平台

**链接：**

- [RPC API 参考文档](https://developers.stellar.org/docs/data/apis/rpc/api-reference/methods)
- [RPC 方法详细说明](https://developers.stellar.org/docs/data/apis/rpc)

## 7. JavaScript SDK

### 7.1 官方 SDK 信息

**SDK 名称**：@stellar/stellar-sdk 
**维护方**：Stellar Development Foundation (SDF) 官方维护 
**开发语言**：JavaScript/TypeScript 
**运行环境**：浏览器和 Node.js


**GitHub 仓库**：[https://github.com/stellar/js-stellar-sdk](https://github.com/stellar/js-stellar-sdk) 
**文档地址**：[https://stellar.github.io/js-stellar-sdk/](https://stellar.github.io/js-stellar-sdk/) 
**NPM 地址**：[https://www.npmjs.com/package/@stellar/stellar-sdk](https://www.npmjs.com/package/@stellar/stellar-sdk)
