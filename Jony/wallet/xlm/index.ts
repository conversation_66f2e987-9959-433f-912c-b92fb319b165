import * as StellarSdk from 'stellar-sdk';
import { derivePath } from 'ed25519-hd-key';

// Stellar 网络配置
const STELLAR_NETWORKS = {
  mainnet: {
    networkPassphrase: StellarSdk.Networks.PUBLIC,
    horizonUrl: 'https://horizon.stellar.org'
  },
  testnet: {
    networkPassphrase: StellarSdk.Networks.TESTNET,
    horizonUrl: 'https://horizon-testnet.stellar.org'
  }
};

/**
 * 从助记词创建 Stellar 地址
 * @param seedHex 种子的十六进制字符串
 * @param receiveOrChange 接收或找零 (通常为 "0")
 * @param addressIndex 地址索引
 * @param network 网络类型 ("mainnet" 或 "testnet")
 * @returns 包含私钥、公钥和地址的对象
 */
export function createXlmAddress(
  seedHex: string,
  receiveOrChange: string,
): any {
  try {
    // 构建完整的派生路径
    const fullPath = `m/44'/148'/${receiveOrChange}'`;

    // 从种子派生密钥
    const { key } = derivePath(fullPath, seedHex);

    // 创建 Stellar 密钥对
    const keypair = StellarSdk.Keypair.fromRawEd25519Seed(key);

    return {
      privateKey: keypair.secret(),
      publicKey: keypair.publicKey(),
      address: keypair.publicKey()
    };
  } catch (error: any) {
    throw new Error(`创建 XLM 地址失败: ${error.message}`);
  }
}

/**
 * 从私钥导入 Stellar 地址
 * @param params 包含私钥和网络的参数对象
 * @returns Stellar 地址
 */
export function importXlmAddress(params: { privateKey: string }): string {
  try {
    const { privateKey } = params;

    // 从私钥创建密钥对
    const keypair = StellarSdk.Keypair.fromSecret(privateKey);

    return keypair.publicKey();
  } catch (error: any) {
    throw new Error(`导入 XLM 地址失败: ${error.message}`);
  }
}

/**
 * 验证 Stellar 地址格式
 * @param params 包含地址和网络的参数对象
 * @returns 验证结果 (true/false)
 */
export function verifyXlmAddress(params: { address: string}): boolean {
  try {
    const { address } = params;

    // Stellar 地址应该以 G 开头，长度为 56 个字符
    if (!address || address.length !== 56 || !address.startsWith('G')) {
      return false;
    }

    // 使用 Stellar SDK 验证地址格式
    StellarSdk.Keypair.fromPublicKey(address);
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 获取账户信息
 * @param publicKey 公钥
 * @param network 网络
 * @returns 账户信息
 */
export async function getAccountInfo(publicKey: string, network: string): Promise<any> {
  try {
    const networkConfig = STELLAR_NETWORKS[network as keyof typeof STELLAR_NETWORKS];
    if (!networkConfig) {
      throw new Error(`不支持的网络: ${network}`);
    }

    const server = new StellarSdk.Server(networkConfig.horizonUrl);

    // 先尝试直接加载账户
    try {
      const account = await server.loadAccount(publicKey);
      return account;
    } catch (loadError: any) {
      // 如果直接加载失败，尝试通过 accounts API 查询
      if (loadError.response && loadError.response.status === 404) {
        console.log('账户可能未激活，尝试其他方式查询...');

        // 尝试查询账户的交易记录来验证账户是否存在
        const transactions = await server.transactions().forAccount(publicKey).limit(1).call();

        if (transactions.records.length > 0) {
          // 账户有交易记录，说明存在，但可能是特殊状态
          throw new Error('账户存在但无法直接加载，可能需要重新激活');
        } else {
          throw new Error('账户不存在');
        }
      }
      throw loadError;
    }
  } catch (error: any) {
    throw new Error(`获取账户信息失败: ${error.message}`);
  }
}


/**
 * 构建XLM交易
 * @param params 交易参数
 * @returns 构建的交易对象
 */
export async function buildXlmTransaction(params: {
  sourcePublicKey: string;
  destination: string;
  amount: string;
  memo?: { type: string; value: string };
  network: string;
  fee?: string;
}): Promise<any> {
  try {
    const { sourcePublicKey, destination, amount, memo, network, fee } = params;

    // 获取网络配置
    const networkConfig = STELLAR_NETWORKS[network as keyof typeof STELLAR_NETWORKS];
    if (!networkConfig) {
      throw new Error(`不支持的网络: ${network}`);
    }

    // 获取源账户信息
    const sourceAccount = await getAccountInfo(sourcePublicKey, network);

    // 创建交易构建器
    const transactionBuilder = new StellarSdk.TransactionBuilder(sourceAccount, {
      fee: fee || StellarSdk.BASE_FEE,
      networkPassphrase: networkConfig.networkPassphrase
    });

    // 添加支付操作
    transactionBuilder.addOperation(
      StellarSdk.Operation.payment({
        destination: destination,
        asset: StellarSdk.Asset.native(),
        amount: amount
      })
    );

    // 添加操作
    // signObj.operations.forEach((operation: any) => {
    //   switch (operation.type) {
    //     case 'payment':
    //       transactionBuilder.addOperation(
    //           StellarSdk.Operation.payment({
    //             destination: operation.destination,
    //             asset: operation.asset || StellarSdk.Asset.native(),
    //             amount: operation.amount,
    //             source: operation.source
    //           })
    //       );
    //       break;
    //     case 'createAccount':
    //       transactionBuilder.addOperation(
    //           StellarSdk.Operation.createAccount({
    //             destination: operation.destination,
    //             startingBalance: operation.startingBalance,
    //             source: operation.source
    //           })
    //       );
    //       break;
    //     case 'changeTrust':
    //       transactionBuilder.addOperation(
    //           StellarSdk.Operation.changeTrust({
    //             asset: operation.asset,
    //             limit: operation.limit,
    //             source: operation.source
    //           })
    //       );
    //       break;
    //     default:
    //       throw new Error(`不支持的操作类型: ${operation.type}`);
    //   }
    // });


    // 添加memo（如果存在）
    if (memo) {
      switch (memo.type) {
        case 'text':
          transactionBuilder.addMemo(StellarSdk.Memo.text(memo.value));
          break;
        case 'id':
          transactionBuilder.addMemo(StellarSdk.Memo.id(memo.value));
          break;
        case 'hash':
          transactionBuilder.addMemo(StellarSdk.Memo.hash(memo.value));
          break;
        case 'return':
          transactionBuilder.addMemo(StellarSdk.Memo.return(memo.value));
          break;
      }
    }

    // 构建交易
    const transaction = transactionBuilder.setTimeout(30).build();
    return transaction;
  } catch (error: any) {
    throw new Error(`构建 XLM 交易失败: ${error.message}`);
  }
}

/**
 * 签名交易（不提交到网络）
 * @param params 交易参数
 * @returns 签名后的交易 XDR
 */
export async function signXlmTransactionOnly(params: {
  privateKey: string;
  sourcePublicKey: string;
  destination: string;
  amount: string;
  memo?: { type: string; value: string };
  network: string;
  fee?: string;
}): Promise<string> {
  try {
    const { privateKey } = params;

    // 构建交易
    const transaction = await buildXlmTransaction(params);

    // 创建密钥对并签名
    const sourceKeypair = StellarSdk.Keypair.fromSecret(privateKey);
    transaction.sign(sourceKeypair);

    // 返回签名后的交易 XDR
    return transaction.toXDR();
  } catch (error: any) {
    throw new Error(`签名 XLM 交易失败: ${error.message}`);
  }
}







// // 从助记词生成种子
// export async function generateSeedFromMnemonic(mnemonic: string, password:string=''): string {
//   // 验证助记词
//   if (!bip39.validateMnemonic(mnemonic)) {
//     throw  new  Error('无效助记词');
//   }
//
//   // 生成种子
//   const seed = bip39.mnemonicToSeedSync(mnemonic, password);
//   return seed.toString('hex');
// }
//
// // 从种子派生密钥
// export async function deriveKeyFromSeed(seedHex:string, accountIndex:number = 0): any{
//   const fullPath = `m/44'/148'/${accountIndex}'/0'`;
//
//   // 使用 ed25519-hd-key 进行密钥派生
//   const  {key} = derivePath(fullPath, seedHex);
//   return key;     // 32字节的原始密钥
// }
//
// // 创建Steller 密钥对
// export async function createStellarKeypair(derivedKey:Buffer) : StellarSdk.Keypair {
//   // 从派生的密钥创建Stellar 密钥对
//   const keypair = StellarSdk.Keypair.fromRawEd25519Seed(derivedKey);
//   return keypair;
// }
//
//
// // 完整的地址生成函数
// export async function createXlmAddress (receiveOrChange:string, addressIndex:string, network:string):{privateKey:string, publicKey: string, address: string} {
//
//   const mnemonic = ''
//   const password = ''
//   // 验证助记词
//   if (!bip39.validateMnemonic(mnemonic)) {
//     throw  new  Error('无效助记词');
//   }
//
//   // 生成种子
//   const seedHex = bip39.mnemonicToSeedSync(mnemonic, password);
//
//   // 步骤1: 构造完整派生路径
//   const fullPath = `m/44'/148'/${receiveOrChange}'/${addressIndex}'`;
//
//   // 步骤2: 从种子派生密钥
//   const { key } = derivePath(fullPath, seedHex.toString('hex'))
//
//   // 步骤3: 创建Stellar密钥对
//   const keypair = StellarSdk.Keypair.fromRawEd25519Seed(key);
//
//   // 步骤4: 返回完整信息
//   return {
//     privateKey: keypair.secret(),   // 5开头的私钥
//     publicKey: keypair.publicKey(), // G开头的公钥
//     address : keypair.publicKey()   // 地址就是公钥
//   }
// }
//

//
// // 构建交易器
// export async function buildTransaction(params: {
//   sourceAccount: any,
//   operations: any[],
//   fee?: string,
//   memo?: any,
//   timeBounds? : any,
//   network: string
// }): StellarSdk.Transaction {
//   // 步骤1: 获取网络配置
//   const networkPassphrase = params.network = 'mainnet'? StellarSdk.Networks.PUBLIC: StellarSdk.Networks.TESTNET;
//
//   // 步骤2 ： 创建交易构造器
//   const transactionBuiler = new  StellarSdk.TransactionBuilder(
//       params.sourceAccount,
//       {
//         fee: params.fee || StellarSdk.BASE_FEE,
//         networkPassphrase: networkPassphrase,
//         timebounds : params.timeBounds
//       }
//   );
//
//   // 步骤3: 添加操作
//   params.operations.forEach(operation => {
//     transactionBuiler.addOperation(operation);
//   });
//
//   // 步骤4: 添加Memo (如果有)
//   if (params.memo) {
//     transactionBuiler.addMemo(params.memo)
//   }
//
//   // 步骤5 ： 构建交易
//   return  transactionBuiler.setTimeout(30).build();
// }


//
// // 完整的离线签名流程
// export async function signXlmTransaction(params: {
//   privateKey: string,
//   signObj: any,
//   network: string
// }): string {
//   try {
//     //步骤1 : 创建密钥对
//     const sourceKeypair = StellarSdk.Keypair.fromSecret(params.privateKey);
//
//     // 步骤2: 获取网络配置
//     const networkPassphrase = params.network = 'mainnet' ? StellarSdk.Networks.PUBLIC : StellarSdk.Networks.TESTNET;
//
//     // 步骤3: 创建交易构建器
//     const transactionBuilder = new StellarSdk.TransactionBuilder(
//         params.signObj.sourceAccount,
//         {
//           fee: params.signObj.fee || StellarSdk.BASE_FEE,
//           networkPassphrase: networkPassphrase,
//           timebounds: params.signObj.timebounds
//         }
//     );
//
//     // 步骤4 ：添加操作
//     params.signObj.operations.forEach((operation: any) => {
//       switch (operation.type) {
//         case 'payment':
//           transactionBuilder.addOperation(
//               StellarSdk.Operation.payment({
//                 destination: operation.destination,
//                 asset: operation.asset || StellarSdk.Asset.native(),
//                 amount: operation.amount
//               })
//           );
//           break;
//           // 可以添加更多操作类型
//       }
//     });
//     // 步骤5: 添加Memo
//     if (params.signObj.memo) {
//       switch (params.signObj.memo.type) {
//         case 'text':
//           transactionBuilder.addMemo(StellarSdk.Memo.text(params.signObj.memo.value));
//           break;
//         case 'id':
//           transactionBuilder.addMemo(StellarSdk.Memo.id(params.signObj.memo.value));
//           break;
//           // 添加其他的 memo类型
//       }
//     }
//     // 步骤6 ：构建交易
//     const transaction = transactionBuilder.setTimeout(30).build();
//
//     // 步骤7 ： 签名交易
//     transaction.sign(sourceKeypair)
//
//     // 步骤8 ：返回XDR
//     return transaction.toXDR();
//   }catch (error) {
//     throw  new Error(`签名失败：${error.message}`);
//   }
// }



