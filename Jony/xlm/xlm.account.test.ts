import { mnemonicToSeed } from "../wallet/bip/bip";
import {
  createXlmAddress,
  importXlmAddress,
  verifyXlmAddress,
  getAccountInfo,
} from "../wallet/xlm";

describe('xlm unit test case', () => {

  const MNEMOIC = "funny pill item type warfare account pumpkin level crop solid soul alcohol maze maid recipe quality ghost lawn tiny vote face solve wreck tenant";
  const PUBLICKEY = "GBHV4ZVLZ6ZNDTN74QXCNBCE46SFP62ALGFVMFMEMPDLLFY5XXX57MXR";
  const PRIVATEKEY = "SCTRBQMZOLKKJUTTGF62QIZSDRXNGFYKSGFSIBGBUBFNZMUPZJI6QEQB";

  const TESTNET_NETWORK = 'testnet';

  test('create xlm address', () => {
    const params_1 = {
      mnemonic: MNEMOIC,
      password: ""
    };
    const seedHex = mnemonicToSeed(params_1);
    const account = createXlmAddress(seedHex, "0");
    console.log('Created XLM Account:', account);
  });

  test('import xlm address', () => {
    const params = {
      privateKey: PRIVATEKEY
    };
    const address = importXlmAddress(params);
    console.log('Imported XLM Address:', address);
  });

  test('verify xlm address', () => {
    const validParams = {
      address: PUBLICKEY
    };
    const validResult = verifyXlmAddress(validParams);
    console.log('Valid address verification:', validResult);
  });

  test('account info', async () => {
    const testAddress = PUBLICKEY;

    try {
      const account = await getAccountInfo(testAddress, TESTNET_NETWORK);
      console.log(`Account Info :${testAddress}`, account);

    } catch (error: any) {
      console.log('GetAccount Error:', error.message);
    }
  });

});