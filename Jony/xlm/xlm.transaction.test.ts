import * as StellarSdk from 'stellar-sdk';
import {
  signXlmTransactionOnly,
} from "../wallet/xlm";

describe('XLM Transaction Test Cases', () => {
  
  // 测试网络配置
  const TESTNET_NETWORK = 'testnet';
  const TESTNET_SERVER = new StellarSdk.Server('https://horizon-testnet.stellar.org');
  
  // 测试账户信息（来自用户提供的链接）
  const SOURCE_PUBLIC_KEY = "GBHV4ZVLZ6ZNDTN74QXCNBCE46SFP62ALGFVMFMEMPDLLFY5XXX57MXR";
  const SOURCE_PRIVATE_KEY = "SCTRBQMZOLKKJUTTGF62QIZSDRXNGFYKSGFSIBGBUBFNZMUPZJI6QEQB";
  const DESTINATION_PUBLIC_KEY = "GBKZBNXKPCQOB55YM37B4JQVEVDSYZVGZJO3WIXO7C2XEJMSFLTQIV57";
  
  // 交易参数
  const PAYMENT_AMOUNT = "1.0000000";
  const TRANSACTION_MEMO = "transaction test";
  const TRANSACTION_FEE = "100";


  /**
   *  构建签名交易不提交 (Sign Transaction)
   */
  test('signTransaction - 签名支付交易', async () => {
    try {
      // 签名交易参数
      const signParams = {
        privateKey: SOURCE_PRIVATE_KEY,
        sourcePublicKey: SOURCE_PUBLIC_KEY,
        destination: DESTINATION_PUBLIC_KEY,
        amount: PAYMENT_AMOUNT,
        memo: { type: "test", value: TRANSACTION_MEMO },
        network: TESTNET_NETWORK,
        fee: TRANSACTION_FEE
      };

      console.log('🔐 开始签名交易...');
      const signedXDR = await signXlmTransactionOnly(signParams);
      
      console.log('\n✅ 交易签名成功');
      console.log('   交易签名 XDR示例:', signedXDR);

      // 验证XDR可以被解析
      const parsedTransaction = StellarSdk.TransactionBuilder.fromXDR(signedXDR, StellarSdk.Networks.TESTNET);
      console.log('   ✓ XDR解析验证通过');
      console.log('   解析后签名数量:', parsedTransaction);


    } catch (error: any) {
      console.log('❌ 签名交易失败:', error.message);
    }
  });


  /**
   * 提交交易 (Submit Transaction)
   */
  test('submitTransaction - 提交交易到网络', async () => {

    try {
      // 构建并签名交易
      const signParams = {
        privateKey: SOURCE_PRIVATE_KEY,
        sourcePublicKey: SOURCE_PUBLIC_KEY,
        destination: DESTINATION_PUBLIC_KEY,
        amount: PAYMENT_AMOUNT,
        memo: { type: "text", value: TRANSACTION_MEMO },
        network: TESTNET_NETWORK,
        fee: TRANSACTION_FEE
      };

      console.log('🚀 准备提交交易到测试网络...');
      const signedXDR = await signXlmTransactionOnly(signParams);
      console.log('签名后的交易 XDR: ', signedXDR);

      // 解析签名后的交易
      const transaction = StellarSdk.TransactionBuilder.fromXDR(signedXDR, StellarSdk.Networks.TESTNET);
      console.log('签名后的交易 XDR: ', transaction);

      // 提交交易到网络
      console.log('📡 提交交易到Stellar测试网络...');
      const submitResult = await TESTNET_SERVER.submitTransaction(transaction, { skipMemoRequiredCheck: true });

      console.log('\n🎉 交易提交成功！');
      console.log('   交易哈希:', submitResult.hash);
      console.log('   账本序号:', submitResult.ledger);
      console.log('   交易状态:', '成功');

      // 验证提交结果
      expect(submitResult).toBeDefined();
      expect(submitResult.hash).toBeDefined();

    } catch (error: any) {
      console.log('❌ 提交交易失败:', error.message);

    }
  });

});
