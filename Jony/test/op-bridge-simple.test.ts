// OP 桥接简单测试 - ETH 和 Token 转账、充值提现、挑战期测试

import { signTransaction } from "../../s56learner/Jony/wallet";
const ethers = require("ethers");

// 测试配置
const PRIVATE_KEY = "";
const FROM_ADDRESS = "******************************************";
const TO_ADDRESS = "******************************************";

// OP Sepolia 配置
const OP_CONFIG = {
  l1ChainId: 11155111, // Sepolia
  l2ChainId: 11155420, // OP Sepolia
  l1RpcUrl: 'https://ethereum-sepolia-rpc.publicnode.com',
  l2RpcUrl: 'https://endpoints.omniatech.io/v1/op/sepolia/public',
  l1StandardBridge: '******************************************', // Sepolia L1StandardBridgeProxy (测试网地址)
  l2StandardBridge: '******************************************',
  challengePeriodSeconds: 604800, // 7 days
};

// 获取 nonce
async function getNonce(address: string, rpcUrl: string): Promise<number> {
  try {
    const provider = new ethers.providers.JsonRpcProvider(rpcUrl);
    return await provider.getTransactionCount(address, 'pending');
  } catch (error) {
    return 0;
  }
}

// 发送交易
async function sendTx(signedTx: string, rpcUrl: string): Promise<any> {
  try {
    const provider = new ethers.providers.JsonRpcProvider(rpcUrl);
    const txResponse = await provider.sendTransaction(signedTx);
    const receipt = await txResponse.wait(1);
    
    return {
      success: true,
      txHash: txResponse.hash,
      receipt: receipt
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message
    };
  }
}


describe('OP 桥接简单测试', () => {

  // 1. OP Sepolia ETH 转账
  test("OP Sepolia ETH 转账", async () => {
    console.log('\n🔄 OP Sepolia ETH 转账测试');
    
    const nonce = await getNonce(FROM_ADDRESS, OP_CONFIG.l2RpcUrl);
    
    const signedTx = signTransaction({
      privateKey: PRIVATE_KEY,
      nonce: nonce,
      from: FROM_ADDRESS,
      to: TO_ADDRESS,
      gasLimit: 21000,
      maxFeePerGas: **********, // 1 Gwei
      maxPriorityFeePerGas: 100000000, // 0.1 Gwei
      gasPrice: 0,
      amount: "0.001",
      decimal: 18,
      chainId: OP_CONFIG.l2ChainId,
      tokenAddress: "0x00",
      callData: "",
    });
    
    console.log(`✅ 交易已签名`);
    
    const result = await sendTx(signedTx, OP_CONFIG.l2RpcUrl);
    
    if (result.success) {
      console.log(`✅ ETH 转账成功: ${result.txHash}`);
      console.log(`🔗 OP Sepolia: https://sepolia-optimism.etherscan.io/tx/${result.txHash}`);
    } else {
      console.log(`❌ ETH 转账失败: ${result.error}`);
    }
    
    expect(result).toBeDefined();
  }, 120000);

  // 2. ETH 充值 (L1 → L2)
  test("ETH 充值 (L1 → L2)", async () => {
    console.log('\n💰 ETH 充值测试');

    const nonce = await getNonce(FROM_ADDRESS, OP_CONFIG.l1RpcUrl);

    // 使用 depositETHTo 函数指定接收地址
    const { Interface } = require('@ethersproject/abi');
    const abi = ["function depositETHTo(address _to, uint32 _minGasLimit, bytes calldata _extraData) external payable"];
    const iface = new Interface(abi);
    const callData = iface.encodeFunctionData("depositETHTo", [
      TO_ADDRESS, // _to: L2 接收地址
      200000, // _minGasLimit
      "0x" // _extraData: 空数据
    ]);

    const signedTx = signTransaction({
      privateKey: PRIVATE_KEY,
      nonce: nonce,
      from: FROM_ADDRESS,
      to: OP_CONFIG.l1StandardBridge,
      gasLimit: 2000000, // 增加 gas 限制
      maxFeePerGas: 20000000000, // 20 Gwei
      maxPriorityFeePerGas: 2000000000, // 2 Gwei
      gasPrice: 0,
      amount: "0.001",
      decimal: 18,
      chainId: OP_CONFIG.l1ChainId,
      tokenAddress: "0x00",
      callData: callData,
    });

    console.log(`📝 调用数据: ${callData}`);
    console.log(`📝 目标合约: ${OP_CONFIG.l1StandardBridge}`);

    const result = await sendTx(signedTx, OP_CONFIG.l1RpcUrl);

    if (result.success) {
      console.log(`✅ ETH 充值成功: ${result.txHash}`);
      console.log(`🔗 Sepolia: https://sepolia.etherscan.io/tx/${result.txHash}`);
    } else {
      console.log(`❌ ETH 充值失败: ${result.error}`);
    }

    expect(result).toBeDefined();
  }, 120000);

  // 3. ETH 提现 (L2 → L1)
  test("ETH 提现 (L2 → L1)", async () => {
    console.log('\n💸 ETH 提现测试');
    
    const nonce = await getNonce(FROM_ADDRESS, OP_CONFIG.l2RpcUrl);
    
    // L2StandardBridge withdraw 调用数据
    const { Interface } = require('@ethersproject/abi');
    const abi = ["function withdraw(address _to, uint256 _amount, uint32 _minGasLimit, bytes _extraData) payable"];
    const iface = new Interface(abi);
    const amount = ethers.utils.parseEther("0.001");
    const callData = iface.encodeFunctionData("withdraw", [TO_ADDRESS, amount, 100000, "0x"]);
    
    const signedTx = signTransaction({
      privateKey: PRIVATE_KEY,
      nonce: nonce,
      from: FROM_ADDRESS,
      to: OP_CONFIG.l2StandardBridge,
      gasLimit: 2000000,
      maxFeePerGas: **********, // 1 Gwei
      maxPriorityFeePerGas: 100000000, // 0.1 Gwei
      gasPrice: 0,
      amount: "0.001",
      decimal: 18,
      chainId: OP_CONFIG.l2ChainId,
      tokenAddress: "0x00",
      callData: callData,
    });
    
    const result = await sendTx(signedTx, OP_CONFIG.l2RpcUrl);
    
    if (result.success) {
      console.log(`✅ ETH 提现发起成功: ${result.txHash}`);
      console.log(`🔗 OP Sepolia: https://sepolia-optimism.etherscan.io/tx/${result.txHash}`);
      console.log(`⚠️  需要等待 7 天挑战期`);
    } else {
      console.log(`❌ ETH 提现失败: ${result.error}`);
    }
    
    expect(result).toBeDefined();
  }, 120000);

  // 4. Token 转账
  test("Token 转账", async () => {
    console.log('\n🪙 Token 转账测试');
    
    const nonce = await getNonce(FROM_ADDRESS, OP_CONFIG.l2RpcUrl);
    const tokenAddress = "******************************************"; // WETH on OP Sepolia
    
    const signedTx = signTransaction({
      privateKey: PRIVATE_KEY,
      nonce: nonce,
      from: FROM_ADDRESS,
      to: TO_ADDRESS,
      gasLimit: 100000,
      maxFeePerGas: **********,
      maxPriorityFeePerGas: 100000000,
      gasPrice: 0,
      amount: "0.001",
      decimal: 18,
      chainId: OP_CONFIG.l2ChainId,
      tokenAddress: tokenAddress,
      callData: "",
    });
    
    const result = await sendTx(signedTx, OP_CONFIG.l2RpcUrl);
    
    if (result.success) {
      console.log(`✅ Token 转账成功: ${result.txHash}`);
      console.log(`🔗 OP Sepolia: https://sepolia-optimism.etherscan.io/tx/${result.txHash}`);
    } else {
      console.log(`❌ Token 转账失败: ${result.error}`);
    }
    
    expect(result).toBeDefined();
  }, 120000);


  // 6. 使用指南
  test("使用指南", () => {
    console.log('\n📋 OP 桥接测试使用指南');
    console.log('');
    console.log('🔧 启用测试:');
    console.log('');
    console.log('🌉 测试功能:');
    console.log('✅ ETH 转账 (OP Sepolia)');
    console.log('💰 ETH 充值 (L1 → L2)');
    console.log('💸 ETH 提现 (L2 → L1)');
    console.log('🪙 Token 转账');
    expect(true).toBe(true);
  });
});
