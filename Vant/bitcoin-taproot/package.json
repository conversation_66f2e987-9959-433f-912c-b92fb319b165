{"name": "bitcoin-taproot", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/the-wbe3/bitcoin-taproot.git"}, "author": "seek.guo", "license": "MIT", "bugs": {"url": "https://github.com/the-wbe3/bitcoin-taproot"}, "homepage": "https://github.com/the-wbe3/bitcoin-taproot#readme", "dependencies": {"@noble/hashes": "^1.2.0", "bech32": "^2.0.0", "bip174": "^2.1.1", "bitcoinjs-lib": "^6.1.6", "bs58check": "^3.0.1", "typeforce": "^1.11.3", "varuint-bitcoin": "^1.1.2"}, "devDependencies": {"@jest/globals": "^30.0.4", "@types/bs58": "^4.0.0", "@types/bs58check": "^2.1.0", "@types/jest": "^30.0.0", "@types/mocha": "^5.2.7", "@types/node": "^16.11.7", "@types/proxyquire": "^1.3.28", "@types/randombytes": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "better-npm-audit": "^3.7.3", "bip32": "^4.0.0", "bip39": "^3.1.0", "bip65": "^1.0.1", "bip68": "^1.0.3", "bs58": "^4.0.0", "dhttp": "^3.0.0", "ecpair": "^2.0.1", "eslint": "^8.29.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "hoodwink": "^2.0.0", "jest": "^30.0.4", "minimaldata": "^1.0.2", "mocha": "^10.6.0", "nyc": "^15.1.0", "prettier": "^2.8.0", "proxyquire": "^2.0.1", "randombytes": "^2.1.0", "regtest-client": "0.2.0", "rimraf": "^2.6.3", "tiny-secp256k1": "^2.2.0", "ts-jest": "^29.4.0", "ts-node": "^8.3.0", "typedoc": "^0.25.1", "typescript": "^4.4.4"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}