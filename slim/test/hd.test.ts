import { generateMnemonic, mnemonicToSeed } from "../wallet/bip/bip";
import { createAddress, signTransaction, importAddress } from "../wallet";
import BigNumber from "bignumber.js";
const ethers = require("ethers");
const provider = new ethers.providers.JsonRpcProvider(
    "https://polygon-rpc.com"
);

describe("op", () => {
    //op充值
    // Test Contract Call (Activity Creation)
    test("op depositETH", async () => {
        const privateKey = "9a9ab54eb2fd25ff36182100b0e5e5e4cf94562db8d833966b11bf5694a09566";
        const wallet = new ethers.Wallet(privateKey);
        const from = wallet.address;

        // Create contract interface
        const activityInterface = new ethers.utils.Interface([
            "function depositETH(uint32, bytes)",
        ]);

        // Encode function call data
        const callData = activityInterface.encodeFunctionData("depositETH", [
            2100000,
            "0x"
        ]);
        //用provider构建 签名 参数
        const provider = new ethers.providers.JsonRpcProvider("https://ethereum-sepolia-rpc.publicnode.com");
        // Optimism L1 Bridge Sepolia 地址（官方地址）
        const bridgeAddress = "******************************************";
        const amount = "0.1";
        const decimal = 18;
        const nonce = await provider.getTransactionCount(from, "latest");
        const feeData = await provider.getFeeData();
        const estimate = provider.estimateGas({
            from,
            to: bridgeAddress,
            value: BigNumber(amount).times("1e18").toFixed(0),
            data: callData
        })

        // Sign transaction
        const signed = signTransaction({
            privateKey: privateKey,
            nonce: nonce,
            from: from,
            to: bridgeAddress,
            gasLimit: (await estimate).toNumber(),
            amount,
            gasPrice: 0,
            decimal,
            chainId: 11155111,
            tokenAddress: "0x00",
            callData: callData,
            maxPriorityFeePerGas: parseInt(feeData.maxPriorityFeePerGas!.toString()),
            maxFeePerGas: parseInt(feeData.maxFeePerGas!.toString())
        });

        console.log("📤 Broadcasting tx...");
        const tx = await provider.sendTransaction(signed);
        console.log("📨 Sent! Tx Hash:", tx.hash);

        const receipt = await tx.wait();
        console.log("✅ Transaction confirmed");
        console.log(receipt.status === 1 ? "✅ 成功" : "❌ 执行失败");
        console.log(receipt);
    });

    //op提现
    test("op withdrawETH", async () => {
        const privateKey = "9a9ab54eb2fd25ff36182100b0e5e5e4cf94562db8d833966b11bf5694a09566";
        const wallet = new ethers.Wallet(privateKey);
        const from = wallet.address;

        // Create contract interface
        const activityInterface = new ethers.utils.Interface([
            "function withdraw(address, uint256, uint32, bytes)",
        ]);

        // Encode function call data
        const callData = activityInterface.encodeFunctionData("withdraw", [
            '******************************************', ethers.utils.parseEther("0.01"), 1000000, "0x"
        ]);
        //用provider构建 签名 参数
        const provider = new ethers.providers.JsonRpcProvider("https://ethereum-sepolia-rpc.publicnode.com");
        // Optimism L2 Bridge Sepolia 地址（官方地址）
        const bridgeAddress = "******************************************";
        const amount = "0.01";
        const decimal = 18;
        const nonce = await provider.getTransactionCount(from, "latest");
        const feeData = await provider.getFeeData();

        // Sign transaction
        const signed = signTransaction({
            privateKey: privateKey,
            nonce: nonce,
            from: from,
            to: bridgeAddress,
            gasLimit: 1000000,
            amount,
            gasPrice: 0,
            decimal,
            chainId: 11155420,
            tokenAddress: "0x00",
            callData: callData,
            maxPriorityFeePerGas: parseInt(feeData.maxPriorityFeePerGas!.toString()),
            maxFeePerGas: parseInt(feeData.maxFeePerGas!.toString())
        });

        console.log("📤 Broadcasting tx...");
        const tx = await provider.sendTransaction(signed);
        console.log("📨 Sent! Tx Hash:", tx.hash);

        const receipt = await tx.wait();
        console.log("✅ Transaction confirmed");
        console.log(receipt.status === 1 ? "✅ 成功" : "❌ 执行失败");
        console.log(receipt);
    });
});