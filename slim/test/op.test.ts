import { generateMnemonic, mnemonicToSeed } from "../wallet/bip/bip";
import { createAddress, signTransaction, importAddress } from "../wallet";
const ethers = require("ethers");
const provider = new ethers.providers.JsonRpcProvider(
    "https://polygon-rpc.com"
);

describe("eth unit test case", () => {

    // Test op Transfer--------https://opt-sepolia.g.alchemy.com/v2/M98Abo5XZqswpBv4ppyXJ
    test("sign op tx", async () => {
        const privateKey = "cadf7450f8a7f15b5a3c9eb094b7de3fcef85465ee3de3d3a3bb687f31c79289";
        const wallet = new ethers.Wallet(privateKey, provider);
        // Sign eth transaction
        const rawHex = signTransaction({
            privateKey: privateKey,
            nonce: 121,
            from: "******************************************",
            to: "******************************************",
            gasLimit: 21000,
            maxFeePerGas: **********2,
            maxPriorityFeePerGas: **********,
            gasPrice: 0,
            amount: "0.000001",
            decimal: 18,
            chainId: 11155420,
            tokenAddress: "0x00",
            callData: "",
        });
        console.log(rawHex)
    })
});

