import { generateMnemonic, mnemonicToSeed } from "../wallet/bip/bip";
import { createAddress, signTransaction, importAddress } from "../wallet";
const ethers = require("ethers");
const provider = new ethers.providers.JsonRpcProvider(
    "https://polygon-rpc.com"
);

describe("eth unit test case", () => {
    // Test address creation
    /*
    slim:9a9ab54eb2fd25ff36182100b0e5e5e4cf94562db8d833966b11bf5694a09566
    {"privateKey":"0x9a9ab54eb2fd25ff36182100b0e5e5e4cf94562db8d833966b11bf5694a09566",
    "publicKey":"0x023ea3d918a6fa4891bb268f0af9d4bc10fee610803ef7d2b34ee4b73a96ec905e",
    "address":"******************************************"}
     */

    /*
        {"privateKey":"0xcadf7450f8a7f15b5a3c9eb094b7de3fcef85465ee3de3d3a3bb687f31c79289",
        "publicKey":"0x02ff81435f8a43a400d42e9aa73d1c723a7d70a995fc63c628efa22ee09e7fa15d",
        "address":"******************************************"}

     */
    test("createAddress", () => {
        //const mnemonic = generateMnemonic({ number: 12, language: "english" });
        const mnemonic = "debris ill smooth summer wet crunch flag bubble link jealous breeze alert"
        const params = {
            mnemonic: mnemonic,
            password: "",
        };
        const seed = mnemonicToSeed(params);
        const account = createAddress(seed.toString("hex"), "0");
        console.log(account);
    });


    // Test address import
    test("importAddress", () => {
        const account = importAddress("YOUR_PRIVATE_KEY");
        console.log(account);
    });

    // Test eth Transfer--------
    test("sign eth tx", async () => {
        const privateKey = "cadf7450f8a7f15b5a3c9eb094b7de3fcef85465ee3de3d3a3bb687f31c79289";
        const wallet = new ethers.Wallet(privateKey, provider);
        // Sign eth transaction
        const rawHex = signTransaction({
            privateKey: privateKey,
            nonce: 70,
            from: "******************************************",
            to: "******************************************",
            gasLimit: 21000,
            maxFeePerGas: ************,
            maxPriorityFeePerGas: ***********,
            gasPrice: 0,
            amount: "1",
            decimal: 18,
            chainId: ********,
            tokenAddress: "0x00",
            callData: "",
        });
        console.log(rawHex)
    })
});

