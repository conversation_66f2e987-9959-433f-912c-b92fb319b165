import { generateMnemonic, mnemonicToSeed } from "../wallet/bip/bip";
import {createAddress, signTransaction} from "../wallet/index";
const ethers = require("ethers");
const provider = new ethers.providers.JsonRpcProvider(
     "https://opt-sepolia.g.alchemy.com/v2/xxxx"
    // "https://sepolia.optimism.io"
);

const L1Provider = new ethers.providers.JsonRpcProvider(
     "https://eth-sepolia.g.alchemy.com/v2/xxxx"
);

describe("eth unit test case", () => {
    test("createAddress", () => {
        const mnemonic = generateMnemonic({ number: 12, language: "english" });
        const params = {
            mnemonic: mnemonic,
            password: "",
        };
        const seed = mnemonicToSeed(params);
        const account = createAddress(seed.toString("hex"), "0");
        console.log(account);
        // {"privateKey":"0xecefb0d085e279ad57d1023c2c15522d24c19260d86bdd9f34a746a72fe57201","publicKey":"0x02b5aa851148c27cad1e7490f011d7b3f8820437c44d16e89ed7ea61f78f27056e","address":"******************************************"}
    });


    test("test eth transfer", async () => {
        const privateKey = "ecefb0d085e279ad57d1023c2c15522d24c19260d86bdd9f34a746a72fe57201"
        const wallet = new ethers.Wallet(privateKey, provider);

        const nonce = await provider.getTransactionCount(wallet.address);
        const rawHex = signTransaction({
            privateKey: privateKey,
            nonce: Number(nonce),
            from: wallet.address,
            to: "******************************************",
            gasLimit: 100000,
            maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
            maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
            gasPrice: 0,
            amount: "0.01",
            decimal: 18,
            chainId: 11155420,
            tokenAddress: "0x00",
            callData: "",
        });
        console.log(rawHex)
    })

    test("sign USDC token transfer", async () => {
        const privateKey = "ecefb0d085e279ad57d1023c2c15522d24c19260d86bdd9f34a746a72fe57201"
        const wallet = new ethers.Wallet(privateKey, provider);
        const USDCAddress = "******************************************"
        const rawHex = signTransaction({
            privateKey: privateKey,
            nonce: Number(await provider.getTransactionCount(wallet.address)),
            from: wallet.address,
            to: "******************************************",
            gasLimit: 100000,
            maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
            maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
            gasPrice: 0,
            amount: "1",
            decimal: 6,
            chainId: 11155420,
            tokenAddress: USDCAddress,
            callData: "",
        });
        console.log(rawHex)
    })


    test("deposit to Optimism testnet", async ()=>{
        // Create contract interface
        const activityInterface = new ethers.utils.Interface([
            "function depositETH(uint32, bytes)"
        ]);

        const callData = activityInterface.encodeFunctionData("depositETH", [21000, "0x"]);

        const privateKey = "ecefb0d085e279ad57d1023c2c15522d24c19260d86bdd9f34a746a72fe57201"
        const opL1BridgeAddress = "******************************************"
        const wallet = new ethers.Wallet(privateKey, provider);

        const rawHex = signTransaction({
            privateKey: privateKey,
            nonce: Number(await L1Provider.getTransactionCount(wallet.address)),
            from: wallet.address,
            to: opL1BridgeAddress,
            gasLimit: 1000000,
            maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
            maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
            gasPrice: 0,
            amount: "0.1",
            decimal: 18,
            chainId: 11155111,
            tokenAddress: "0x00",
            callData: callData,
        });

        // Broadcast transaction
        const tx = await L1Provider.sendTransaction(rawHex);
        console.log("Transaction hash:", tx.hash);

        // Wait for confirmation
        const receipt = await tx.wait(1);
        console.log("Transaction confirmed in block:", receipt.blockNumber);
        console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
    }, 60000);


    test("withdraw from Optimism testnet", async ()=>{
        // Create contract interface
        const activityInterface = new ethers.utils.Interface([
            "function withdraw(address, uint256, uint32, bytes)",
        ]);

        const callData = activityInterface.encodeFunctionData("withdraw", ["******************************************", ethers.utils.parseEther("0.001"), 21000, "0x"]);

        const privateKey = "ecefb0d085e279ad57d1023c2c15522d24c19260d86bdd9f34a746a72fe57201"
        const opL2BridgeAddress = "******************************************"
        const wallet = new ethers.Wallet(privateKey, provider);

        const rawHex = signTransaction({
            privateKey: privateKey,
            nonce: Number(await provider.getTransactionCount(wallet.address)),
            from: wallet.address,
            to: opL2BridgeAddress,
            gasLimit: 1000000,
            maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
            maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
            gasPrice: 0,
            amount: "0.001",
            decimal: 18,
            chainId: 11155420,
            tokenAddress: "0x00",
            callData: callData,
        });

        // Broadcast transaction
        const tx = await provider.sendTransaction(rawHex);
        console.log("Transaction hash:", tx.hash);

        // Wait for confirmation
        const receipt = await tx.wait(1);
        console.log("Transaction confirmed in block:", receipt.blockNumber);
        console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
    }, 60000);

    test("get optimism sepolia challenge period", async () => {
        const OptimismPortalProxy = "******************************************"; // OptimismPortalProxy contract address
        const portalAbi = ["function proofMaturityDelaySeconds() view returns (uint256)"];
        const portal = new ethers.Contract(OptimismPortalProxy, portalAbi, L1Provider);

        
        const period = await portal.proofMaturityDelaySeconds();
        console.log(`Challenge Period: ${period} seconds`);
        
    })
})


    