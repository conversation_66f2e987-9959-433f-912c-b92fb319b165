## 1.ETH转账

* 1.1 代码

  ```typescript
  export function signTransaction(params: { privateKey: string; nonce: number; from: string; to: string; gasLimit: number; amount: string; gasPrice: number; decimal: number; chainId: any; tokenAddress: string; callData: string;  maxPriorityFeePerGas?: number; maxFeePerGas?: number; }) {
      let { privateKey, nonce, from, to, gasPrice, gasLimit, amount, tokenAddress, callData,  decimal, maxPriorityFeePerGas, maxFeePerGas, chainId } = params;
      const transactionNonce = numberToHex(nonce);
      const gasLimits = numberToHex(gasLimit);
      const chainIdHex = numberToHex(chainId);
      let newAmount = BigNumber(amount).times((BigNumber(10).pow(decimal)));
      const numBalanceHex = numberToHex(newAmount);
      let txData: any = {
          nonce: transactionNonce,
          gasLimit: gasLimits,
          to,
          from,
          chainId: chainIdHex,
          value: numBalanceHex
      }
      if (maxFeePerGas && maxPriorityFeePerGas) {
          txData.maxFeePerGas = numberToHex(maxFeePerGas);
          txData.maxPriorityFeePerGas = numberToHex(maxPriorityFeePerGas);
      } else {
          txData.gasPrice = numberToHex(gasPrice);
      }
      if (tokenAddress && tokenAddress !== "0x00") {
          const ABI = ["function transfer(address to, uint amount)"];
          const iface = new Interface(ABI);
          if (params.callData) {
              txData.data = callData;
              txData.value = "0x0";
          } else {
              txData.data = iface.encodeFunctionData("transfer", [to, numBalanceHex]);
              txData.to = tokenAddress;
          }
          txData.value = "0x0";
      }
      let common: any, tx: any;
      if (txData.maxFeePerGas && txData.maxPriorityFeePerGas) {
          common = (Common as any).custom({
              chainId: chainId,
              defaultHardfork: "london"
          });
          tx = FeeMarketEIP1559Transaction.fromTxData(txData, {
              common
          });
      } else {
          common = (Common as any).custom({ chainId: chainId })
          tx = Transaction.fromTxData(txData, {
              common
          });
      }
      const privateKeyBuffer = Buffer.from(privateKey, "hex");
      const signedTx = tx.sign(privateKeyBuffer);
      const serializedTx = signedTx.serialize();
      if (!serializedTx) {
          throw new Error("sign is null or undefined");
      }
      return `0x${serializedTx.toString('hex')}`;
  }
  ```
* 1.2 获取签名数据

  ```typescript
  test("test eth transfer", async () => {
          const privateKey = "ecefb0d085e279ad57d1023c2c15522d24c19260d86bdd9f34a746a72fe57201"
          const wallet = new ethers.Wallet(privateKey, provider);

          const nonce = await provider.getTransactionCount(wallet.address);
          const rawHex = signTransaction({
              privateKey: privateKey,
              nonce: Number(nonce),
              from: wallet.address,
              to: "******************************************",
              gasLimit: 100000,
              maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
              maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
              gasPrice: 0,
              amount: "0.01",
              decimal: 18,
              chainId: 11155420,
              tokenAddress: "0x00",
              callData: "",
          });
          console.log(rawHex)
   })
  ```

  打印数据为：**0x02f87783aa37dc808506fc23ac0085174876e800830186a094b67c931903092c715802e49046c5b3419bc29c7e872386f26fc1000080c080a0c52fb3fccb05b44291428302a7afe4992ae669c10d94a7ac362c2e814a194bf7a068d16101ee3629ce21df58d29127c2128294943123ac65e9098ddcea79457874**
* 1.3 发送交易

  ```shell
  curl --location 'https://sepolia.optimism.io' \
  --header 'Content-Type: application/json' \
  --header 'Cookie: lb1=edge-proxyd-proxyd-quicknode-http' \
  --data '{
  	"jsonrpc":"2.0",
  	"method":"eth_sendRawTransaction",
  	"params":["0x02f87783aa37dc808506fc23ac0085174876e800830186a094b67c931903092c715802e49046c5b3419bc29c7e872386f26fc1000080c080a0c52fb3fccb05b44291428302a7afe4992ae669c10d94a7ac362c2e814a194bf7a068d16101ee3629ce21df58d29127c2128294943123ac65e9098ddcea79457874"],
  	"id":1
  }'
  ```
* 1.4 结果

![1751694667014](image/1751694667014.png)

## 2.Token转账

* 2.1 获取签名数据

  ```typescript
  test("sign USDC token transfer", async () => {
          const privateKey = "ecefb0d085e279ad57d1023c2c15522d24c19260d86bdd9f34a746a72fe57201"
          const wallet = new ethers.Wallet(privateKey, provider);
          const USDCAddress = "******************************************"
          const rawHex = signTransaction({
              privateKey: privateKey,
              nonce: Number(await provider.getTransactionCount(wallet.address)),
              from: wallet.address,
              to: "******************************************",
              gasLimit: 500000,
              maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
              maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
              gasPrice: 0,
              amount: "1",
              decimal: 6,
              chainId: 11155420,
              tokenAddress: USDCAddress,
              callData: "",
          });
          console.log(rawHex)
      })
  ```

  打印数据为：**0x02f8b583aa37dc018506fc23ac0085174876e800830186a0945fd84259d66cd46123540766be93dfe6d43130d780b844a9059cbb000000000000000000000000b67c931903092c715802e49046c5b3419bc29c7e00000000000000000000000000000000000000000000000000000000000f4240c080a01ebae749533aa2efe4cdb026009853b8f74eeadb365d91f95ee1cd520e53445ba016938f2ded9d07e3954f7edb63cd0a7a12fa6a3eb002f4a5cd2ed4f1e07d72f1**
* 2.2 发送交易

  ```shell
  curl --location 'https://opt-sepolia.g.alchemy.com/v2/********************************' \
  --header 'Content-Type: application/json' \
  --header 'Cookie: _cfuvid=WNM7yvf.b4iRuLHqINySAdWee3hgSpOaovKQUTMPv5Y-1751696473651-*******-604800000' \
  --data '{
  	"jsonrpc":"2.0",
  	"method":"eth_sendRawTransaction",
  	"params":["0x02f8b583aa37dc018506fc23ac0085174876e800830186a0945fd84259d66cd46123540766be93dfe6d43130d780b844a9059cbb000000000000000000000000b67c931903092c715802e49046c5b3419bc29c7e00000000000000000000000000000000000000000000000000000000000f4240c080a01ebae749533aa2efe4cdb026009853b8f74eeadb365d91f95ee1cd520e53445ba016938f2ded9d07e3954f7edb63cd0a7a12fa6a3eb002f4a5cd2ed4f1e07d72f1"],
  	"id":1
  }'
  ```
* 2.3 结果

![1751696593069](image/1751696593069.png)

## 3.RPC扫链接口

#### 1.发送交易

* request
  ```shell
  curl --location 'https://sepolia.optimism.io' \
  --header 'Content-Type: application/json' \
  --header 'Cookie: lb1=edge-proxyd-proxyd-quicknode-http' \
  --data '{
  	"jsonrpc":"2.0",
  	"method":"eth_sendRawTransaction",
  	"params":["0x02f87783aa37dc808506fc23ac0085174876e800830186a094b67c931903092c715802e49046c5b3419bc29c7e872386f26fc1000080c080a0c52fb3fccb05b44291428302a7afe4992ae669c10d94a7ac362c2e814a194bf7a068d16101ee3629ce21df58d29127c2128294943123ac65e9098ddcea79457874"],
  	"id":1
  }'
  ```
* response
  ```shell
  {
      "jsonrpc": "2.0",
      "result": "0x6f322736321d34f64b3d2b0ff20042c10c1b69f5b1d25a4cbe59272d82b97162",
      "id": 1
  }
  ```

#### 2.根据hash获取交易详情

* request
  ```shell
  curl --location 'https://sepolia.optimism.io' \
  --header 'Content-Type: application/json' \
  --header 'Cookie: lb1=edge-proxyd-proxyd-quicknode-http' \
  --data '{
  	"jsonrpc":"2.0",
  	"method":"eth_getTransactionByHash",
  	"params":[
  		"0x6f322736321d34f64b3d2b0ff20042c10c1b69f5b1d25a4cbe59272d82b97162"
  	],
  	"id":1
  }'
  ```
* response
  ```shell
  {
      "jsonrpc": "2.0",
      "result": {
          "accessList": [],
          "blockHash": "0x62632aa6278df363705e9f7bb440042a8dfd8937309f0d040de46b6cfe945fdb",
          "blockNumber": "0x1c8ef21",
          "chainId": "0xaa37dc",
          "from": "******************************************",
          "gas": "0x186a0",
          "gasPrice": "0x6fc23acfa",
          "hash": "0x6f322736321d34f64b3d2b0ff20042c10c1b69f5b1d25a4cbe59272d82b97162",
          "input": "0x",
          "maxFeePerGas": "0x174876e800",
          "maxPriorityFeePerGas": "0x6fc23ac00",
          "nonce": "0x0",
          "r": "0xc52fb3fccb05b44291428302a7afe4992ae669c10d94a7ac362c2e814a194bf7",
          "s": "0x68d16101ee3629ce21df58d29127c2128294943123ac65e9098ddcea79457874",
          "to": "******************************************",
          "transactionIndex": "0x1",
          "type": "0x2",
          "v": "0x0",
          "value": "0x2386f26fc10000",
          "yParity": "0x0"
      },
      "id": 1
  }
  ```

#### 3.获取交易回执

* request
  ```shell
  curl --location 'https://sepolia.optimism.io' \
  --header 'Content-Type: application/json' \
  --header 'Cookie: lb1=edge-proxyd-proxyd-quicknode-http' \
  --data '{
  	"jsonrpc":"2.0",
  	"method":"eth_getTransactionReceipt",
  	"params":[
  		"0x6f322736321d34f64b3d2b0ff20042c10c1b69f5b1d25a4cbe59272d82b97162"
  	],
  	"id":1
  }'
  ```
* response
  ```shell
  {
      "jsonrpc": "2.0",
      "result": {
          "blockHash": "0x62632aa6278df363705e9f7bb440042a8dfd8937309f0d040de46b6cfe945fdb",
          "blockNumber": "0x1c8ef21",
          "contractAddress": null,
          "cumulativeGasUsed": "0x10648",
          "effectiveGasPrice": "0x6fc23acfa",
          "from": "******************************************",
          "gasUsed": "0x5208",
          "l1BaseFeeScalar": "0x1db0",
          "l1BlobBaseFee": "0x1",
          "l1BlobBaseFeeScalar": "0xd2730",
          "l1Fee": "0x5e6d8572e",
          "l1GasPrice": "0x7c3f43c6",
          "l1GasUsed": "0x640",
          "logs": [],
          "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000",
          "status": "0x1",
          "to": "******************************************",
          "transactionHash": "0x6f322736321d34f64b3d2b0ff20042c10c1b69f5b1d25a4cbe59272d82b97162",
          "transactionIndex": "0x1",
          "type": "0x2"
      },
      "id": 1
  }
  ```

#### 4.获取块高

* request
  ```sehll
  curl --location 'https://opt-sepolia.g.alchemy.com/v2/********************************' \
  --header 'Content-Type: application/json' \
  --header 'Cookie: _cfuvid=s_nXy9Nvo3FYBmK6hzZ9QABSlNjfD87JX0i3B2HxIys-1751699907807-*******-604800000' \
  --data '{
  	"jsonrpc":"2.0",
  	"method":"eth_blockNumber",
  	"params":[],
  	"id":83
  }'
  ```
* response

```shell
{
    "jsonrpc": "2.0",
    "id": 83,
    "result": "0x1c8fb0b"
}
```

#### 5.根据块高获取区块信息

* request
  ```sehll
  curl --location 'https://opt-sepolia.g.alchemy.com/v2/********************************' \
  --header 'Content-Type: application/json' \
  --header 'Cookie: _cfuvid=s_nXy9Nvo3FYBmK6hzZ9QABSlNjfD87JX0i3B2HxIys-1751699907807-*******-604800000' \
  --data '{
  	"jsonrpc":"2.0",
  	"method":"eth_getBlockByNumber",
  	"params":[
  		"0x1c8fb0b", 
  		true
  	],
  	"id":1
  }'
  ```
* response

```shell
{
    "jsonrpc": "2.0",
    "id": 1,
    "result": {
        "baseFeePerGas": "0xfa",
        "blobGasUsed": "0x0",
        "difficulty": "0x0",
        "excessBlobGas": "0x0",
        "extraData": "0x00000000fa00000002",
        "gasLimit": "0x2625a00",
        "gasUsed": "0x10654",
        "hash": "0x61788345276748e24d902db01f6409385b509289451c15bedc58b60fe15823a1",
        "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000",
        "miner": "0x4200000000000000000000000000000000000011",
        "mixHash": "0x79727a62beb574870f9f1a9b039d0c5abeba6477d5547e7e066a758c401877bd",
        "nonce": "0x0000000000000000",
        "number": "0x1c8fb0b",
        "parentBeaconBlockRoot": "0xade4853c3a09cca55bcbf9c8045b070a537281f5c5e9db55236ba9e23864f809",
        "parentHash": "0x64250e85956563564bc897070cc47528565815ad2038723f1aaae440727c67ed",
        "receiptsRoot": "0xa76c970def2088c99eb6ce001addaaa89a55f58f35c6020b5fe3117d2287283a",
        "requestsHash": "0xe3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
        "sha3Uncles": "0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347",
        "size": "0x3f3",
        "stateRoot": "0xadab7a6871b6b5ffb9197768d1506e027a9a4a68db366bfa38d0df6e8b349711",
        "timestamp": "0x6868d1c2",
        "transactions": [
            {
                "blockHash": "0x61788345276748e24d902db01f6409385b509289451c15bedc58b60fe15823a1",
                "blockNumber": "0x1c8fb0b",
                "from": "0xdeaddeaddeaddeaddeaddeaddeaddeaddead0001",
                "gas": "0xf4240",
                "gasPrice": "0x0",
                "hash": "0xab66fea732de90d305f07f7cc79b7e1c49618719cbb1549a01fff2d2b44b82f0",
                "input": "0x098999be00001db0000d27300000000000000009000000006868d16c000000000084b10e0000000000000000000000000000000000000000000000000000000151516bff0000000000000000000000000000000000000000000000000000000000000001dfd796c05b7bb13c5e403839c6c394a4194856a4128ae083332b5b0fe33476bf0000000000000000000000008f23bb38f531600e5d8fddaaec41f13fab46e98c000000000000000000000000",
                "nonce": "0x1c8fb0d",
                "to": "0x4200000000000000000000000000000000000015",
                "transactionIndex": "0x0",
                "value": "0x0",
                "type": "0x7e",
                "v": "0x0",
                "r": "0x0",
                "s": "0x0",
                "sourceHash": "0x213b9b7ac150c2d38a41b93ac52aadb0312ae4f3d37743eb7c44de90fb222050",
                "mint": "0x0",
                "depositReceiptVersion": "0x1"
            },
            {
                "blockHash": "0x61788345276748e24d902db01f6409385b509289451c15bedc58b60fe15823a1",
                "blockNumber": "0x1c8fb0b",
                "from": "0x482d7e3f7406d354cfc1eb93359779fd98d88f14",
                "gas": "0x5208",
                "gasPrice": "0xf433a",
                "hash": "0xa3a51fee1ded604711a804c74bd9acc5c34483c21ae1a9e88ddf339ab17a0c41",
                "input": "0x",
                "nonce": "0x322b",
                "to": "0x482d7e3f7406d354cfc1eb93359779fd98d88f14",
                "transactionIndex": "0x1",
                "value": "0x38d7ea4c68000",
                "type": "0x0",
                "chainId": "0xaa37dc",
                "v": "0x1546fdc",
                "r": "0x32cf462aeca0679e6faae11de7e7cfffac314e19ed1cdae3015fe4b3f9a5a0e4",
                "s": "0xe103b38501b619cae42b0b3f1e7c31d671182893212e4586752c8f8cb2416e4"
            }
        ],
        "transactionsRoot": "0xb60032e7ecaa9d0fb224f645b963aae98dfacb52518e3167ca80e5af03288869",
        "uncles": [],
        "withdrawals": [],
        "withdrawalsRoot": "0xfe23533f5c3eae1aa3f8bcf6d0d57a6be433545a450b8fa2822b9c5b4661ec05"
    }
}
```

## 4.OP测试网充值

* 代码

```typescript
test("deposit to Optimism testnet", async ()=>{
        // Create contract interface
        const activityInterface = new ethers.utils.Interface([
            "function depositETH(uint32, bytes)",
        ]);

        const callData = activityInterface.encodeFunctionData("depositETH", [21000, "0x"]);

        const privateKey = "ecefb0d085e279ad57d1023c2c15522d24c19260d86bdd9f34a746a72fe57201"
        const opL1BridgeAddress = "******************************************"
        const wallet = new ethers.Wallet(privateKey, provider);

        const rawHex = signTransaction({
            privateKey: privateKey,
            nonce: Number(await provider.getTransactionCount(wallet.address)),
            from: wallet.address,
            to: opL1BridgeAddress,
            gasLimit: 100000,
            maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
            maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
            gasPrice: 0,
            amount: "0.01",
            decimal: 18,
            chainId: 11155420,
            tokenAddress: "0x00",
            callData: callData,
        });

        // Broadcast transaction
        const tx = await provider.sendTransaction(rawHex);
        console.log("Transaction hash:", tx.hash);

        // Wait for confirmation
        const receipt = await tx.wait(1);
        console.log("Transaction confirmed in block:", receipt.blockNumber);
        console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
    }, 60000);
```

* 结果:  https://sepolia.etherscan.io/tx/0xffeac111975a8633e2bf18775b2e023d2da95c379d0ce1be41c7049d44158e3e

  ![1751716571721](image/1751716571721.png)

## 5.OP测试网提现

* 代码

  ```typescript
  test("withdraw from Optimism testnet", async ()=>{
          // Create contract interface
          const activityInterface = new ethers.utils.Interface([
              "function withdraw(address, uint256, uint32, bytes)",
          ]);

          const callData = activityInterface.encodeFunctionData("withdraw", ["******************************************", ethers.utils.parseEther("0.001"), 21000, "0x"]);

          const privateKey = "ecefb0d085e279ad57d1023c2c15522d24c19260d86bdd9f34a746a72fe57201"
          const opL2BridgeAddress = "******************************************"
          const wallet = new ethers.Wallet(privateKey, provider);

          const rawHex = signTransaction({
              privateKey: privateKey,
              nonce: Number(await provider.getTransactionCount(wallet.address)),
              from: wallet.address,
              to: opL2BridgeAddress,
              gasLimit: 1000000,
              maxFeePerGas: Number(ethers.utils.parseUnits("100", "gwei")),
              maxPriorityFeePerGas: Number(ethers.utils.parseUnits("30", "gwei")),
              gasPrice: 0,
              amount: "0.001",
              decimal: 18,
              chainId: 11155420,
              tokenAddress: "0x00",
              callData: callData,
          });

          // Broadcast transaction
          const tx = await provider.sendTransaction(rawHex);
          console.log("Transaction hash:", tx.hash);

          // Wait for confirmation
          const receipt = await tx.wait(1);
          console.log("Transaction confirmed in block:", receipt.blockNumber);
          console.log("Transaction status:", receipt.status === 1 ? "Success" : "Failed");
      }, 60000);
  ```
* 结果： https://sepolia-optimism.etherscan.io/tx/0xbea51b3c354c233212c212b6dc338265d51f7912d4c3e82952be563a28e65b0e

  ![1751718281707](image/1751718281707.png)

## 6.获取测试网挑战期

* 在 [Contract Addresses](https://docs.optimism.io/superchain/addresses) 页面中找到sepolia测试网中 `OptimismPortalProxy` 合约的部署地址 `******************************************`， 调用合约中的 `proofMaturityDelaySeconds` 函数获取。

![1751726254273](image/1751726254273.png)

* 代码

  ```typescript
  test("get optimism sepolia challenge period", async () => {
          const OptimismPortalProxy = "******************************************"; // OptimismPortalProxy contract address
          const portalAbi = ["function proofMaturityDelaySeconds() view returns (uint256)"];
          const portal = new ethers.Contract(OptimismPortalProxy, portalAbi, L1Provider);


          const period = await portal.proofMaturityDelaySeconds();
          console.log(`Challenge Period: ${period} seconds`);

      })
  ```
* 结果为604800秒，即7天
